"""
增强版基金交易系统 V3
集成六大维度评估体系的完整自动化交易系统
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List
import time
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from coordinators import MultiAgentCoordinatorV3, FundTradingExecutorV3, TradingAgent
from agents.traditional import TechnicalAgent, GuaAnalysisAgent, FundFlowAgent
from agents.enhanced import EnhancedTechnicalAgent, SorosReflexivityAgent
from core.utils import *


class EnhancedFundTradingSystemV3:
    """
    @class EnhancedFundTradingSystemV3
    @brief 增强版基金交易多智能体系统V3
    @details 集成六大维度评估体系的完整自动化交易系统
    """
    
    def __init__(self, title: str = ""):
        # 配置日志
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化所有代理
        self.technical_agent = TechnicalAgent()
        self.enhanced_technical_agent = EnhancedTechnicalAgent()
        self.gua_agent = GuaAnalysisAgent()
        self.fund_flow_agent = FundFlowAgent()
        self.soros_agent = SorosReflexivityAgent()
        self.trading_agent = TradingAgent(title=title)
        
        # 初始化V3新增组件
        self.coordinator = MultiAgentCoordinatorV3()
        self.executor = FundTradingExecutorV3()

        # 交易基金列表
        self.buy_fund_list = [
            '513030', '513080', '513500', '513520', '513300',
            '513850', '159329', '159561', '520830', '159567',
            '518880', '601398'
        ]

        # 初始化基金特性分析器
        from optimizers.fund_analyzer import FundCharacteristicsAnalyzer
        self.fund_analyzer = FundCharacteristicsAnalyzer()

        # 自动同步基金分类
        self._sync_fund_categories()
        
        # 新闻和指数检查时间段
        self.news_index_time = [
            ['09:27:00', '09:32:00'], ['10:00:00', '10:06:00'], 
            ['10:30:00', '10:36:00'], ['11:00:00', '11:06:00'],
            ['13:00:00', '13:06:00'], ['13:30:00', '13:36:00'], 
            ['14:00:00', '14:06:00'], ['14:30:00', '14:36:00']
        ]
        
        # 并行处理配置
        self.max_workers = 4
        self.timeout = 30
        
        self.logger.info("Enhanced Fund Trading System V3 initialized")
        self.logger.info("🔄 V3版本特性: 六大维度评估 + 智能冲突解决 + 动态权重管理")

    def _sync_fund_categories(self):
        """同步基金分类"""
        try:
            # 尝试从配置文件加载分类
            self.fund_analyzer.load_categories_from_config()

            # 与当前基金列表同步
            new_classifications = self.fund_analyzer.sync_with_fund_list(self.buy_fund_list)

            if new_classifications:
                self.logger.info(f"新分类的基金: {new_classifications}")
                # 保存更新后的分类到配置文件
                self.fund_analyzer.save_categories_to_config()

            # 显示分类统计
            stats = self.fund_analyzer.get_category_stats()
            self.logger.info(f"基金分类统计: {stats}")

        except Exception as e:
            self.logger.warning(f"基金分类同步失败: {e}")

    def get_fund_category_info(self, fund_code: str) -> Dict[str, Any]:
        """获取基金分类信息"""
        try:
            category = self.fund_analyzer.get_fund_category(fund_code)
            correlation_score = self.fund_analyzer.calculate_correlation_score(fund_code)
            category_adjustment = self.fund_analyzer._get_category_adjustment(category)

            return {
                'category': category,
                'correlation_score': correlation_score,
                'category_adjustment': category_adjustment
            }
        except Exception as e:
            self.logger.error(f"获取基金分类信息失败 {fund_code}: {e}")
            return {
                'category': 'unknown',
                'correlation_score': 0.5,
                'category_adjustment': {'sensitivity': 0.0, 'smoothness': 0.0}
            }

        
    def analyze_fund_v3(self, fund_code: str) -> Dict[str, Any]:
        """V3版本的增强基金分析"""
        self.logger.info(f"Starting V3 enhanced analysis for fund {fund_code}")
        
        # 使用V3协调器进行分析
        analysis_result = self.coordinator.coordinate_analysis(fund_code)
        
        # 获取账户信息
        account_data = self.trading_agent.get_account_info()
        analysis_result['account_data'] = account_data
        
        return analysis_result
    
    def execute_trading_decision_v3(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """V3版本的交易决策执行"""
        fund_code = analysis_result.get('fund_code')
        enhanced_decision = analysis_result.get('enhanced_decision', {})

        # 获取决策信息 - 优先使用风控后的最终决策
        final_decision = analysis_result.get('final_decision', 'hold')  # 使用风控后的决策
        final_confidence = analysis_result.get('final_confidence', 0.0)  # 使用风控后的置信度

        # 如果没有风控决策，则使用原始决策
        if final_decision == 'hold' and 'final_decision' not in analysis_result:
            final_decision = enhanced_decision.get('decision', 'hold')
            final_confidence = enhanced_decision.get('confidence', 0.0)

        self.logger.info(f"执行基金 {fund_code} 决策: {final_decision} (置信度: {final_confidence:.3f})")
        
        if final_decision in ['buy', 'sell'] and final_confidence > 0.5:
            # 使用V3执行器进行交易
            execution_result = self.executor.execute_decision(analysis_result)
            
            # 如果V3执行器支持，同时使用真实交易代理
            if execution_result.get('execution_status') == 'executed':
                # 准备交易数据
                trade_data = {
                    'action': final_decision,
                    'fund_code': fund_code,
                    'price': execution_result.get('price', 1.0),
                    'quantity': execution_result.get('shares', '20000')
                }
                
                # 执行真实交易
                trade_result = self.trading_agent.process(trade_data)
                execution_result['real_trade_result'] = trade_result
                
                self.logger.info(f"Real trade executed for {fund_code}: {trade_result}")
            
            return execution_result
        else:
            return {
                'fund_code': fund_code,
                'action': 'hold',
                'reason': f"Decision: {final_decision}, Confidence: {final_confidence:.2f} (threshold: 0.5)",
                'timestamp': datetime.now().isoformat()
            }
    
    def run_trading_cycle_v3(self) -> List[Dict[str, Any]]:
        """运行V3版本的单次交易周期"""
        cycle_start_time = datetime.now()
        results = []
        
        # 测试基金列表
        fund_codes = self.buy_fund_list
        
        print(f"\n🔄 V3 交易周期开始 - {len(fund_codes)} 只基金 [{cycle_start_time.strftime('%H:%M:%S')}]")
        
        for fund_code in fund_codes:
            try:
                # 执行V3分析
                analysis_result = self.analyze_fund_v3(fund_code)
                
                if 'error' not in analysis_result:
                    # 展示详细分析结果
                    self.display_detailed_analysis_result(analysis_result)
                    
                    # 执行交易决策
                    execution_result = self.execute_trading_decision_v3(analysis_result)
                    result = {
                        'fund_code': fund_code,
                        'analysis_result': analysis_result,
                        'execution_result': execution_result
                    }
                else:
                    print(f"\n❌ 基金 {fund_code} 分析失败: {analysis_result['error']}")
                    result = {
                        'fund_code': fund_code,
                        'error': analysis_result['error']
                    }
                    
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Trading cycle error for {fund_code}: {e}")
                results.append({
                    'fund_code': fund_code,
                    'error': str(e)
                })
        
        # 周期结束摘要
        cycle_end_time = datetime.now()
        cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
        
        # 统计结果
        successful_analysis = len([r for r in results if 'error' not in r])
        failed_analysis = len([r for r in results if 'error' in r])
        
        # 统计决策类型 - 使用风控后的最终决策
        original_decisions = {}
        final_decisions = {}

        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})

                # 原始决策统计
                enhanced_decision = analysis_result.get('enhanced_decision', {})
                original_decision = enhanced_decision.get('decision', 'hold')
                original_decisions[original_decision] = original_decisions.get(original_decision, 0) + 1

                # 最终决策统计（风控后）
                final_decision = analysis_result.get('final_decision', original_decision)
                final_decisions[final_decision] = final_decisions.get(final_decision, 0) + 1
        
        # 简化输出 - 显示风控后的最终决策分布
        final_decision_summary = " | ".join([f"{k.upper()}:{v}" for k, v in final_decisions.items()]) if final_decisions else "无决策"

        # 检查是否有风控干预
        risk_interventions = 0
        for result in results:
            if 'error' not in result:
                analysis_result = result.get('analysis_result', {})
                original = analysis_result.get('enhanced_decision', {}).get('decision', 'hold')
                final = analysis_result.get('final_decision', original)
                if original != final:
                    risk_interventions += 1

        # 显示统计信息
        if risk_interventions > 0:
            print(f"🏁 周期完成: 耗时{cycle_duration:.1f}s | 成功{successful_analysis}/{len(fund_codes)} | 最终决策分布: {final_decision_summary} | 🛡️风控干预:{risk_interventions}次\n")
        else:
            print(f"🏁 周期完成: 耗时{cycle_duration:.1f}s | 成功{successful_analysis}/{len(fund_codes)} | 决策分布: {final_decision_summary}\n")
        
        return results

    def display_detailed_analysis_result(self, analysis_result: Dict[str, Any]) -> None:
        """展示精简的分析结果"""
        try:
            fund_code = analysis_result.get('fund_code', 'UNKNOWN')
            enhanced_decision = analysis_result.get('enhanced_decision', {})

            # 核心决策结果
            original_decision = enhanced_decision.get('decision', 'hold')
            original_confidence = enhanced_decision.get('confidence', 0)
            weighted_score = enhanced_decision.get('weighted_score', 0)

            # 风控后的最终决策
            final_decision = analysis_result.get('final_decision', original_decision)
            final_confidence = analysis_result.get('final_confidence', original_confidence)

            # 检查是否被风控修改
            risk_control_changed = (original_decision != final_decision)

            # 显示原始决策
            decision_icon = "🟢" if original_decision == "buy" else ("🔴" if original_decision == "sell" else "🟡")
            print(f"\n{decision_icon} {fund_code}: {original_decision.upper()} | 置信度:{original_confidence:.2f} | 评分:{weighted_score:.3f}")

            # 如果风控修改了决策，显示风控信息
            if risk_control_changed:
                risk_control = analysis_result.get('risk_control', {})
                risk_level = risk_control.get('risk_level', 'unknown')

                # 获取风控违规详情
                risk_validation = risk_control.get('risk_validation_result', {}).get('risk_validation', {})
                violations = risk_validation.get('technical_violations', {})

                # 生成风控说明
                risk_icon = "🛡️"
                risk_reasons = []

                if violations:
                    for indicator, violation in violations.items():
                        if indicator == 'bollinger_bands':
                            risk_reasons.append("布林线位置过高")
                        elif indicator == 'rsi':
                            risk_reasons.append("RSI超买")
                        elif indicator == 'volume':
                            risk_reasons.append("成交量不足")
                        else:
                            risk_reasons.append(f"{indicator}违规")

                if not risk_reasons:
                    risk_reasons.append("技术条件不符合")

                risk_summary = ", ".join(risk_reasons[:3])  # 最多显示3个原因

                print(f"   {risk_icon} 风控阻止: {original_decision.upper()} → {final_decision.upper()} | 原因: {risk_summary} | 风险等级: {risk_level}")
                print(f"   📊 最终执行: {final_decision.upper()} (风控后置信度: {final_confidence:.2f})")

            # 显示市场分类
            market_classification = enhanced_decision.get('market_classification', {})
            classification = market_classification.get('primary_classification', '未知')
            print(f"   📊 市场分类: {classification}")

            # 显示LLM分析结果
            llm_analysis = analysis_result.get('llm_analysis', {})
            if llm_analysis and 'error' not in llm_analysis:
                market_sentiment = llm_analysis.get('market_sentiment', '未知')
                llm_confidence = llm_analysis.get('confidence_level', 0)
                strategy_suggestion = llm_analysis.get('strategy_suggestion', '')
                
                # 显示基本LLM信息
                print(f"   🤖 LLM分析: 情绪={market_sentiment} | 置信度={llm_confidence:.2f}")
                
                # 显示完整的策略建议（如果有的话）
                if strategy_suggestion:
                    # 将长文本分行显示，每行最多80个字符
                    suggestion_lines = self._wrap_text(strategy_suggestion, 80)
                    for i, line in enumerate(suggestion_lines):
                        if i == 0:
                            print(f"   💡 策略建议: {line}")
                        else:
                            print(f"             {line}")
                
                # 显示市场驱动因素（如果有的话）
                market_drivers = llm_analysis.get('market_drivers', [])
                if market_drivers:
                    print(f"   📈 市场驱动:")
                    for driver in market_drivers[:3]:  # 显示前3个驱动因素
                        driver_lines = self._wrap_text(driver, 70)
                        for j, line in enumerate(driver_lines):
                            if j == 0:
                                print(f"     • {line}")
                            else:
                                print(f"       {line}")
                
                # 显示风险点（如果有的话）
                risk_points = llm_analysis.get('risk_points', [])
                if risk_points:
                    print(f"   ⚠️ 风险提示:")
                    for risk in risk_points[:2]:  # 显示前2个风险点
                        risk_lines = self._wrap_text(risk, 70)
                        for k, line in enumerate(risk_lines):
                            if k == 0:
                                print(f"     • {line}")
                            else:
                                print(f"       {line}")
                    
            elif llm_analysis and 'error' in llm_analysis:
                print(f"   🤖 LLM分析: 失败 - {llm_analysis.get('error', '未知错误')[:50]}...")

            # 显示维度评估摘要
            dimension_evaluations = enhanced_decision.get('dimension_evaluations', {})
            if dimension_evaluations:
                dim_summary = []
                for dim_name, dim_data in dimension_evaluations.items():
                    score = dim_data.get('score', 0)
                    state = dim_data.get('state', 'unknown')
                    dim_summary.append(f"{dim_name}:{score:.2f}")

                print(f"   🎯 维度评估: {' | '.join(dim_summary[:3])}")  # 只显示前3个

        except Exception as e:
            print(f"❌ {fund_code} 分析展示失败: {str(e)}")

    def _wrap_text(self, text: str, width: int) -> List[str]:
        """将长文本按指定宽度分行"""
        if not text:
            return []
        
        lines = []
        words = text.split()
        current_line = ""
        
        for word in words:
            # 检查添加这个词后是否会超过宽度
            if len(current_line + " " + word) <= width:
                if current_line:
                    current_line += " " + word
                else:
                    current_line = word
            else:
                # 如果当前行不为空，保存它并开始新行
                if current_line:
                    lines.append(current_line)
                    current_line = word
                else:
                    # 如果单个词就超过宽度，直接添加
                    lines.append(word)
        
        # 添加最后一行
        if current_line:
            lines.append(current_line)
        
        return lines

    def run_trading_system_v3(self) -> None:
        """V3版本的自动化交易系统主循环"""
        self.logger.info("Starting V3 Enhanced Fund Trading System")

        try:
            # 主交易循环
            while ('06:05:00' <= datetime.now().strftime('%H:%M:%S') <= '11:27:00') or \
                  ('12:50:00' <= datetime.now().strftime('%H:%M:%S') <= '23:55:00'):

                # 运行V3交易周期
                results = self.run_trading_cycle_v3()

                # 休息间隔
                time.sleep(15)

        except KeyboardInterrupt:
            self.logger.info("V3 Trading system interrupted by user")
        except Exception as e:
            self.logger.error(f"V3 Trading system error: {e}")
        finally:
            self.logger.info("V3 Enhanced trading system stopped")

    def diagnose_system_status_v3(self, fund_code: str = '513500') -> Dict[str, Any]:
        """V3版本的系统诊断"""
        self.logger.info(f"🔧 V3系统诊断开始 - 基金: {fund_code}")

        diagnosis = {
            'fund_code': fund_code,
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'issues_found': [],
            'recommendations': [],
            'component_status': {},
            'v3_features_status': {}
        }

        try:
            # 1. 测试V3增强分析
            self.logger.info("🔍 测试V3六大维度分析...")
            analysis_result = self.analyze_fund_v3(fund_code)
            if 'error' in analysis_result:
                diagnosis['issues_found'].append("V3 enhanced analysis failed")
                diagnosis['component_status']['v3_analysis'] = 'failed'
            else:
                diagnosis['component_status']['v3_analysis'] = 'working'

            # 2. 测试传统代理状态
            self.logger.info("🔍 测试传统代理状态...")
            for agent_name, agent in [
                ('technical', self.technical_agent),
                ('enhanced_technical', self.enhanced_technical_agent),
                ('gua', self.gua_agent),
                ('flow', self.fund_flow_agent),
                ('soros', self.soros_agent)
            ]:
                try:
                    result = agent.process({'fund_code': fund_code})
                    if 'error' in result:
                        diagnosis['component_status'][agent_name] = 'error'
                        diagnosis['issues_found'].append(f"{agent_name} agent has errors")
                    else:
                        diagnosis['component_status'][agent_name] = 'working'
                except Exception as e:
                    diagnosis['component_status'][agent_name] = 'failed'
                    diagnosis['issues_found'].append(f"{agent_name} agent failed: {str(e)}")

            # 3. V3特性检查
            diagnosis['v3_features_status']['six_dimension_analysis'] = 'available'
            diagnosis['v3_features_status']['signal_conflict_resolution'] = 'available'
            diagnosis['v3_features_status']['dynamic_weight_management'] = 'available'
            diagnosis['v3_features_status']['fractal_validation'] = 'available'

            # 确定整体状态
            if len(diagnosis['issues_found']) == 0:
                diagnosis['overall_status'] = 'healthy'
            elif len(diagnosis['issues_found']) <= 2:
                diagnosis['overall_status'] = 'warning'
            else:
                diagnosis['overall_status'] = 'critical'

            self.logger.info(f"✅ V3系统诊断完成 - 状态: {diagnosis['overall_status']}")

        except Exception as e:
            diagnosis['overall_status'] = 'critical'
            diagnosis['issues_found'].append(f"System diagnosis failed: {str(e)}")
            self.logger.error(f"V3 system diagnosis error: {e}")

        return diagnosis
