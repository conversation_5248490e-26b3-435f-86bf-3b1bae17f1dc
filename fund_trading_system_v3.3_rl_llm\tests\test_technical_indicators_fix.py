"""
测试技术指标数据传递修复
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3


def test_technical_indicators_fix():
    """测试技术指标数据传递修复"""
    print("测试技术指标数据传递修复")
    print("-" * 50)
    
    coordinator = MultiAgentCoordinatorV3()
    result = coordinator.coordinate_analysis('513500')
    
    print(f"基金: {result['fund_code']}")
    print(f"最终决策: {result.get('final_decision', 'unknown')}")
    print(f"风险等级: {result.get('risk_control', {}).get('risk_level', 'unknown')}")
    
    # 检查风控验证详情
    risk_control = result.get('risk_control', {})
    risk_validation = risk_control.get('risk_validation_result', {}).get('risk_validation', {})
    
    if 'technical_violations' in risk_validation:
        violations = risk_validation['technical_violations']
        if violations:
            print(f"技术指标违规: {list(violations.keys())}")
            for indicator, violation in violations.items():
                print(f"  - {indicator}: {violation}")
        else:
            print("技术指标验证通过")
    else:
        print("未找到技术指标验证结果")
    
    # 检查数据结构
    print("\n数据结构检查:")
    if 'technical_analysis' in result.get('traditional_agents', {}):
        print("传统智能体中有technical_analysis")
    
    # 检查整合数据
    enhanced_decision = result.get('enhanced_decision', {})
    if enhanced_decision:
        print(f"增强决策结果: {enhanced_decision.get('decision', 'unknown')}")
        print(f"增强决策置信度: {enhanced_decision.get('confidence', 0.0)}")


if __name__ == "__main__":
    test_technical_indicators_fix()
