# 使用指南

## 🚀 快速开始

### 1. 环境检查
首先确保你的环境已经安装了必要的依赖：

```bash
# 检查Python版本 (需要3.9+)
python --version

# 检查关键库
python -c "import torch; print('PyTorch:', torch.__version__)"
python -c "import gymnasium; print('Gymnasium:', gymnasium.__version__)"
python -c "import pandas; print('Pandas:', pandas.__version__)"
```

### 2. 最小化测试
运行最小化测试来验证系统功能：

```bash
python minimal_test.py
```

预期输出：
```
=== 最小化功能测试 ===
1. 测试数据生成...
   数据生成成功: (100, 7)
2. 测试交易环境...
   环境重置成功，观察维度: (10,)
...
✅ 所有测试通过！系统核心功能正常。
```

### 3. 简化系统测试
运行不包含技术指标的简化版本：

```bash
python simple_test_system.py
```

这将训练50轮，大约需要5-10分钟。

### 4. 完整系统测试
运行包含技术指标的完整版本：

```bash
python quick_test_system.py
```

## 📊 理解输出结果

### 训练过程输出
```
Episode 0: Train=9.5103, Val=0.0000, Net Worth=$106764.26
保存最佳模型，验证得分: 0.0000
```

- **Train**: 训练集上的平均得分
- **Val**: 验证集上的得分
- **Net Worth**: 当前净资产价值

### 测试结果输出
```
测试轮次 1: 奖励=0.0673, 净值=$106730.90, 收益率=6.73%
```

- **奖励**: 该轮次的累积奖励
- **净值**: 最终资产价值
- **收益率**: 相对于初始资金的收益率

## 🔧 自定义配置

### 基础配置
```python
from integrated_trading_system import TradingSystem

config = {
    'data': {
        'symbol': 'DEMO',
        'length': 1000,        # 数据长度
        'train_ratio': 0.7,    # 训练集比例
        'val_ratio': 0.15      # 验证集比例
    },
    'env': {
        'initial_balance': 100000,           # 初始资金
        'use_technical_indicators': True,    # 是否使用技术指标
        'transaction_cost': 0.001           # 交易成本
    },
    'training': {
        'episodes': 500,       # 训练轮数
        'batch_size': 64,      # 批次大小
        'memory_size': 50000,  # 经验池大小
        'eval_episodes': 10,   # 评估轮数
        'save_freq': 50        # 保存频率
    }
}

system = TradingSystem(config)
train_results, test_results = system.run_complete_pipeline()
```

### 高级配置
```python
# 模型参数调整
config['model'] = {
    'actor_lr': 1e-4,      # Actor学习率
    'critic_lr': 1e-4,     # Critic学习率
    'gamma': 0.99,         # 折扣因子
    'tau': 0.005,          # 软更新系数
    'alpha': 0.2           # 熵正则化系数
}
```

## 📈 性能调优

### 1. 数据量调整
- **小数据集** (length=500): 快速测试
- **中等数据集** (length=2000): 标准训练
- **大数据集** (length=5000+): 充分训练

### 2. 训练参数调整
- **快速测试**: episodes=50-100
- **标准训练**: episodes=500-1000
- **深度训练**: episodes=2000+

### 3. 网络参数调整
- **学习率**: 3e-4 (标准), 1e-4 (稳定), 1e-3 (快速)
- **批次大小**: 32 (小), 64 (标准), 128 (大)
- **经验池**: 10000 (小), 50000 (标准), 100000 (大)

## 🎯 使用场景

### 场景1: 快速验证
```bash
# 目标: 验证系统是否正常工作
python minimal_test.py
```

### 场景2: 算法研究
```python
# 目标: 研究不同算法参数的效果
config['training']['episodes'] = 1000
config['model']['actor_lr'] = 1e-4
system = TradingSystem(config)
```

### 场景3: 策略开发
```python
# 目标: 开发新的交易策略
class CustomTradingEnv(EnhancedStockTradingEnv):
    def _calculate_reward(self, action, info):
        # 自定义奖励函数
        return custom_reward

# 使用自定义环境
```

### 场景4: 生产部署
```python
# 目标: 部署到生产环境
config['data']['length'] = 10000
config['training']['episodes'] = 2000
config['env']['transaction_cost'] = 0.0005  # 更真实的交易成本
```

## 📁 文件管理

### 模型文件
- `./models/best_model.pth`: 最佳模型
- `./models/model_episode_X.pth`: 定期保存的模型
- `./models/test_model.pth`: 测试模型

### 结果文件
- `./results/config_TIMESTAMP.json`: 配置文件
- `./results/train_scores_TIMESTAMP.csv`: 训练得分
- `./results/test_results_TIMESTAMP.csv`: 测试结果
- `./results/training_results.png`: 结果图表

## 🐛 常见问题

### Q1: CUDA内存不足
```python
# 解决方案: 减少批次大小
config['training']['batch_size'] = 32
config['training']['memory_size'] = 10000
```

### Q2: 训练速度慢
```python
# 解决方案: 减少数据量和训练轮数
config['data']['length'] = 500
config['training']['episodes'] = 100
```

### Q3: 收益率不理想
```python
# 解决方案: 调整奖励函数和交易成本
config['env']['transaction_cost'] = 0.0001  # 降低交易成本
# 或者修改奖励函数
```

### Q4: 技术指标版本报错
```bash
# 解决方案: 先运行简化版本
python simple_test_system.py
```

## 📊 结果解读

### 好的结果指标
- 训练得分逐渐上升
- 验证得分稳定
- 测试收益率 > 5%
- 最大回撤 < 20%

### 需要改进的指标
- 训练得分波动大
- 验证得分下降
- 测试收益率 < 0%
- 频繁的大幅亏损

## 🔄 下一步

1. **成功运行基础测试后**:
   - 尝试调整参数
   - 增加训练轮数
   - 使用技术指标版本

2. **获得满意结果后**:
   - 保存最佳配置
   - 进行更长时间的训练
   - 考虑真实数据测试

3. **准备生产使用**:
   - 集成实时数据
   - 添加风险控制
   - 实施监控系统

---

**提示**: 强化学习需要大量的试验和调整，不要期望第一次就获得完美的结果。耐心调试和优化是成功的关键！