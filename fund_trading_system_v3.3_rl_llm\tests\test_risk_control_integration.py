"""
风控系统集成测试
测试风控系统与现有交易系统的集成
"""

import unittest
import sys
import os
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
from system.enhanced_trading_system import EnhancedFundTradingSystemV3
from agents.enhanced.risk_control_agent import RiskControlAgent
from core.data_structures import DimensionEvaluationResult


class TestRiskControlIntegration(unittest.TestCase):
    """测试风控系统集成"""
    
    def setUp(self):
        """测试前准备"""
        self.coordinator = MultiAgentCoordinatorV3()
        self.test_fund_code = '513500'
        
    def test_coordinator_with_risk_control(self):
        """测试协调器集成风控验证"""
        # Mock各个agent的返回结果
        with patch.object(self.coordinator.technical_agent, 'process') as mock_tech, \
             patch.object(self.coordinator.gua_agent, 'process') as mock_gua, \
             patch.object(self.coordinator.flow_agent, 'process') as mock_flow, \
             patch.object(self.coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
            
            # 设置mock返回值
            mock_tech.return_value = {
                'buy_signal': True,
                'indicators': {
                    'bb_position': 0.1,  # 符合买入条件
                    'rsi': 45,
                    'volume_ratio': 1.5
                },
                'confidence': 0.8
            }
            
            mock_gua.return_value = {
                'gua_score': 0.6,
                'gua_analysis': '良好'
            }
            
            mock_flow.return_value = {
                'high_liquidity': True,
                'flow_score': 0.7
            }
            
            mock_enhanced.return_value = {
                'decision': 'buy',
                'confidence': 0.8,
                'dimension_evaluations': {
                    '趋势': DimensionEvaluationResult('趋势', 'up', 0.7, 0.8, [], 'good'),
                    '波动性': DimensionEvaluationResult('波动性', 'medium', 0.5, 0.7, [], 'good'),
                    '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
                    '情绪': DimensionEvaluationResult('情绪', 'positive', 0.6, 0.7, [], 'good'),
                    '结构': DimensionEvaluationResult('结构', 'stable', 0.7, 0.8, [], 'good'),
                    '转换': DimensionEvaluationResult('转换', 'low', 0.3, 0.6, [], 'good')
                }
            }
            
            # 执行协调分析
            result = self.coordinator.coordinate_analysis(self.test_fund_code)
            
            # 验证结果包含风控信息
            self.assertIn('risk_control', result)
            self.assertIn('final_decision', result)
            self.assertIn('final_confidence', result)
            self.assertEqual(result['fund_code'], self.test_fund_code)
            self.assertEqual(result['coordinator_version'], 'V3.1')
    
    def test_risk_control_blocks_bad_buy(self):
        """测试风控阻止不良买入决策"""
        with patch.object(self.coordinator.technical_agent, 'process') as mock_tech, \
             patch.object(self.coordinator.gua_agent, 'process') as mock_gua, \
             patch.object(self.coordinator.flow_agent, 'process') as mock_flow, \
             patch.object(self.coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
            
            # 设置mock返回值 - 技术指标不符合风控要求
            mock_tech.return_value = {
                'buy_signal': True,
                'indicators': {
                    'bb_position': 0.9,  # 布林线位置过高
                    'rsi': 75,  # RSI过高
                    'volume_ratio': 0.8  # 成交量不足
                },
                'confidence': 0.8
            }
            
            mock_gua.return_value = {'gua_score': 0.6}
            mock_flow.return_value = {'high_liquidity': True}
            mock_enhanced.return_value = {
                'decision': 'buy',
                'confidence': 0.8,
                'dimension_evaluations': {}
            }
            
            result = self.coordinator.coordinate_analysis(self.test_fund_code)
            
            # 验证风控阻止了买入
            self.assertIn('risk_control', result)
            risk_control = result['risk_control']
            
            # 最终决策应该被风控改为hold
            self.assertIn('final_decision', result)
            # 由于技术指标违规，最终决策可能被改为hold
    
    def test_risk_control_allows_good_buy(self):
        """测试风控允许良好的买入决策"""
        with patch.object(self.coordinator.technical_agent, 'process') as mock_tech, \
             patch.object(self.coordinator.gua_agent, 'process') as mock_gua, \
             patch.object(self.coordinator.flow_agent, 'process') as mock_flow, \
             patch.object(self.coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
            
            # 设置mock返回值 - 所有条件都符合
            mock_tech.return_value = {
                'buy_signal': True,
                'indicators': {
                    'bb_position': 0.1,  # 布林线位置良好
                    'rsi': 40,  # RSI适中
                    'volume_ratio': 1.5,  # 成交量充足
                    'macd_bullish': True
                },
                'confidence': 0.8
            }
            
            mock_gua.return_value = {'gua_score': 0.7}
            mock_flow.return_value = {'high_liquidity': True}
            mock_enhanced.return_value = {
                'decision': 'buy',
                'confidence': 0.8,
                'dimension_evaluations': {
                    '波动性': DimensionEvaluationResult('波动性', 'low', 0.3, 0.8, [], 'good'),
                    '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
                    '情绪': DimensionEvaluationResult('情绪', 'positive', 0.7, 0.8, [], 'good')
                }
            }
            
            result = self.coordinator.coordinate_analysis(self.test_fund_code)
            
            # 验证风控通过
            self.assertIn('risk_control', result)
            self.assertIn('final_decision', result)


class TestEnhancedTradingSystemIntegration(unittest.TestCase):
    """测试增强交易系统集成"""
    
    def setUp(self):
        """测试前准备"""
        self.trading_system = EnhancedFundTradingSystemV3("测试系统")
        
    @patch('coordinators.multi_agent_coordinator.MultiAgentCoordinatorV3.coordinate_analysis')
    def test_trading_system_with_risk_control(self, mock_coordinate):
        """测试交易系统集成风控"""
        # Mock协调器返回结果
        mock_coordinate.return_value = {
            'fund_code': '513500',
            'analysis_time': datetime.now().isoformat(),
            'enhanced_decision': {
                'decision': 'buy',
                'confidence': 0.8
            },
            'risk_control': {
                'final_decision': 'buy',
                'confidence': 0.8,
                'risk_level': 'low'
            },
            'final_decision': 'buy',
            'final_confidence': 0.8
        }
        
        # 执行分析
        result = self.trading_system.analyze_fund_v3('513500')
        
        # 验证结果包含风控信息
        self.assertIn('risk_control', result)
        self.assertIn('final_decision', result)
        self.assertEqual(result['fund_code'], '513500')
    
    @patch('coordinators.multi_agent_coordinator.MultiAgentCoordinatorV3.coordinate_analysis')
    def test_trading_execution_with_risk_control(self, mock_coordinate):
        """测试交易执行集成风控"""
        # Mock分析结果 - 风控通过
        analysis_result = {
            'fund_code': '513500',
            'enhanced_decision': {
                'decision': 'buy',
                'confidence': 0.8
            },
            'risk_control': {
                'final_decision': 'buy',
                'confidence': 0.8,
                'risk_level': 'low'
            },
            'final_decision': 'buy',
            'final_confidence': 0.8
        }
        
        # 执行交易决策
        execution_result = self.trading_system.execute_trading_decision_v3(analysis_result)
        
        # 验证执行结果
        self.assertIsNotNone(execution_result)


class TestRiskControlWorkflow(unittest.TestCase):
    """测试风控工作流程"""
    
    def setUp(self):
        """测试前准备"""
        self.risk_agent = RiskControlAgent()
        
    def test_complete_risk_workflow(self):
        """测试完整的风控工作流程"""
        # 第一步：准备测试数据
        test_data = {
            'fund_code': '513500',
            'analysis_result': {
                'technical_analysis': {
                    'indicators': {
                        'bb_position': 0.15,  # 接近下轨
                        'rsi': 35,  # 偏低RSI
                        'volume_ratio': 1.3,  # 适中成交量
                        'close': 100,
                        'bb_upper': 105,
                        'bb_middle': 100,
                        'bb_lower': 95,
                        'macd_dif': 0.2,
                        'macd_dea': 0.1,
                        'macd_bullish': True
                    }
                },
                'dimension_evaluations': {
                    '波动性': DimensionEvaluationResult('波动性', 'medium', 0.5, 0.7, [], 'good'),
                    '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
                    '情绪': DimensionEvaluationResult('情绪', 'neutral', 0.5, 0.6, [], 'good')
                }
            },
            'proposed_decision': 'buy'
        }
        
        # 第二步：执行风控验证
        result = self.risk_agent.process(test_data)
        
        # 第三步：验证结果结构
        self.assertIn('fund_code', result)
        self.assertIn('risk_validation', result)
        self.assertIn('final_decision', result)
        self.assertIn('risk_level', result)
        
        # 第四步：验证风控逻辑
        risk_validation = result['risk_validation']
        self.assertIn('technical_violations', risk_validation)
        self.assertIn('market_environment_score', risk_validation)
        self.assertIn('portfolio_risk_score', risk_validation)
        
        # 第五步：测试详细技术验证
        detailed_result = self.risk_agent.validate_buy_conditions(
            '513500', 100, test_data['analysis_result']['technical_analysis']
        )
        
        self.assertIn('technical_analysis', detailed_result)
        self.assertIn('bollinger_analysis', detailed_result['technical_analysis'])
        self.assertIn('rsi_analysis', detailed_result['technical_analysis'])
        self.assertIn('volume_analysis', detailed_result['technical_analysis'])
    
    def test_risk_escalation_workflow(self):
        """测试风险升级工作流程"""
        # 高风险场景
        high_risk_data = {
            'fund_code': '513500',
            'analysis_result': {
                'technical_analysis': {
                    'indicators': {
                        'bb_position': 0.95,  # 严重超买
                        'rsi': 85,  # 严重超买
                        'volume_ratio': 0.5  # 成交量不足
                    }
                },
                'dimension_evaluations': {
                    '波动性': DimensionEvaluationResult('波动性', 'high', 0.9, 0.8, [], 'poor'),
                    '流动性': DimensionEvaluationResult('流动性', 'poor', 0.2, 0.5, [], 'poor'),
                    '情绪': DimensionEvaluationResult('情绪', 'negative', 0.1, 0.4, [], 'poor')
                }
            },
            'proposed_decision': 'buy'
        }
        
        result = self.risk_agent.process(high_risk_data)
        
        # 验证高风险被正确识别
        self.assertEqual(result['final_decision'], 'hold')
        self.assertIn(result['risk_level'], ['high', 'critical'])
        
        risk_validation = result['risk_validation']
        self.assertFalse(risk_validation.get('passed', True))
        self.assertTrue(len(risk_validation.get('rejection_reasons', [])) > 0)


class TestRiskControlPerformance(unittest.TestCase):
    """测试风控系统性能"""
    
    def setUp(self):
        """测试前准备"""
        self.coordinator = MultiAgentCoordinatorV3()
        
    def test_risk_control_performance(self):
        """测试风控系统性能"""
        import time
        
        # 准备测试数据
        test_funds = ['513500', '513080', '513030']
        
        with patch.object(self.coordinator.technical_agent, 'process') as mock_tech, \
             patch.object(self.coordinator.gua_agent, 'process') as mock_gua, \
             patch.object(self.coordinator.flow_agent, 'process') as mock_flow, \
             patch.object(self.coordinator.enhanced_decision_agent, 'process') as mock_enhanced:
            
            # 设置mock返回值
            mock_tech.return_value = {
                'buy_signal': True,
                'indicators': {'bb_position': 0.1, 'rsi': 45, 'volume_ratio': 1.5},
                'confidence': 0.8
            }
            mock_gua.return_value = {'gua_score': 0.6}
            mock_flow.return_value = {'high_liquidity': True}
            mock_enhanced.return_value = {
                'decision': 'buy',
                'confidence': 0.8,
                'dimension_evaluations': {}
            }
            
            # 测试性能
            start_time = time.time()
            
            for fund_code in test_funds:
                result = self.coordinator.coordinate_analysis(fund_code)
                self.assertIn('risk_control', result)
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time = total_time / len(test_funds)
            
            # 验证性能要求（每个基金分析应在2秒内完成）
            self.assertLess(avg_time, 2.0, f"平均分析时间 {avg_time:.3f}s 超过2秒限制")
    
    def test_risk_control_memory_usage(self):
        """测试风控系统内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多次风控验证
        risk_agent = RiskControlAgent()
        
        for i in range(100):
            test_data = {
                'fund_code': f'51350{i % 10}',
                'analysis_result': {
                    'technical_analysis': {
                        'indicators': {
                            'bb_position': 0.1 + (i % 10) * 0.1,
                            'rsi': 30 + (i % 40),
                            'volume_ratio': 1.0 + (i % 5) * 0.2
                        }
                    }
                },
                'proposed_decision': 'buy'
            }
            
            result = risk_agent.process(test_data)
            self.assertIsNotNone(result)
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 验证内存使用合理（增长不超过50MB）
        self.assertLess(memory_increase, 50, f"内存增长 {memory_increase:.1f}MB 过多")


if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2)
