# Product Requirements Document (PRD): System v3.5
---

### 1. Introduction & Vision

This document outlines the requirements for **System v3.5**, a unified, end-to-end platform for the development, testing, and deployment of sophisticated, AI-driven fund trading strategies.

The vision is to merge the powerful real-time, multi-agent decision-making core of `v3.3` with the robust backtesting framework and advanced technical analysis capabilities of `v3.4`. The resulting system will provide a seamless workflow for quantitative strategists, from initial idea to rigorous backtesting and final deployment, all within a single, cohesive environment.

### 2. Goals & Objectives

- **Unify Codebase**: Eliminate redundancy and create a single source of truth for the entire trading system.
- **Seamless Mode Switching**: Enable users to effortlessly switch between a high-fidelity `backtest` mode and a `live/paper trading` mode.
- **Enhance Decision Intelligence**: Augment the existing RL (quantitative) and LLM (semantic) signals with CZSC (structural technical analysis) to create a more robust and diversified decision-making process.
- **Improve Strategy Lifecycle Management**: Support the full lifecycle of a trading strategy: design, development, testing, optimization, and deployment.

### 3. User Persona

**Name**: Dr. <PERSON>
**Role**: Quantitative Strategist / AI Trading Developer
**Needs**:
- A stable platform to rapidly prototype and test new trading ideas.
- The ability to combine signals from different sources (quantitative models, news analysis, technical patterns).
- A reliable backtesting engine to validate strategy performance before risking capital.
- A clear and easy path to deploy a validated strategy into a live (or paper) trading environment.
- Confidence that the logic used in backtesting is the *exact* same logic used in live trading.

### 4. Functional Requirements

#### 4.1. Unified Trading Core
- The system shall incorporate three distinct types of intelligent agents:
    - **RL Agent**: Analyzes market microstructure and quantitative data to generate trading signals.
    - **LLM Agent**: Analyzes news, reports, and other unstructured text to provide semantic and sentiment context.
    - **CZSC Agent**: Analyzes price action based on "Chan Zhong Shuo Chan" theory to identify chart patterns and structural opportunities.
- The system shall feature a central **Agent Collaboration Manager** that intelligently fuses the signals from all three agents into a single, actionable trading decision.
- The fusion logic must be configurable, allowing the user to adjust the weight or priority of each agent's input.

#### 4.2. Dual-Mode Operation
- The system must be launchable in one of two modes via a command-line interface:
    1.  **Backtest Mode (`--mode backtest`)**:
        - Runs the defined strategy over a specified historical dataset.
        - Uses the exact same signal generation and decision logic as live trading.
        - Produces a comprehensive performance report at the end of the run, including metrics like Sharpe Ratio, Max Drawdown, Win/Loss Ratio, and an equity curve.
    2.  **Trade Mode (`--mode trade`)**:
        - Connects to a live data feed (or a simulated one for paper trading).
        - Executes the trading logic on each new data point (e.g., every new bar).
        - Generates and logs trading decisions in real-time.
        - Forwards decisions to a trade execution module.

#### 4.3. Centralized Configuration
- All system parameters must be managed through a single, unified configuration file.
- This includes data paths, model paths, agent-specific settings, backtest parameters (start/end dates, initial capital, commissions), and live trading parameters (API keys, endpoints).

### 5. Non-Functional Requirements

- **Extensibility**: The architecture should make it straightforward to add a fourth type of agent in the future with minimal changes to the core system.
- **Performance**: Backtests should be computationally efficient, allowing for rapid iteration on strategy ideas.
- **Reliability**: The system must be stable and produce deterministic results (i.e., a backtest run with the same parameters must always yield the same result).
- **Usability**: The system should be well-documented with a clear `README` file explaining how to configure and run it in both modes.

### 6. Success Metrics

- A user can run a complete backtest and a live paper-trading session using a single command for each.
- The final `v3.5` codebase has over 95% less code duplication compared to the combined code of `v3.3` and `v3.4`.
- A new agent can be prototyped and integrated into the collaboration manager within a reasonable timeframe (e.g., less than a week).
- The backtest report is comprehensive enough to make an informed decision about the strategy's viability.

### 7. Out of Scope

- A graphical user interface (GUI).
- Direct, real-money trading execution (initially). The focus is on generating signals and paper trading.
- Automated strategy optimization (e.g., via grid search or genetic algorithms), although the backtesting framework should support this in the future.
