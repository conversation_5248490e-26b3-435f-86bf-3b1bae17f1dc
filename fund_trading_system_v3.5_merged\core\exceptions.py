"""
Custom Exception Classes for Trading System v3.5

Provides specific exception types for better error handling and debugging.
"""

from typing import Optional, Dict, Any


class TradingSystemError(Exception):
    """Base exception class for all trading system errors"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.context = context or {}
    
    def __str__(self) -> str:
        error_str = f"TradingSystemError: {self.message}"
        if self.error_code:
            error_str += f" (Code: {self.error_code})"
        if self.context:
            error_str += f" Context: {self.context}"
        return error_str


class ConfigurationError(TradingSystemError):
    """Raised when there are configuration-related errors"""
    pass


class DataError(TradingSystemError):
    """Raised when there are data-related errors"""
    pass


class ModelError(TradingSystemError):
    """Raised when there are model-related errors"""
    pass


class AgentError(TradingSystemError):
    """Raised when there are agent-related errors"""
    pass


class CoordinationError(TradingSystemError):
    """Raised when there are coordination-related errors"""
    pass


class BacktestError(TradingSystemError):
    """Raised when there are backtest-related errors"""
    pass


class ValidationError(TradingSystemError):
    """Raised when validation fails"""
    pass


# Specific error types for common scenarios

class InvalidDataFormatError(DataError):
    """Raised when data format is invalid"""
    
    def __init__(self, expected_format: str, actual_format: str, 
                 field_name: Optional[str] = None):
        message = f"Invalid data format. Expected: {expected_format}, Got: {actual_format}"
        if field_name:
            message += f" for field '{field_name}'"
        
        context = {
            'expected_format': expected_format,
            'actual_format': actual_format,
            'field_name': field_name
        }
        super().__init__(message, error_code="INVALID_DATA_FORMAT", context=context)


class MissingDataError(DataError):
    """Raised when required data is missing"""
    
    def __init__(self, missing_fields: list, data_source: Optional[str] = None):
        message = f"Missing required data fields: {missing_fields}"
        if data_source:
            message += f" from source '{data_source}'"
        
        context = {
            'missing_fields': missing_fields,
            'data_source': data_source
        }
        super().__init__(message, error_code="MISSING_DATA", context=context)


class ModelLoadError(ModelError):
    """Raised when model loading fails"""
    
    def __init__(self, model_path: str, reason: str):
        message = f"Failed to load model from '{model_path}': {reason}"
        context = {
            'model_path': model_path,
            'reason': reason
        }
        super().__init__(message, error_code="MODEL_LOAD_FAILED", context=context)


class AgentInitializationError(AgentError):
    """Raised when agent initialization fails"""
    
    def __init__(self, agent_name: str, reason: str):
        message = f"Failed to initialize agent '{agent_name}': {reason}"
        context = {
            'agent_name': agent_name,
            'reason': reason
        }
        super().__init__(message, error_code="AGENT_INIT_FAILED", context=context)


class InsufficientConfidenceError(AgentError):
    """Raised when agent confidence is too low"""
    
    def __init__(self, agent_name: str, confidence: float, threshold: float):
        message = f"Agent '{agent_name}' confidence {confidence:.3f} below threshold {threshold:.3f}"
        context = {
            'agent_name': agent_name,
            'confidence': confidence,
            'threshold': threshold
        }
        super().__init__(message, error_code="INSUFFICIENT_CONFIDENCE", context=context)


class ConfigValidationError(ConfigurationError):
    """Raised when configuration validation fails"""
    
    def __init__(self, validation_errors: list, config_section: Optional[str] = None):
        message = f"Configuration validation failed: {validation_errors}"
        if config_section:
            message += f" in section '{config_section}'"
        
        context = {
            'validation_errors': validation_errors,
            'config_section': config_section
        }
        super().__init__(message, error_code="CONFIG_VALIDATION_FAILED", context=context)


class APIError(TradingSystemError):
    """Raised when external API calls fail"""
    
    def __init__(self, api_name: str, status_code: Optional[int] = None, 
                 response_text: Optional[str] = None):
        message = f"API call to '{api_name}' failed"
        if status_code:
            message += f" with status code {status_code}"
        if response_text:
            message += f": {response_text}"
        
        context = {
            'api_name': api_name,
            'status_code': status_code,
            'response_text': response_text
        }
        super().__init__(message, error_code="API_CALL_FAILED", context=context)


# Error handling utilities

def handle_error_with_context(error: Exception, context: Dict[str, Any]) -> TradingSystemError:
    """Convert generic exceptions to TradingSystemError with context"""
    if isinstance(error, TradingSystemError):
        # Add additional context to existing TradingSystemError
        error.context.update(context)
        return error
    
    # Convert generic exception to TradingSystemError
    return TradingSystemError(
        message=str(error),
        error_code="GENERIC_ERROR",
        context=context
    )


def create_error_response(error: TradingSystemError) -> Dict[str, Any]:
    """Create standardized error response dictionary"""
    return {
        'success': False,
        'error': {
            'message': error.message,
            'code': error.error_code,
            'context': error.context,
            'type': error.__class__.__name__
        }
    }


def log_error_with_context(logger, error: TradingSystemError) -> None:
    """Log error with full context information"""
    logger.error(
        f"Error occurred: {error.message}",
        extra={
            'error_code': error.error_code,
            'error_type': error.__class__.__name__,
            'context': error.context
        }
    )
