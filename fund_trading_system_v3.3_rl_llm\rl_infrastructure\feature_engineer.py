"""
特征工程器 - 将市场数据转换为RL状态向量
支持基础技术特征和LLM增强特征的提取和组合
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


class FeatureEngineer:
    """
    @class FeatureEngineer
    @brief 特征工程器
    @details 将原始市场数据转换为标准化的状态向量，支持LLM增强特征
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 特征维度配置
        self.state_dim = self.config.get('state_dim', 50)
        self.basic_feature_dim = self.config.get('basic_feature_dim', 30)
        self.llm_feature_dim = self.config.get('llm_feature_dim', 20)
        
        # 特征配置
        self.price_feature_dim = self.config.get('price_feature_dim', 8)
        self.technical_feature_dim = self.config.get('technical_feature_dim', 15)
        self.dimension_feature_dim = self.config.get('dimension_feature_dim', 6)
        self.macro_feature_dim = self.config.get('macro_feature_dim', 8)
        
        # 数据标准化参数
        self.enable_normalization = self.config.get('enable_normalization', True)
        self.normalization_method = self.config.get('normalization_method', 'min_max')  # min_max, z_score
        
        # 特征缓存
        self.feature_cache = {}
        self.cache_size_limit = self.config.get('cache_size_limit', 100)
        
        # 统计信息
        self.feature_stats = {
            'total_extractions': 0,
            'cache_hits': 0,
            'extraction_errors': 0
        }
        
        self.logger.info("特征工程器初始化完成")
    
    def create_enhanced_state_vector(self, market_data: Dict[str, Any], 
                                   llm_insights: Optional[Dict[str, Any]] = None,
                                   fund_code: str = None) -> np.ndarray:
        """
        @brief 创建增强状态向量（50维）
        @param market_data: 市场数据
        @param llm_insights: LLM分析洞察
        @param fund_code: 基金代码
        @return: 标准化的状态向量
        """
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(market_data, llm_insights, fund_code)
            
            # 检查缓存
            if cache_key in self.feature_cache:
                self.feature_stats['cache_hits'] += 1
                return self.feature_cache[cache_key]
            
            # 提取基础技术特征 (30维)
            basic_features = self._extract_basic_features(market_data)
            
            # 提取LLM增强特征 (20维)
            if llm_insights:
                llm_features = self._extract_llm_features(llm_insights)
            else:
                llm_features = self._get_default_llm_features()
            
            # 组合特征向量
            combined_features = np.concatenate([basic_features, llm_features])
            
            # 确保维度正确
            enhanced_vector = self._ensure_correct_dimensions(combined_features, self.state_dim)
            
            # 数据标准化
            if self.enable_normalization:
                enhanced_vector = self._normalize_features(enhanced_vector)
            
            # 缓存结果
            self._cache_features(cache_key, enhanced_vector)
            
            # 更新统计
            self.feature_stats['total_extractions'] += 1
            
            return enhanced_vector
            
        except Exception as e:
            self.logger.error(f"创建增强状态向量失败: {str(e)}")
            self.feature_stats['extraction_errors'] += 1
            return self._get_fallback_state_vector()
    
    def create_basic_state_vector(self, market_data: Dict[str, Any], 
                                position: float = 0.0, 
                                portfolio_value: float = 100000.0) -> np.ndarray:
        """
        @brief 创建基础状态向量（不含LLM特征）
        @param market_data: 市场数据
        @param position: 当前持仓
        @param portfolio_value: 组合价值
        @return: 基础状态向量
        """
        try:
            # 提取基础特征
            basic_features = self._extract_basic_features(market_data, position, portfolio_value)
            
            # 扩展到目标维度
            extended_vector = self._ensure_correct_dimensions(basic_features, self.state_dim)
            
            # 标准化
            if self.enable_normalization:
                extended_vector = self._normalize_features(extended_vector)
            
            return extended_vector
            
        except Exception as e:
            self.logger.error(f"创建基础状态向量失败: {str(e)}")
            return self._get_fallback_state_vector()
    
    def _extract_basic_features(self, market_data: Dict[str, Any], 
                              position: float = 0.0, 
                              portfolio_value: float = 100000.0) -> np.ndarray:
        """提取基础技术特征（30维）"""
        features = []
        
        try:
            # 价格特征 (8维)
            price_features = self._extract_price_features(market_data)
            features.extend(price_features)
            
            # 技术指标特征 (15维) 
            technical_features = self._extract_technical_features(market_data)
            features.extend(technical_features)
            
            # 六大维度评估特征 (6维)
            dimension_features = self._extract_dimension_features(market_data)
            features.extend(dimension_features)
            
            # 组合状态特征 (1维，扩展为其他维度占位符)
            portfolio_features = [position]  # 当前持仓
            features.extend(portfolio_features)
            
            # 确保30维
            while len(features) < self.basic_feature_dim:
                features.append(0.5)  # 默认值填充
            
            return np.array(features[:self.basic_feature_dim], dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"提取基础特征失败: {str(e)}")
            return np.full(self.basic_feature_dim, 0.5, dtype=np.float32)
    
    def _extract_price_features(self, market_data: Dict[str, Any]) -> List[float]:
        """提取价格特征（8维）"""
        try:
            price_data = market_data.get('price_data', {})
            
            features = [
                price_data.get('current_price', 1.0),
                price_data.get('change_pct', 0.0) / 100,  # 标准化到[-1,1]范围
                price_data.get('volume', 1000000) / 1000000,  # 标准化
                self._calculate_price_momentum(price_data),
                self._calculate_price_volatility(price_data),
                self._calculate_price_trend(price_data),
                self._calculate_volume_trend(price_data),
                self._calculate_price_position(price_data)  # 当前价格在历史范围中的位置
            ]
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取价格特征失败: {str(e)}")
            return [0.5] * self.price_feature_dim
    
    def _extract_technical_features(self, market_data: Dict[str, Any]) -> List[float]:
        """提取技术指标特征（15维）"""
        try:
            technical_data = market_data.get('technical_analysis', {})
            indicators = technical_data.get('indicators', {})
            
            features = [
                # 趋势指标
                indicators.get('rsi', 50) / 100,  # 标准化到[0,1]
                self._normalize_macd(indicators.get('macd', 0.0)),
                self._calculate_ma_ratio(indicators),  # MA5/MA20比率
                self._calculate_price_ma_position(market_data, indicators),  # 价格相对MA位置
                
                # 动量指标
                self._calculate_momentum_score(indicators),
                self._calculate_trend_strength(indicators),
                
                # 波动率指标
                self._calculate_volatility_indicator(indicators),
                self._calculate_bollinger_position(indicators),
                
                # 成交量指标
                self._calculate_volume_ratio(market_data),
                self._calculate_volume_momentum(market_data),
                
                # 支撑阻力指标
                self._calculate_support_resistance_position(market_data),
                
                # 综合指标
                self._calculate_technical_score(indicators),
                self._calculate_divergence_indicator(market_data),
                self._calculate_cycle_position(market_data),
                
                # 占位符
                0.5
            ]
            
            return features[:self.technical_feature_dim]
            
        except Exception as e:
            self.logger.error(f"提取技术指标特征失败: {str(e)}")
            return [0.5] * self.technical_feature_dim
    
    def _extract_dimension_features(self, market_data: Dict[str, Any]) -> List[float]:
        """提取六大维度评估特征（6维）"""
        try:
            evaluations = market_data.get('evaluations', {})
            
            dimension_names = ['趋势', '波动性', '流动性', '情绪', '结构', '转换']
            features = []
            
            for dim_name in dimension_names:
                eval_result = evaluations.get(dim_name)
                if eval_result and hasattr(eval_result, 'score'):
                    features.append(eval_result.score)
                else:
                    features.append(0.5)  # 默认中性值
            
            return features[:self.dimension_feature_dim]
            
        except Exception as e:
            self.logger.error(f"提取维度特征失败: {str(e)}")
            return [0.5] * self.dimension_feature_dim
    
    def _extract_llm_features(self, llm_insights: Dict[str, Any]) -> np.ndarray:
        """提取LLM特征（20维）"""
        try:
            enhanced_insights = llm_insights.get('enhanced_insights', {})
            semantic_features = llm_insights.get('semantic_features', {})
            
            features = [
                # 基础情绪特征 (5维)
                enhanced_insights.get('market_sentiment_score', 0.5),
                enhanced_insights.get('sentiment_intensity', 0.5),
                enhanced_insights.get('sentiment_stability', 0.5),
                enhanced_insights.get('confidence_level', 0.5),
                enhanced_insights.get('certainty_level', 0.5),
                
                # 风险特征 (3维)
                enhanced_insights.get('risk_level', 0.5),
                enhanced_insights.get('risk_preference', 0.5),
                semantic_features.get('risk_perception', 0.5),
                
                # 机会特征 (3维)
                enhanced_insights.get('opportunity_score', 0.5),
                enhanced_insights.get('trend_confidence', 0.5),
                semantic_features.get('opportunity_urgency', 0.5),
                
                # 时间特征 (2维)
                enhanced_insights.get('time_horizon', 0.5),
                semantic_features.get('time_urgency', 0.5),
                
                # 驱动因素权重 (4维)
                enhanced_insights.get('driver_importance', {}).get('fundamental', 0.25),
                enhanced_insights.get('driver_importance', {}).get('technical', 0.25),
                enhanced_insights.get('driver_importance', {}).get('sentiment', 0.25),
                enhanced_insights.get('driver_importance', {}).get('macro', 0.25),
                
                # LLM质量指标 (3维)
                self._calculate_llm_quality_score(llm_insights),
                self._calculate_llm_consistency_score(llm_insights),
                self._calculate_llm_relevance_score(llm_insights)
            ]
            
            # 确保20维
            while len(features) < self.llm_feature_dim:
                features.append(0.5)
            
            return np.array(features[:self.llm_feature_dim], dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"提取LLM特征失败: {str(e)}")
            return self._get_default_llm_features()
    
    def _get_default_llm_features(self) -> np.ndarray:
        """获取默认LLM特征"""
        return np.full(self.llm_feature_dim, 0.5, dtype=np.float32)
    
    # 辅助计算方法
    def _calculate_price_momentum(self, price_data: Dict[str, Any]) -> float:
        """计算价格动量"""
        try:
            change_pct = price_data.get('change_pct', 0.0)
            # 将百分比变化转换到[-1,1]范围
            return np.tanh(change_pct / 5.0)  # 5%变化对应约0.76
        except:
            return 0.0
    
    def _calculate_price_volatility(self, price_data: Dict[str, Any]) -> float:
        """计算价格波动率"""
        try:
            # 简化实现：使用价格变化的绝对值
            change_pct = abs(price_data.get('change_pct', 0.0))
            return min(change_pct / 10.0, 1.0)  # 10%变化对应1.0
        except:
            return 0.5
    
    def _calculate_price_trend(self, price_data: Dict[str, Any]) -> float:
        """计算价格趋势"""
        try:
            change_pct = price_data.get('change_pct', 0.0)
            if change_pct > 1.0:
                return 0.8  # 强上涨
            elif change_pct > 0.5:
                return 0.6  # 上涨
            elif change_pct < -1.0:
                return 0.2  # 强下跌
            elif change_pct < -0.5:
                return 0.4  # 下跌
            else:
                return 0.5  # 横盘
        except:
            return 0.5
    
    def _calculate_volume_trend(self, price_data: Dict[str, Any]) -> float:
        """计算成交量趋势"""
        try:
            volume = price_data.get('volume', 1000000)
            # 简化：假设100万为平均成交量
            volume_ratio = volume / 1000000
            return min(volume_ratio, 2.0) / 2.0  # 标准化到[0,1]
        except:
            return 0.5
    
    def _calculate_price_position(self, price_data: Dict[str, Any]) -> float:
        """计算价格在历史范围中的位置"""
        try:
            # 简化实现：使用当前价格相对变化
            current_price = price_data.get('current_price', 1.0)
            # 假设价格在0.8-1.2范围内波动
            normalized_position = (current_price - 0.8) / (1.2 - 0.8)
            return np.clip(normalized_position, 0.0, 1.0)
        except:
            return 0.5
    
    def _normalize_macd(self, macd_value: float) -> float:
        """标准化MACD值"""
        try:
            # MACD值通常在[-0.1, 0.1]范围内
            normalized = (macd_value + 0.1) / 0.2
            return np.clip(normalized, 0.0, 1.0)
        except:
            return 0.5
    
    def _calculate_ma_ratio(self, indicators: Dict[str, Any]) -> float:
        """计算MA5/MA20比率"""
        try:
            ma5 = indicators.get('ma5', 1.0)
            ma20 = indicators.get('ma20', 1.0)
            if ma20 > 0:
                ratio = ma5 / ma20
                # 将比率转换到[0,1]范围，1.0为中性
                return (ratio - 0.9) / 0.2  # 0.9-1.1映射到0-1
            return 0.5
        except:
            return 0.5
    
    def _calculate_price_ma_position(self, market_data: Dict[str, Any], indicators: Dict[str, Any]) -> float:
        """计算价格相对MA的位置"""
        try:
            current_price = market_data.get('price_data', {}).get('current_price', 1.0)
            ma20 = indicators.get('ma20', current_price)
            if ma20 > 0:
                ratio = current_price / ma20
                return (ratio - 0.9) / 0.2  # 标准化
            return 0.5
        except:
            return 0.5
    
    def _calculate_momentum_score(self, indicators: Dict[str, Any]) -> float:
        """计算动量评分"""
        try:
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0.0)
            
            # 综合RSI和MACD计算动量
            rsi_momentum = (rsi - 50) / 50  # [-1, 1]
            macd_momentum = np.tanh(macd * 100)  # [-1, 1]
            
            combined_momentum = (rsi_momentum + macd_momentum) / 2
            return (combined_momentum + 1) / 2  # 转换到[0, 1]
        except:
            return 0.5
    
    def _calculate_trend_strength(self, indicators: Dict[str, Any]) -> float:
        """计算趋势强度"""
        try:
            rsi = indicators.get('rsi', 50)
            ma5 = indicators.get('ma5', 1.0)
            ma20 = indicators.get('ma20', 1.0)
            
            # 基于RSI极值和MA排列判断趋势强度
            rsi_strength = max(abs(rsi - 50) - 20, 0) / 30  # RSI偏离50的程度
            ma_strength = abs(ma5 - ma20) / ma20 if ma20 > 0 else 0
            
            return min((rsi_strength + ma_strength) / 2, 1.0)
        except:
            return 0.5
    
    def _calculate_volatility_indicator(self, indicators: Dict[str, Any]) -> float:
        """计算波动率指标"""
        try:
            # 简化：使用RSI的变化率作为波动率代理
            rsi = indicators.get('rsi', 50)
            volatility = abs(rsi - 50) / 50
            return volatility
        except:
            return 0.5
    
    def _calculate_bollinger_position(self, indicators: Dict[str, Any]) -> float:
        """计算布林带位置"""
        # 简化实现
        return 0.5
    
    def _calculate_volume_ratio(self, market_data: Dict[str, Any]) -> float:
        """计算成交量比率"""
        try:
            volume = market_data.get('price_data', {}).get('volume', 1000000)
            # 相对于平均成交量的比率
            volume_ratio = volume / 1000000
            return min(volume_ratio / 2.0, 1.0)  # 标准化
        except:
            return 0.5
    
    def _calculate_volume_momentum(self, market_data: Dict[str, Any]) -> float:
        """计算成交量动量"""
        # 简化实现
        return 0.5
    
    def _calculate_support_resistance_position(self, market_data: Dict[str, Any]) -> float:
        """计算支撑阻力位置"""
        # 简化实现
        return 0.5
    
    def _calculate_technical_score(self, indicators: Dict[str, Any]) -> float:
        """计算技术综合评分"""
        try:
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0.0)
            
            # 综合技术指标评分
            rsi_score = 1.0 if 30 <= rsi <= 70 else 0.5  # RSI在正常范围
            macd_score = 0.8 if macd > 0 else 0.2  # MACD正负
            
            return (rsi_score + macd_score) / 2
        except:
            return 0.5
    
    def _calculate_divergence_indicator(self, market_data: Dict[str, Any]) -> float:
        """计算背离指标"""
        # 简化实现
        return 0.5
    
    def _calculate_cycle_position(self, market_data: Dict[str, Any]) -> float:
        """计算周期位置"""
        # 简化实现
        return 0.5
    
    def _calculate_llm_quality_score(self, llm_insights: Dict[str, Any]) -> float:
        """计算LLM质量评分"""
        try:
            confidence = llm_insights.get('enhanced_insights', {}).get('confidence_level', 0.5)
            error_flag = 'error' in llm_insights
            
            if error_flag:
                return 0.2
            else:
                return confidence
        except:
            return 0.5
    
    def _calculate_llm_consistency_score(self, llm_insights: Dict[str, Any]) -> float:
        """计算LLM一致性评分"""
        try:
            sentiment_score = llm_insights.get('enhanced_insights', {}).get('market_sentiment_score', 0.5)
            confidence = llm_insights.get('enhanced_insights', {}).get('confidence_level', 0.5)
            
            # 一致性：情绪极端时置信度也应该高
            extremeness = abs(sentiment_score - 0.5) * 2  # [0, 1]
            consistency = 1.0 - abs(extremeness - confidence)
            
            return max(0.0, consistency)
        except:
            return 0.5
    
    def _calculate_llm_relevance_score(self, llm_insights: Dict[str, Any]) -> float:
        """计算LLM相关性评分"""
        try:
            # 简化：基于是否有具体的策略建议
            strategy_suggestion = llm_insights.get('enhanced_insights', {}).get('strategy_suggestion', '')
            
            if len(str(strategy_suggestion)) > 10:  # 有具体建议
                return 0.8
            else:
                return 0.4
        except:
            return 0.5
    
    def _ensure_correct_dimensions(self, features: np.ndarray, target_dim: int) -> np.ndarray:
        """确保特征向量维度正确"""
        try:
            if len(features) > target_dim:
                return features[:target_dim]
            elif len(features) < target_dim:
                padding = np.full(target_dim - len(features), 0.5)
                return np.concatenate([features, padding])
            else:
                return features
        except:
            return np.full(target_dim, 0.5, dtype=np.float32)
    
    def _normalize_features(self, features: np.ndarray) -> np.ndarray:
        """标准化特征"""
        try:
            if self.normalization_method == 'min_max':
                # Min-Max标准化到[0, 1]
                min_val = np.min(features)
                max_val = np.max(features)
                if max_val > min_val:
                    return (features - min_val) / (max_val - min_val)
                else:
                    return features
            elif self.normalization_method == 'z_score':
                # Z-score标准化
                mean_val = np.mean(features)
                std_val = np.std(features)
                if std_val > 0:
                    normalized = (features - mean_val) / std_val
                    # 将z-score转换到[0, 1]范围
                    return 1.0 / (1.0 + np.exp(-normalized))  # sigmoid
                else:
                    return features
            else:
                return features
        except:
            return features
    
    def _generate_cache_key(self, market_data: Dict[str, Any], 
                          llm_insights: Optional[Dict[str, Any]], 
                          fund_code: str) -> str:
        """生成缓存键"""
        try:
            # 简化的缓存键生成
            timestamp = market_data.get('timestamp', datetime.now().isoformat())
            price = market_data.get('price_data', {}).get('current_price', 1.0)
            llm_hash = hash(str(llm_insights)) if llm_insights else 0
            
            return f"{fund_code}_{timestamp}_{price:.4f}_{llm_hash}"
        except:
            return f"fallback_{datetime.now().timestamp()}"
    
    def _cache_features(self, cache_key: str, features: np.ndarray) -> None:
        """缓存特征"""
        try:
            if len(self.feature_cache) >= self.cache_size_limit:
                # 删除最旧的缓存项
                oldest_key = next(iter(self.feature_cache))
                del self.feature_cache[oldest_key]
            
            self.feature_cache[cache_key] = features.copy()
        except Exception as e:
            self.logger.error(f"缓存特征失败: {str(e)}")
    
    def _get_fallback_state_vector(self) -> np.ndarray:
        """获取兜底状态向量"""
        return np.full(self.state_dim, 0.5, dtype=np.float32)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'state_dim': 50,
            'basic_feature_dim': 30,
            'llm_feature_dim': 20,
            'price_feature_dim': 8,
            'technical_feature_dim': 15,
            'dimension_feature_dim': 6,
            'macro_feature_dim': 8,
            'enable_normalization': True,
            'normalization_method': 'min_max',
            'cache_size_limit': 100
        }
    
    def get_feature_importance(self, state_vector: np.ndarray) -> Dict[str, float]:
        """获取特征重要性"""
        try:
            if len(state_vector) != self.state_dim:
                return {'error': 'Invalid state vector dimension'}
            
            # 计算各类特征的重要性
            basic_features = state_vector[:self.basic_feature_dim]
            llm_features = state_vector[self.basic_feature_dim:]
            
            importance = {
                'price_features': float(np.mean(np.abs(basic_features[:self.price_feature_dim] - 0.5))),
                'technical_features': float(np.mean(np.abs(basic_features[self.price_feature_dim:self.price_feature_dim + self.technical_feature_dim] - 0.5))),
                'dimension_features': float(np.mean(np.abs(basic_features[-self.dimension_feature_dim:] - 0.5))),
                'llm_features': float(np.mean(np.abs(llm_features - 0.5))),
                'overall_importance': float(np.mean(np.abs(state_vector - 0.5)))
            }
            
            return importance
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_feature_stats(self) -> Dict[str, Any]:
        """获取特征统计信息"""
        stats = self.feature_stats.copy()
        stats['cache_size'] = len(self.feature_cache)
        stats['cache_hit_rate'] = self.feature_stats['cache_hits'] / max(self.feature_stats['total_extractions'], 1)
        return stats
    
    def clear_cache(self) -> None:
        """清空特征缓存"""
        self.feature_cache.clear()
        self.logger.info("特征缓存已清空") 