#!/usr/bin/env python3
"""
修复PyTorch冲突问题的脚本
"""
import subprocess
import sys
import os

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def main():
    print("=== PyTorch冲突修复脚本 ===")
    
    # 1. 检查当前PyTorch安装
    print("\n1. 检查当前PyTorch安装...")
    success, stdout, stderr = run_command("pip list | findstr torch")
    if success:
        print("当前torch相关包:")
        print(stdout)
    
    # 2. 卸载用户目录下的PyTorch
    print("\n2. 卸载用户目录下的PyTorch...")
    torch_packages = ["torch", "torchvision", "torchaudio"]
    
    for package in torch_packages:
        print(f"卸载 {package}...")
        success, stdout, stderr = run_command(f"pip uninstall {package} -y")
        if success:
            print(f"✓ {package} 卸载成功")
        else:
            print(f"✗ {package} 卸载失败: {stderr}")
    
    # 3. 清理残留文件
    print("\n3. 清理残留文件...")
    user_site_packages = os.path.expanduser("~\\AppData\\Roaming\\Python\\Python39\\site-packages")
    torch_dirs = ["torch", "torchvision", "torchaudio"]
    
    for torch_dir in torch_dirs:
        full_path = os.path.join(user_site_packages, torch_dir)
        if os.path.exists(full_path):
            print(f"发现残留目录: {full_path}")
            print("请手动删除此目录")
    
    # 4. 重新安装PyTorch (CPU版本，兼容性更好)
    print("\n4. 重新安装PyTorch...")
    install_cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    print(f"执行: {install_cmd}")
    
    success, stdout, stderr = run_command(install_cmd)
    if success:
        print("✓ PyTorch重新安装成功")
    else:
        print(f"✗ PyTorch安装失败: {stderr}")
        print("请手动执行以下命令:")
        print(install_cmd)
    
    # 5. 验证安装
    print("\n5. 验证安装...")
    success, stdout, stderr = run_command('python -c "import torch; print(f\'PyTorch版本: {torch.__version__}\'); print(f\'安装位置: {torch.__file__}\')"')
    if success:
        print("验证结果:")
        print(stdout)
    else:
        print(f"验证失败: {stderr}")
    
    print("\n=== 修复完成 ===")
    print("现在可以尝试 import parl")

if __name__ == "__main__":
    main()