# Trading System v3.5 Requirements
# Core dependencies for the multi-agent trading system

# Data processing and numerical computation
numpy>=1.21.0
pandas>=1.3.0

# Machine learning and AI
# Note: Optional dependencies for full functionality
# torch>=1.12.0  # For RL models (optional)
# transformers>=4.20.0  # For LLM integration (optional)

# System and utilities
typing>=3.7.4
dataclasses>=0.6  # Python 3.7+ has this built-in
pathlib>=1.0.1  # Built-in with Python 3.4+

# Logging and configuration
# All built-in modules:
# - logging (built-in)
# - json (built-in)
# - datetime (built-in)
# - argparse (built-in)
# - sys (built-in)
# - os (built-in)

# Optional dependencies for enhanced functionality:
# requests>=2.25.0  # For external API calls
# matplotlib>=3.3.0  # For plotting and visualization
# seaborn>=0.11.0  # For enhanced plotting
# scipy>=1.7.0  # For advanced statistical functions
# scikit-learn>=1.0.0  # For additional ML algorithms

# Development dependencies (optional):
# pytest>=6.0.0  # For testing
# black>=21.0.0  # For code formatting
# flake8>=3.8.0  # For linting 