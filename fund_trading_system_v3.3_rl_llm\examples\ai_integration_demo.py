"""
AI集成演示
展示如何在交易系统中集成LLM分析功能
"""

import sys
import os
import logging
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from analyzers import LLMMarketAnalyzer, NaturalLanguageInterface, MultiDimensionalMarketClassifier
from core.data_structures import DimensionEvaluationResult


class AIIntegrationDemo:
    """AI集成演示类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化AI组件
        self.llm_analyzer = LLMMarketAnalyzer()
        self.nl_interface = NaturalLanguageInterface(self.llm_analyzer)
        self.market_classifier = MultiDimensionalMarketClassifier()
        
        # 模拟市场数据
        self.sample_market_data = self._create_sample_data()
    
    def _create_sample_data(self) -> Dict[str, Any]:
        """创建示例市场数据"""
        # 模拟六大维度评估结果
        evaluations = {
            '趋势': DimensionEvaluationResult(
                dimension_name='趋势',
                state='上升',
                score=0.65,
                confidence=0.8,
                signals=['买入'],
                data_quality='good',
                details={'ma_trend': '上升', 'momentum': '强势'}
            ),
            '波动性': DimensionEvaluationResult(
                dimension_name='波动性',
                state='中等',
                score=0.45,
                confidence=0.7,
                signals=['中性'],
                data_quality='good',
                details={'volatility_level': '中等', 'stability': '稳定'}
            ),
            '流动性': DimensionEvaluationResult(
                dimension_name='流动性',
                state='正常',
                score=0.55,
                confidence=0.75,
                signals=['正常'],
                data_quality='good',
                details={'volume_trend': '正常', 'liquidity_risk': '低'}
            ),
            '情绪': DimensionEvaluationResult(
                dimension_name='情绪',
                state='偏乐观',
                score=0.35,
                confidence=0.6,
                signals=['偏乐观'],
                data_quality='good',
                details={'sentiment_score': '积极', 'fear_greed': '贪婪'}
            ),
            '结构': DimensionEvaluationResult(
                dimension_name='结构',
                state='健康',
                score=0.6,
                confidence=0.8,
                signals=['健康'],
                data_quality='good',
                details={'structure_health': '良好', 'support_resistance': '明确'}
            ),
            '基本面': DimensionEvaluationResult(
                dimension_name='基本面',
                state='中性',
                score=0.4,
                confidence=0.65,
                signals=['中性'],
                data_quality='good',
                details={'fundamentals': '稳定', 'valuation': '合理'}
            )
        }
        
        return {
            'evaluations': evaluations,
            'price_data': {
                'current_price': 1.2345,
                'change_pct': 2.15,
                'volume': 1500000,
                'high': 1.2456,
                'low': 1.2234
            },
            'fund_data': {
                '000001': {
                    'nav': 1.2345,
                    'change_pct': 2.15,
                    'name': '华夏成长混合'
                }
            }
        }
    
    def demo_market_classification(self):
        """演示市场分类功能"""
        print("\n" + "="*50)
        print("🏷️  市场分类演示")
        print("="*50)
        
        try:
            # 进行市场分类
            classification_result = self.market_classifier.classify_market(
                self.sample_market_data['evaluations'],
                fund_code='000001',
                debug=True
            )
            
            print(f"主要分类: {classification_result['primary_classification']}")
            print(f"分类置信度: {classification_result['classification_confidence']:.3f}")
            print(f"分类描述: {classification_result['classification_description']}")
            print(f"市场特征: {', '.join(classification_result['market_characteristics'])}")
            
            if 'classification_reason' in classification_result:
                print(f"\n分类原因:\n{classification_result['classification_reason']}")
            
            return classification_result
            
        except Exception as e:
            print(f"市场分类演示失败: {str(e)}")
            return None
    
    def demo_llm_analysis(self):
        """演示LLM市场分析功能"""
        print("\n" + "="*50)
        print("🤖 LLM市场分析演示")
        print("="*50)
        
        try:
            # 添加分类结果到市场数据
            classification = self.demo_market_classification()
            if classification:
                self.sample_market_data['classification'] = classification
            
            # 进行LLM分析
            analysis_result = self.llm_analyzer.analyze_market_narrative(
                self.sample_market_data,
                fund_code='000001'
            )
            
            print(f"分析类型: {analysis_result.get('analysis_type', 'N/A')}")
            print(f"市场情绪: {analysis_result.get('market_sentiment', 'N/A')}")
            print(f"置信度: {analysis_result.get('confidence_level', 0):.2f}")
            
            print(f"\n市场驱动因素:")
            for driver in analysis_result.get('market_drivers', []):
                print(f"  • {driver}")
            
            print(f"\n风险点:")
            for risk in analysis_result.get('risk_points', []):
                print(f"  • {risk}")
            
            print(f"\n投资机会:")
            for opportunity in analysis_result.get('opportunities', []):
                print(f"  • {opportunity}")
            
            print(f"\n策略建议:")
            print(f"  {analysis_result.get('strategy_suggestion', 'N/A')}")
            
            print(f"\n核心洞察:")
            print(f"  {analysis_result.get('key_insights', 'N/A')}")
            
            return analysis_result
            
        except Exception as e:
            print(f"LLM分析演示失败: {str(e)}")
            return None
    
    def demo_natural_language_queries(self):
        """演示自然语言查询功能"""
        print("\n" + "="*50)
        print("💬 自然语言查询演示")
        print("="*50)
        
        # 准备上下文数据
        context_data = {
            'fund_data': self.sample_market_data['fund_data'],
            'market_analysis': {
                'market_sentiment': '积极',
                'market_drivers': ['技术面改善', '资金流入'],
                'risk_points': ['波动性增加', '外部不确定性']
            },
            'technical_indicators': {
                'ma5': 1.23,
                'ma20': 1.21,
                'rsi': 65.5
            }
        }
        
        # 示例查询
        sample_queries = [
            "当前市场状态如何？",
            "基金000001的表现怎么样？",
            "现在适合买入吗？",
            "有什么风险需要注意？"
        ]
        
        for query in sample_queries:
            print(f"\n用户查询: {query}")
            print("-" * 30)
            
            try:
                result = self.nl_interface.process_query(query, context_data)
                
                if result['success']:
                    print(f"查询类型: {result['query_type']}")
                    print(f"提取信息: {result['extracted_info']}")
                    print(f"AI回答: {result['response']}")
                else:
                    print(f"查询失败: {result.get('error', '未知错误')}")
                    print(f"兜底回答: {result['response']}")
                    
            except Exception as e:
                print(f"查询处理异常: {str(e)}")
    
    def demo_decision_explanation(self):
        """演示决策解释功能"""
        print("\n" + "="*50)
        print("📝 决策解释演示")
        print("="*50)
        
        # 模拟决策数据
        decision_data = {
            'decision_type': '买入',
            'fund_code': '000001',
            'decision_confidence': 0.75,
            'key_factors': [
                '趋势向上突破',
                '成交量放大',
                '技术指标转强'
            ],
            'risk_factors': [
                '市场波动性增加',
                '外部环境不确定'
            ]
        }
        
        try:
            explanation = self.llm_analyzer.generate_decision_explanation(decision_data)
            print(f"决策: {decision_data['decision_type']} {decision_data['fund_code']}")
            print(f"置信度: {decision_data['decision_confidence']:.2f}")
            print(f"\nAI解释:")
            print(explanation)
            
        except Exception as e:
            print(f"决策解释生成失败: {str(e)}")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("🚀 AI集成功能完整演示")
        print("="*60)
        
        # 检查LLM服务状态
        if self.llm_analyzer.client:
            print("✅ LLM服务连接正常")
        else:
            print("⚠️  LLM服务不可用，将使用兜底功能")
        
        # 运行各项演示
        self.demo_market_classification()
        self.demo_llm_analysis()
        self.demo_natural_language_queries()
        self.demo_decision_explanation()
        
        print("\n" + "="*60)
        print("🎉 AI集成演示完成！")
        print("="*60)
        
        # 显示建议的下一步
        print("\n📋 建议的下一步:")
        print("1. 配置正确的API密钥以启用LLM功能")
        print("2. 集成到现有的交易决策流程中")
        print("3. 添加更多的市场数据源")
        print("4. 优化提示词以提高分析质量")
        print("5. 添加用户反馈机制以持续改进")


def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行演示
    demo = AIIntegrationDemo()
    demo.run_full_demo()


if __name__ == "__main__":
    main()