"""
环境测试脚本 - 检查各种深度强化学习库的可用性
"""

import sys
import importlib

def test_import(module_name, description=""):
    """测试模块导入"""
    try:
        importlib.import_module(module_name)
        print(f"✓ {module_name} - {description}")
        return True
    except ImportError as e:
        print(f"✗ {module_name} - {description} (错误: {e})")
        return False

def test_parl_environment():
    """测试PARL环境"""
    print("\n=== 测试PARL环境 ===")
    
    success = True
    success &= test_import("paddle", "PaddlePaddle深度学习框架")
    success &= test_import("parl", "PARL强化学习框架")
    success &= test_import("gym", "OpenAI Gym环境")
    
    if success:
        try:
            import pyarrow
            print(f"✓ PyArrow版本: {pyarrow.__version__}")
            
            # 测试PARL的关键功能
            import parl
            from parl.algorithms import SAC
            print("✓ PARL SAC算法导入成功")
            
            print("🎉 PARL环境完全可用!")
            return True
            
        except Exception as e:
            print(f"✗ PARL环境测试失败: {e}")
            return False
    else:
        print("❌ PARL环境不可用")
        return False

def test_sb3_environment():
    """测试Stable-Baselines3环境"""
    print("\n=== 测试Stable-Baselines3环境 ===")
    
    success = True
    success &= test_import("stable_baselines3", "Stable-Baselines3强化学习库")
    success &= test_import("gymnasium", "Gymnasium环境库")
    success &= test_import("torch", "PyTorch深度学习框架")
    
    if success:
        try:
            from stable_baselines3 import PPO, SAC, A2C
            print("✓ SB3算法导入成功 (PPO, SAC, A2C)")
            
            import gymnasium as gym
            env = gym.make("CartPole-v1")
            print("✓ Gymnasium环境创建成功")
            
            print("🎉 Stable-Baselines3环境完全可用!")
            return True
            
        except Exception as e:
            print(f"✗ SB3环境测试失败: {e}")
            return False
    else:
        print("❌ Stable-Baselines3环境不可用")
        return False

def test_ray_environment():
    """测试Ray RLlib环境"""
    print("\n=== 测试Ray RLlib环境 ===")
    
    success = True
    success &= test_import("ray", "Ray分布式计算框架")
    
    if success:
        try:
            from ray.rllib.algorithms.ppo import PPOConfig
            print("✓ Ray RLlib PPO算法导入成功")
            
            print("🎉 Ray RLlib环境可用!")
            return True
            
        except Exception as e:
            print(f"✗ Ray RLlib环境测试失败: {e}")
            return False
    else:
        print("❌ Ray RLlib环境不可用")
        return False

def test_basic_dependencies():
    """测试基础依赖"""
    print("=== 测试基础依赖 ===")
    
    test_import("numpy", "数值计算库")
    test_import("pandas", "数据处理库")
    test_import("matplotlib", "绘图库")

def recommend_solution():
    """推荐解决方案"""
    print("\n=== 推荐解决方案 ===")
    
    parl_ok = test_parl_environment()
    sb3_ok = test_sb3_environment()
    ray_ok = test_ray_environment()
    
    if parl_ok:
        print("\n🎯 推荐使用PARL:")
        print("   python enhanced_rl_stock_base.py")
        
    elif sb3_ok:
        print("\n🎯 推荐使用Stable-Baselines3:")
        print("   python sb3_stock_trading.py")
        
    elif ray_ok:
        print("\n🎯 推荐使用Ray RLlib:")
        print("   需要创建Ray版本的交易环境")
        
    else:
        print("\n❌ 所有强化学习库都不可用")
        print("请运行: python install_requirements.py")

def main():
    """主函数"""
    print("深度强化学习环境测试")
    print("=" * 50)
    
    # 测试基础依赖
    test_basic_dependencies()
    
    # 测试各种RL库
    recommend_solution()
    
    print("\n" + "=" * 50)
    print("测试完成!")

if __name__ == "__main__":
    main()