#!/usr/bin/env python3
"""
清理 Stable-Baselines3 依赖的脚本
移除所有 SB3 相关的文件和依赖项
"""

import os
import sys
import subprocess
from pathlib import Path

def remove_sb3_files():
    """移除 SB3 相关的文件"""
    sb3_files = [
        'enhanced_rl_stock_base_sb3.py',
        'enhanced_rl_stock_base_sb3_compatible.py', 
        'sb3_stock_trading.py',
        'install_sb3_requirements.py',
        'README_SB3.md',
        'test_sb3_environment.py'
    ]
    
    removed_files = []
    for file_path in sb3_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_files.append(file_path)
                print(f"✓ 已删除: {file_path}")
            except Exception as e:
                print(f"✗ 删除失败 {file_path}: {e}")
        else:
            print(f"- 文件不存在: {file_path}")
    
    return removed_files

def uninstall_sb3_packages():
    """卸载 SB3 相关的包"""
    sb3_packages = [
        'stable-baselines3',
        'stable-baselines3[extra]',
        'sb3-contrib',
        'tensorboard'
    ]
    
    uninstalled_packages = []
    for package in sb3_packages:
        try:
            print(f"正在卸载 {package}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'uninstall', package, '-y'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                uninstalled_packages.append(package)
                print(f"✓ 已卸载: {package}")
            else:
                print(f"- {package} 未安装或卸载失败")
        except Exception as e:
            print(f"✗ 卸载 {package} 时出错: {e}")
    
    return uninstalled_packages

def clean_requirements_file():
    """清理 requirements 文件中的 SB3 依赖"""
    requirements_files = ['requirements.txt', 'requirements_backup.txt']
    
    sb3_dependencies = [
        'stable-baselines3',
        'stable_baselines3',
        'sb3-contrib',
        'tensorboard',
        'gym[atari]',
        'gym[box2d]',
        'gym[classic_control]'
    ]
    
    for req_file in requirements_files:
        if os.path.exists(req_file):
            try:
                with open(req_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 过滤掉 SB3 相关的依赖
                filtered_lines = []
                removed_deps = []
                
                for line in lines:
                    line_clean = line.strip().lower()
                    should_remove = False
                    
                    for dep in sb3_dependencies:
                        if dep.lower() in line_clean:
                            should_remove = True
                            removed_deps.append(line.strip())
                            break
                    
                    if not should_remove:
                        filtered_lines.append(line)
                
                # 写回文件
                if removed_deps:
                    with open(req_file, 'w', encoding='utf-8') as f:
                        f.writelines(filtered_lines)
                    
                    print(f"✓ 已清理 {req_file}，移除了以下依赖:")
                    for dep in removed_deps:
                        print(f"  - {dep}")
                else:
                    print(f"- {req_file} 中没有找到 SB3 依赖")
                    
            except Exception as e:
                print(f"✗ 处理 {req_file} 时出错: {e}")
        else:
            print(f"- 文件不存在: {req_file}")

def create_clean_requirements():
    """创建清理后的 requirements.txt"""
    clean_requirements = """# 基础科学计算库
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习库
torch>=1.9.0
scikit-learn>=1.0.0

# 强化学习环境
gymnasium>=0.26.0
gym>=0.21.0

# 数据可视化
matplotlib>=3.4.0
seaborn>=0.11.0
plotly>=5.0.0

# 金融数据
tushare>=1.2.0
yfinance>=0.1.70
akshare>=1.8.0

# 工具库
tqdm>=4.62.0
loguru>=0.6.0
pyyaml>=6.0
python-dotenv>=0.19.0

# 技术分析
talib-binary>=0.4.24
"""
    
    try:
        with open('requirements_clean.txt', 'w', encoding='utf-8') as f:
            f.write(clean_requirements)
        print("✓ 已创建 requirements_clean.txt")
    except Exception as e:
        print(f"✗ 创建 requirements_clean.txt 失败: {e}")

def verify_cleanup():
    """验证清理结果"""
    print("\n" + "="*50)
    print("验证清理结果...")
    print("="*50)
    
    # 检查是否还有 SB3 相关的导入
    python_files = list(Path('.').glob('*.py'))
    sb3_imports = []
    
    for py_file in python_files:
        if py_file.name.startswith('cleanup_'):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'stable_baselines3' in content or 'from stable_baselines3' in content:
                sb3_imports.append(py_file.name)
        except Exception as e:
            print(f"检查 {py_file} 时出错: {e}")
    
    if sb3_imports:
        print("⚠️  以下文件仍包含 SB3 导入:")
        for file in sb3_imports:
            print(f"  - {file}")
    else:
        print("✓ 所有 Python 文件已清理完成")
    
    # 检查已安装的包
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'list'
        ], capture_output=True, text=True)
        
        if 'stable-baselines3' in result.stdout:
            print("⚠️  stable-baselines3 仍然安装")
        else:
            print("✓ stable-baselines3 已完全卸载")
            
    except Exception as e:
        print(f"检查安装包时出错: {e}")

def main():
    """主函数"""
    print("开始清理 Stable-Baselines3 依赖...")
    print("="*50)
    
    # 1. 移除 SB3 相关文件
    print("\n1. 移除 SB3 相关文件:")
    removed_files = remove_sb3_files()
    
    # 2. 卸载 SB3 包
    print("\n2. 卸载 SB3 相关包:")
    uninstalled_packages = uninstall_sb3_packages()
    
    # 3. 清理 requirements 文件
    print("\n3. 清理 requirements 文件:")
    clean_requirements_file()
    
    # 4. 创建清理后的 requirements
    print("\n4. 创建清理后的 requirements:")
    create_clean_requirements()
    
    # 5. 验证清理结果
    verify_cleanup()
    
    print("\n" + "="*50)
    print("清理完成！")
    print("="*50)
    print(f"移除文件: {len(removed_files)} 个")
    print(f"卸载包: {len(uninstalled_packages)} 个")
    print("\n推荐操作:")
    print("1. 使用 enhanced_rl_stock_base_no_sb3.py 作为基础环境")
    print("2. 安装清理后的依赖: pip install -r requirements_clean.txt")
    print("3. 运行测试: python enhanced_rl_stock_base_no_sb3.py --test")

if __name__ == "__main__":
    main()