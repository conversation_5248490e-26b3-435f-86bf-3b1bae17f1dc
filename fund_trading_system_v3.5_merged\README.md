# Trading System v3.5 多智能体量化平台

## 项目简介
本系统基于多智能体架构，集成RL、LLM、CZSC三大智能体，支持多策略融合、风险管理、批量实验与自动化分析，适用于量化研究、策略开发与风控实验。

## 主要特性
- **多智能体融合**：RL（强化学习）、LLM（大语言模型）、CZSC（缠论）三大智能体结构化协作
- **多策略融合**：支持加权、投票、置信度覆盖、保守等多种决策融合策略
- **风险管理**：内置VaR、CVaR、动态止损等参数化风控接口，便于扩展
- **批量实验**：一键批量回测多组参数，自动输出结构化对比报告
- **可扩展接口**：所有Agent、风控、融合策略均为结构化接口，便于二次开发

## 目录结构
- `agents/`：RL/LLM/CZSC智能体及其结构化接口
- `coordinators/`：多智能体协调与融合策略实验
- `core/`：配置、数据结构、工具
- `backtest/engines/`：回测引擎、数据管理、性能分析
- `scripts/`：训练、批量实验、数据生成等自动化脚本
- `output/`：实验结果与报告

## 快速开始

### 1. 环境准备

**系统要求**:
- Python 3.8+
- 8GB+ RAM (推荐16GB)
- 支持CUDA的GPU (可选，用于RL训练)

**安装依赖**:
```bash
# 基础依赖 (必需)
pip install -r requirements.txt

# 可选依赖 (用于高级功能)
pip install torch torchvision  # RL功能
pip install transformers       # LLM功能
pip install akshare tushare   # 真实数据源
```

### 2. 系统验证
```bash
# 运行快速测试验证系统状态
python quick_test.py

# 预期输出: "🎉 All tests passed! Trading System v3.5 is ready to use."
```

### 3. 基础演示模式
```bash
# 运行默认演示
python main.py

# 或明确指定演示模式
python main.py --mode demo
```

### 3. 回测模式
```bash
# 基础回测
python main.py --mode backtest --symbol 000001.SZ

# 指定时间范围的回测
python main.py --mode backtest --symbol 000001.SZ \
  --start-date 2020-01-01 --end-date 2023-12-31

# 带输出和图表的回测
python main.py --mode backtest --symbol 000001.SZ \
  --output ./results --charts
```

### 4. 交易模式
```bash
# 基础交易模式
python main.py --mode trade --symbol 000001.SZ

# 指定风险容忍度
python main.py --mode trade --symbol 000001.SZ --risk-tolerance HIGH
```

### 5. 运行测试
```bash
python quick_test.py
```

### 6. 编程接口使用

**基础API使用**:
```python
from coordinators.trading_agent import TradingAgent, AgentWeight
from datetime import datetime

# 创建交易代理
weights = AgentWeight(rl_weight=0.4, llm_weight=0.3, czsc_weight=0.3)
agent = TradingAgent(weights, risk_tolerance="MEDIUM")

# 分析并获取交易建议
recommendation = agent.analyze_and_recommend(
    symbol="000001.SZ",
    price_data={
        'open': 15.20, 'high': 15.45,
        'low': 15.10, 'close': 15.35,
        'volume': 2500000
    }
)

print(f"建议操作: {recommendation.action}")
print(f"置信度: {recommendation.confidence:.2f}")
print(f"风险等级: {recommendation.risk_level}")
```

### 7. 高级功能
1. **准备数据**
   - 运行 `python scripts/create_sample_data.py --symbol 000001.SZ --start-date 2020-01-01 --end-date 2020-12-31`
2. **RL模型训练**
   - 运行 `python scripts/train_rl_model.py --episodes 100 --symbol 000001.SZ`
3. **批量实验**
   - 运行 `python scripts/experiment_batch.py`
   - 结果自动保存至 `output/batch_experiment/experiment_report_*.csv`

## 多智能体与融合策略用法
- 可在 `coordinators/multi_agent_coordinator.py` 中自定义融合策略
- RL/LLM/CZSC Agent均支持多模型/多API/多特征扩展（见各自`agents/`目录）
- 批量实验脚本可自动遍历多组融合与风控参数

## 风险管理参数说明
- `var_confidence`：VaR置信度（如0.95）
- `cvar_confidence`：CVaR置信度
- `dynamic_stop_loss`：是否启用动态止损
- `risk_model`：风控策略名（basic/advanced/custom）
- 可在`core/config.py`和回测脚本中灵活配置

## 典型扩展指引
- **自定义Agent**：继承BaseAgent，实现`generate_signal`等接口
- **自定义融合策略**：在`MultiAgentCoordinator`中扩展`_fuse_signals`方法
- **高级风控**：在`_apply_risk_management`中实现VaR、CVaR、动态止损等逻辑
- **Web/API集成**：可在`web/`或`api/`目录预留入口，集成前端或服务化

## 参考文档
- `docs/PRD.md`：产品需求与架构说明
- `docs/PROJECT_STATUS.md`：项目进度与状态
- `docs/TODO_LIST.md`：开发任务清单

## 联系与支持
如有问题或需求，欢迎提交Issue或联系开发团队。 