"""
评估报告生成器 - 生成详细的RL模型评估报告
支持多种格式输出、可视化图表、对比分析等功能
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, asdict
import numpy as np

# 尝试导入可选依赖
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_pdf import PdfPages
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import seaborn as sns
    SEABORN_AVAILABLE = True
except ImportError:
    SEABORN_AVAILABLE = False

from .advanced_evaluator import EvaluationResult, AdvancedEvaluator


@dataclass
class ReportConfig:
    """报告配置"""
    title: str = "RL模型评估报告"
    author: str = "Fund Trading System"
    include_charts: bool = True
    include_detailed_metrics: bool = True
    include_trade_analysis: bool = True
    include_risk_analysis: bool = True
    include_benchmark_comparison: bool = True
    chart_style: str = "seaborn"  # matplotlib style
    output_formats: List[str] = None  # ['html', 'pdf', 'json']
    
    def __post_init__(self):
        if self.output_formats is None:
            self.output_formats = ['html', 'json']


class EvaluationReportGenerator:
    """评估报告生成器"""
    
    def __init__(self, config: ReportConfig = None):
        self.config = config or ReportConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置图表样式
        if MATPLOTLIB_AVAILABLE:
            try:
                plt.style.use(self.config.chart_style)
            except:
                plt.style.use('default')
        
        if SEABORN_AVAILABLE:
            sns.set_palette("husl")
    
    def generate_report(self, evaluation_result: EvaluationResult, 
                       output_dir: str, filename_prefix: str = "evaluation_report") -> Dict[str, str]:
        """生成评估报告"""
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            generated_files = {}
            
            # 生成不同格式的报告
            for format_type in self.config.output_formats:
                if format_type == 'html':
                    filepath = self._generate_html_report(evaluation_result, output_dir, filename_prefix)
                    generated_files['html'] = filepath
                elif format_type == 'pdf':
                    filepath = self._generate_pdf_report(evaluation_result, output_dir, filename_prefix)
                    generated_files['pdf'] = filepath
                elif format_type == 'json':
                    filepath = self._generate_json_report(evaluation_result, output_dir, filename_prefix)
                    generated_files['json'] = filepath
                elif format_type == 'markdown':
                    filepath = self._generate_markdown_report(evaluation_result, output_dir, filename_prefix)
                    generated_files['markdown'] = filepath
            
            self.logger.info(f"评估报告生成完成，输出目录: {output_dir}")
            return generated_files
            
        except Exception as e:
            self.logger.error(f"生成评估报告失败: {str(e)}")
            raise
    
    def _generate_html_report(self, evaluation_result: EvaluationResult, 
                            output_dir: str, filename_prefix: str) -> str:
        """生成HTML报告"""
        filepath = os.path.join(output_dir, f"{filename_prefix}.html")
        
        html_content = self._create_html_content(evaluation_result)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return filepath
    
    def _create_html_content(self, evaluation_result: EvaluationResult) -> str:
        """创建HTML内容"""
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.config.title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .metric-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
        .metric-card {{ background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
        .metric-label {{ font-size: 14px; color: #7f8c8d; }}
        .positive {{ color: #27ae60; }}
        .negative {{ color: #e74c3c; }}
        .neutral {{ color: #f39c12; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .chart-container {{ text-align: center; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>{self.config.title}</h1>
        <p><strong>生成时间:</strong> {evaluation_result.timestamp}</p>
        <p><strong>作者:</strong> {self.config.author}</p>
        <p><strong>样本数量:</strong> {evaluation_result.total_samples:,}</p>
    </div>
"""
        
        # 性能指标概览
        html += self._create_performance_overview_html(evaluation_result.performance_metrics)
        
        # 风险指标
        html += self._create_risk_metrics_html(evaluation_result.risk_metrics)
        
        # 交易统计
        if self.config.include_trade_analysis:
            html += self._create_trade_statistics_html(evaluation_result.trade_statistics)
        
        # 基准比较
        if self.config.include_benchmark_comparison and evaluation_result.benchmark_comparison:
            html += self._create_benchmark_comparison_html(evaluation_result.benchmark_comparison)
        
        # 周期分析
        if evaluation_result.period_analysis:
            html += self._create_period_analysis_html(evaluation_result.period_analysis)
        
        # 置信区间
        if evaluation_result.confidence_intervals:
            html += self._create_confidence_intervals_html(evaluation_result.confidence_intervals)
        
        html += """
</body>
</html>
"""
        return html
    
    def _create_performance_overview_html(self, metrics: Dict[str, float]) -> str:
        """创建性能概览HTML"""
        html = """
    <div class="section">
        <h2>📈 性能指标概览</h2>
        <div class="metric-grid">
"""
        
        metric_configs = [
            ('total_return', '总收益率', '%', 'positive' if metrics.get('total_return', 0) > 0 else 'negative'),
            ('annualized_return', '年化收益率', '%', 'positive' if metrics.get('annualized_return', 0) > 0 else 'negative'),
            ('sharpe_ratio', '夏普比率', '', 'positive' if metrics.get('sharpe_ratio', 0) > 1 else 'neutral'),
            ('sortino_ratio', '索提诺比率', '', 'positive' if metrics.get('sortino_ratio', 0) > 1 else 'neutral'),
            ('win_rate', '胜率', '%', 'positive' if metrics.get('win_rate', 0) > 0.5 else 'negative'),
            ('profit_factor', '盈亏比', '', 'positive' if metrics.get('profit_factor', 0) > 1 else 'negative')
        ]
        
        for key, label, unit, css_class in metric_configs:
            value = metrics.get(key, 0)
            if unit == '%':
                display_value = f"{value:.2%}"
            else:
                display_value = f"{value:.3f}"
            
            html += f"""
            <div class="metric-card">
                <div class="metric-value {css_class}">{display_value}</div>
                <div class="metric-label">{label}</div>
            </div>
"""
        
        html += """
        </div>
    </div>
"""
        return html
    
    def _create_risk_metrics_html(self, metrics: Dict[str, float]) -> str:
        """创建风险指标HTML"""
        html = """
    <div class="section">
        <h2>⚠️ 风险指标</h2>
        <div class="metric-grid">
"""
        
        risk_configs = [
            ('volatility', '年化波动率', '%'),
            ('max_drawdown', '最大回撤', '%'),
            ('var_95', 'VaR (95%)', '%'),
            ('cvar_95', 'CVaR (95%)', '%'),
            ('downside_deviation', '下行偏差', '%'),
            ('calmar_ratio', '卡尔马比率', '')
        ]
        
        for key, label, unit in risk_configs:
            if key in metrics:
                value = metrics[key]
                if unit == '%':
                    display_value = f"{value:.2%}"
                else:
                    display_value = f"{value:.3f}"
                
                # 风险指标的颜色逻辑
                if key in ['max_drawdown', 'var_95', 'cvar_95', 'volatility', 'downside_deviation']:
                    css_class = 'negative' if abs(value) > 0.1 else 'neutral'
                else:
                    css_class = 'positive' if value > 1 else 'neutral'
                
                html += f"""
            <div class="metric-card">
                <div class="metric-value {css_class}">{display_value}</div>
                <div class="metric-label">{label}</div>
            </div>
"""
        
        html += """
        </div>
    </div>
"""
        return html
    
    def _create_trade_statistics_html(self, stats: Dict[str, Any]) -> str:
        """创建交易统计HTML"""
        html = """
    <div class="section">
        <h2>📊 交易统计</h2>
        <table>
            <tr><th>指标</th><th>数值</th></tr>
"""
        
        trade_metrics = [
            ('total_trades', '总交易次数', ''),
            ('winning_trades', '盈利交易', ''),
            ('losing_trades', '亏损交易', ''),
            ('average_trade_return', '平均交易收益', '%'),
            ('average_winning_trade', '平均盈利交易', '%'),
            ('average_losing_trade', '平均亏损交易', '%'),
            ('largest_win', '最大盈利', '%'),
            ('largest_loss', '最大亏损', '%'),
            ('max_consecutive_wins', '最大连续盈利', ''),
            ('max_consecutive_losses', '最大连续亏损', '')
        ]
        
        for key, label, unit in trade_metrics:
            if key in stats:
                value = stats[key]
                if unit == '%':
                    display_value = f"{value:.2%}"
                else:
                    display_value = f"{value}"
                
                html += f"<tr><td>{label}</td><td>{display_value}</td></tr>\n"
        
        html += """
        </table>
    </div>
"""
        return html
    
    def _create_benchmark_comparison_html(self, comparison: Dict[str, float]) -> str:
        """创建基准比较HTML"""
        html = """
    <div class="section">
        <h2>📈 基准比较</h2>
        <table>
            <tr><th>指标</th><th>组合</th><th>基准</th><th>差异</th></tr>
"""
        
        comparisons = [
            ('total_return', 'portfolio_total_return', 'benchmark_total_return', 'excess_return', '总收益率', '%'),
            ('annual_return', 'portfolio_annual_return', 'benchmark_annual_return', 'annual_excess_return', '年化收益率', '%'),
            ('volatility', 'portfolio_volatility', 'benchmark_volatility', None, '波动率', '%'),
            ('sharpe', 'portfolio_sharpe', 'benchmark_sharpe', None, '夏普比率', '')
        ]
        
        for _, portfolio_key, benchmark_key, diff_key, label, unit in comparisons:
            if portfolio_key in comparison and benchmark_key in comparison:
                portfolio_val = comparison[portfolio_key]
                benchmark_val = comparison[benchmark_key]
                
                if unit == '%':
                    portfolio_str = f"{portfolio_val:.2%}"
                    benchmark_str = f"{benchmark_val:.2%}"
                else:
                    portfolio_str = f"{portfolio_val:.3f}"
                    benchmark_str = f"{benchmark_val:.3f}"
                
                if diff_key and diff_key in comparison:
                    diff_val = comparison[diff_key]
                    if unit == '%':
                        diff_str = f"{diff_val:.2%}"
                    else:
                        diff_str = f"{diff_val:.3f}"
                    
                    diff_class = 'positive' if diff_val > 0 else 'negative'
                    diff_cell = f'<span class="{diff_class}">{diff_str}</span>'
                else:
                    diff_cell = '-'
                
                html += f"<tr><td>{label}</td><td>{portfolio_str}</td><td>{benchmark_str}</td><td>{diff_cell}</td></tr>\n"
        
        # 相关性
        if 'correlation' in comparison:
            corr = comparison['correlation']
            html += f"<tr><td>相关性</td><td colspan='3'>{corr:.3f}</td></tr>\n"
        
        html += """
        </table>
    </div>
"""
        return html

    def _create_period_analysis_html(self, period_analysis: Dict[str, Dict[str, float]]) -> str:
        """创建周期分析HTML"""
        html = """
    <div class="section">
        <h2>📅 周期分析</h2>
"""

        for period, metrics in period_analysis.items():
            html += f"""
        <h3>{period.title()}分析</h3>
        <table>
            <tr><th>指标</th><th>数值</th></tr>
"""
            for key, value in metrics.items():
                if key == 'positive_months' or key == 'positive_quarters' or key == 'positive_years':
                    display_value = f"{value:.1%}"
                    label = key.replace('positive_', '').replace('_', ' ').title() + ' 正收益比例'
                else:
                    display_value = f"{value:.2%}" if abs(value) < 1 else f"{value:.3f}"
                    label = key.replace('_', ' ').title()

                html += f"<tr><td>{label}</td><td>{display_value}</td></tr>\n"

            html += "</table>\n"

        html += "</div>\n"
        return html

    def _create_confidence_intervals_html(self, intervals: Dict[str, Tuple[float, float]]) -> str:
        """创建置信区间HTML"""
        html = """
    <div class="section">
        <h2>📊 置信区间 (95%)</h2>
        <table>
            <tr><th>指标</th><th>下限</th><th>上限</th></tr>
"""

        for key, (lower, upper) in intervals.items():
            label = key.replace('_', ' ').title()
            if 'return' in key.lower():
                lower_str = f"{lower:.2%}"
                upper_str = f"{upper:.2%}"
            else:
                lower_str = f"{lower:.3f}"
                upper_str = f"{upper:.3f}"

            html += f"<tr><td>{label}</td><td>{lower_str}</td><td>{upper_str}</td></tr>\n"

        html += """
        </table>
    </div>
"""
        return html

    def _generate_json_report(self, evaluation_result: EvaluationResult,
                            output_dir: str, filename_prefix: str) -> str:
        """生成JSON报告"""
        filepath = os.path.join(output_dir, f"{filename_prefix}.json")

        # 转换为可序列化的字典
        report_data = asdict(evaluation_result)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        return filepath

    def _generate_markdown_report(self, evaluation_result: EvaluationResult,
                                output_dir: str, filename_prefix: str) -> str:
        """生成Markdown报告"""
        filepath = os.path.join(output_dir, f"{filename_prefix}.md")

        md_content = self._create_markdown_content(evaluation_result)

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(md_content)

        return filepath

    def _create_markdown_content(self, evaluation_result: EvaluationResult) -> str:
        """创建Markdown内容"""
        md = f"""# {self.config.title}

**生成时间:** {evaluation_result.timestamp}
**作者:** {self.config.author}
**样本数量:** {evaluation_result.total_samples:,}

## 📈 性能指标概览

| 指标 | 数值 |
|------|------|
"""

        # 性能指标
        perf = evaluation_result.performance_metrics
        performance_items = [
            ('总收益率', perf.get('total_return', 0), '%'),
            ('年化收益率', perf.get('annualized_return', 0), '%'),
            ('夏普比率', perf.get('sharpe_ratio', 0), ''),
            ('索提诺比率', perf.get('sortino_ratio', 0), ''),
            ('胜率', perf.get('win_rate', 0), '%'),
            ('盈亏比', perf.get('profit_factor', 0), '')
        ]

        for label, value, unit in performance_items:
            if unit == '%':
                display_value = f"{value:.2%}"
            else:
                display_value = f"{value:.3f}"
            md += f"| {label} | {display_value} |\n"

        # 风险指标
        md += "\n## ⚠️ 风险指标\n\n| 指标 | 数值 |\n|------|------|\n"

        risk = evaluation_result.risk_metrics
        risk_items = [
            ('年化波动率', risk.get('volatility', 0), '%'),
            ('最大回撤', risk.get('max_drawdown', 0), '%'),
            ('VaR (95%)', risk.get('var_95', 0), '%'),
            ('CVaR (95%)', risk.get('cvar_95', 0), '%')
        ]

        for label, value, unit in risk_items:
            if unit == '%':
                display_value = f"{value:.2%}"
            else:
                display_value = f"{value:.3f}"
            md += f"| {label} | {display_value} |\n"

        # 交易统计
        if self.config.include_trade_analysis:
            md += "\n## 📊 交易统计\n\n| 指标 | 数值 |\n|------|------|\n"

            trades = evaluation_result.trade_statistics
            trade_items = [
                ('总交易次数', trades.get('total_trades', 0), ''),
                ('盈利交易', trades.get('winning_trades', 0), ''),
                ('亏损交易', trades.get('losing_trades', 0), ''),
                ('平均交易收益', trades.get('average_trade_return', 0), '%')
            ]

            for label, value, unit in trade_items:
                if unit == '%':
                    display_value = f"{value:.2%}"
                else:
                    display_value = f"{value}"
                md += f"| {label} | {display_value} |\n"

        return md
