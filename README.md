# 现代化强化学习股票交易系统

这是一个基于深度强化学习的股票交易系统，使用现代化的技术栈和算法实现。

## 🚀 主要特性

### 核心组件
- **现代化RL算法**: 使用SAC (Soft Actor-Critic) 算法
- **技术指标支持**: 集成多种技术分析指标
- **完整训练流程**: 包含训练、验证、测试的完整pipeline
- **可视化结果**: 自动生成训练曲线和结果图表

### 技术栈
- **深度学习**: PyTorch 2.5+
- **强化学习环境**: Gymnasium
- **数据处理**: Pandas, NumPy
- **可视化**: Matplotlib
- **Python版本**: 3.9+

## 📁 文件结构

```
├── modern_rl_trading.py          # 现代化RL交易核心模块
├── enhanced_data_processor.py    # 增强数据处理模块
├── integrated_trading_system.py  # 完整交易系统
├── test_modern_trading.py        # 现代化系统测试
├── quick_test_system.py          # 快速测试（含技术指标）
├── simple_test_system.py         # 简化测试（无技术指标）
├── models/                       # 模型保存目录
├── results/                      # 结果保存目录
└── README.md                     # 本文档
```

## 🔧 安装和使用

### 环境要求
```bash
pip install torch gymnasium pandas numpy matplotlib
```

### 快速开始

1. **简单测试**（推荐新手）:
```bash
python simple_test_system.py
```

2. **完整功能测试**:
```bash
python quick_test_system.py
```

3. **自定义配置**:
```python
from integrated_trading_system import TradingSystem

config = {
    'data': {
        'symbol': 'DEMO',
        'length': 1000,
        'train_ratio': 0.7,
        'val_ratio': 0.15
    },
    'env': {
        'initial_balance': 100000,
        'use_technical_indicators': True,
        'transaction_cost': 0.001
    },
    'training': {
        'episodes': 500,
        'batch_size': 64,
        'memory_size': 50000,
        'eval_episodes': 10,
        'save_freq': 50
    }
}

system = TradingSystem(config)
train_results, test_results = system.run_complete_pipeline()
```

## 📊 系统架构

### 1. 数据处理层 (`enhanced_data_processor.py`)
- **数据生成**: 生成真实的股票价格数据
- **技术指标**: 计算SMA, EMA, RSI, MACD, 布林带等
- **特征工程**: 数据归一化和序列化
- **数据分割**: 训练/验证/测试集分割

### 2. 交易环境层 (`modern_rl_trading.py`)
- **基础环境**: `StockTradingEnv` - 标准的Gym环境
- **增强环境**: `EnhancedStockTradingEnv` - 支持技术指标
- **动作空间**: 连续动作 [买入比例, 卖出比例]
- **观察空间**: 市场数据 + 技术指标 + 账户状态
- **奖励函数**: 基于收益率的奖励设计

### 3. 强化学习层
- **算法**: SAC (Soft Actor-Critic)
- **网络结构**: Actor-Critic架构
- **经验回放**: ReplayBuffer
- **目标网络**: 软更新机制

### 4. 系统集成层 (`integrated_trading_system.py`)
- **完整流程**: 数据准备 → 环境创建 → 智能体训练 → 模型评估
- **结果保存**: 自动保存模型、配置和结果
- **可视化**: 训练曲线、收益分布、净值变化

## 🎯 核心算法

### SAC (Soft Actor-Critic)
- **优势**: 样本效率高，稳定性好
- **特点**: 最大熵强化学习，平衡探索与利用
- **网络**: 双Q网络 + 策略网络
- **温度参数**: 自动调节探索程度

### 技术指标
- **趋势指标**: SMA, EMA, MACD
- **震荡指标**: RSI, 随机指标
- **波动指标**: 布林带, 波动率
- **成交量指标**: 成交量比率

## 📈 性能指标

### 训练指标
- **训练得分**: 每轮训练的累积奖励
- **验证得分**: 定期在验证集上的表现
- **收敛性**: 训练曲线的稳定性

### 测试指标
- **收益率**: 最终资产相对初始资产的增长
- **夏普比率**: 风险调整后的收益
- **最大回撤**: 最大的资产损失
- **胜率**: 盈利交易的比例

## 🔍 使用示例

### 基础使用
```python
# 生成数据
from enhanced_data_processor import DataProcessor
processor = DataProcessor()
df = processor.generate_realistic_data(length=1000)

# 创建环境
from integrated_trading_system import EnhancedStockTradingEnv
env = EnhancedStockTradingEnv(df, use_technical_indicators=True)

# 创建智能体
from modern_rl_trading import SAC
agent = SAC(env.observation_space.shape[0], env.action_space.shape[0])

# 训练
# ... 训练代码
```

### 高级配置
```python
# 自定义技术指标
class CustomIndicators(TechnicalIndicators):
    @staticmethod
    def custom_indicator(data):
        # 自定义指标计算
        return data.rolling(10).mean()

# 自定义环境
class CustomTradingEnv(EnhancedStockTradingEnv):
    def _calculate_reward(self, action, info):
        # 自定义奖励函数
        return custom_reward
```

## 🚨 注意事项

1. **数据质量**: 确保输入数据的质量和完整性
2. **参数调优**: 根据具体市场调整超参数
3. **风险控制**: 实盘使用前充分测试
4. **计算资源**: 训练需要一定的计算资源

## 🔄 版本历史

### v2.0 (当前版本)
- ✅ 现代化技术栈 (PyTorch + Gymnasium)
- ✅ SAC算法实现
- ✅ 技术指标集成
- ✅ 完整训练流程
- ✅ 可视化结果

### v1.0 (历史版本)
- 基于PaddlePaddle的实现
- 基础SAC算法
- 简单数据处理

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

---

**免责声明**: 本系统仅用于研究和教育目的，不构成投资建议。实际交易有风险，请谨慎使用。