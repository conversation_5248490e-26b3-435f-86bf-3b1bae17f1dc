"""
Multi-Agent Coordinator Module for Trading System v3.5

This module manages the coordination and collaboration between different trading agents:
- MultiAgentCoordinator: Main coordination logic for agent collaboration
- TradingAgent: Unified trading interface for external systems
"""

from .multi_agent_coordinator import MultiAgentCoordinator
from .trading_agent import TradingAgent

__all__ = ['MultiAgentCoordinator', 'TradingAgent']

__version__ = "3.5.0" 