#!/usr/bin/env python3
"""
Backtest Demo Script

Demonstrates the complete backtesting workflow including data loading,
strategy execution, performance analysis, and report generation.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from pathlib import Path
from datetime import datetime

from core.config import TradingConfig
from backtest.engines.backtest_engine import BacktestEngine, BacktestConfig
from backtest.engines.data_manager import DataManager
from backtest.analyzers.performance_analyzer import PerformanceAnalyzer


def setup_demo_logging():
    """Setup logging for demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def main():
    """Run complete backtest demonstration"""
    setup_demo_logging()
    logger = logging.getLogger("BacktestDemo")
    
    print("🚀 Trading System v3.5 - Backtest Demo")
    print("=" * 50)
    
    try:
        # 1. Initialize configuration
        print("\n📋 Step 1: Loading Configuration")
        config = TradingConfig()
        print(f"✅ Configuration loaded successfully")
        print(f"   Agent weights: RL={config.coordination_config.rl_weight:.2f}, "
              f"LLM={config.coordination_config.llm_weight:.2f}, "
              f"CZSC={config.coordination_config.czsc_weight:.2f}")
        
        # 2. Setup backtest parameters
        print("\n⚙️ Step 2: Setting up Backtest Parameters")
        backtest_config = BacktestConfig(
            start_date="2020-01-02",
            end_date="2020-03-24",
            initial_capital=100000.0,
            commission=0.0003,
            slippage=0.0001
        )
        print(f"✅ Backtest configured:")
        print(f"   Period: {backtest_config.start_date} to {backtest_config.end_date}")
        print(f"   Initial Capital: ${backtest_config.initial_capital:,.2f}")
        print(f"   Commission: {backtest_config.commission:.4f}")
        
        # 3. Initialize components
        print("\n🔧 Step 3: Initializing System Components")
        data_manager = DataManager(data_dir="data")
        backtest_engine = BacktestEngine(config, backtest_config)
        performance_analyzer = PerformanceAnalyzer()
        print("✅ All components initialized")
        
        # 4. Load historical data
        print("\n📊 Step 4: Loading Historical Data")
        symbol = "000001.SZ"
        historical_data = data_manager.load_data(
            symbol=symbol,
            start_date=backtest_config.start_date,
            end_date=backtest_config.end_date
        )
        print(f"✅ Loaded {len(historical_data)} records for {symbol}")
        print(f"   Date range: {historical_data['date'].min()} to {historical_data['date'].max()}")
        print(f"   Price range: ${historical_data['close'].min():.2f} - ${historical_data['close'].max():.2f}")
        
        # 5. Run backtest
        print("\n🔄 Step 5: Running Backtest")
        print("   Executing trading strategy...")
        backtest_result = backtest_engine.run_backtest(historical_data)
        print("✅ Backtest execution completed")
        
        # 6. Analyze performance
        print("\n📈 Step 6: Analyzing Performance")
        performance_metrics = performance_analyzer.analyze_performance(
            equity_curve=backtest_result.equity_curve,
            daily_returns=backtest_result.daily_returns,
            trades=backtest_result.trades
        )
        print("✅ Performance analysis completed")
        
        # 7. Display results
        print("\n🎯 Step 7: Backtest Results")
        print("=" * 40)
        print(f"📊 Portfolio Performance:")
        print(f"   Total Return:      {backtest_result.total_return:>8.2%}")
        print(f"   Annualized Return: {backtest_result.annualized_return:>8.2%}")
        print(f"   Max Drawdown:      {backtest_result.max_drawdown:>8.2%}")
        print(f"   Sharpe Ratio:      {backtest_result.sharpe_ratio:>8.2f}")
        print(f"   Win Rate:          {backtest_result.win_rate:>8.1%}")
        print(f"   Total Trades:      {backtest_result.total_trades:>8d}")
        
        print(f"\n🤖 Agent Performance:")
        for agent, perf in backtest_result.agent_performance.items():
            accuracy = perf['accuracy']
            decisions = perf['total_decisions']
            print(f"   {agent.upper():>4}: {accuracy:>6.1%} accuracy ({decisions:>3d} decisions)")
        
        print(f"\n💰 Capital Summary:")
        print(f"   Initial Capital:   ${backtest_config.initial_capital:>10,.2f}")
        print(f"   Final Capital:     ${backtest_result.final_capital:>10,.2f}")
        print(f"   Total P&L:         ${backtest_result.final_capital - backtest_config.initial_capital:>10,.2f}")
        
        # 8. Generate comprehensive report
        print("\n📋 Step 8: Generating Detailed Report")
        report = performance_analyzer.generate_report(
            metrics=performance_metrics,
            equity_curve=backtest_result.equity_curve,
            daily_returns=backtest_result.daily_returns,
            trades=backtest_result.trades,
            start_date=backtest_config.start_date,
            end_date=backtest_config.end_date,
            initial_capital=backtest_config.initial_capital
        )
        
        # 9. Save results
        output_dir = Path("output/backtest_demo")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = output_dir / f"backtest_report_{symbol}_{timestamp}.json"
        
        performance_analyzer.save_report(report, str(report_file))
        print(f"✅ Detailed report saved to: {report_file}")
        
        # 10. Create visualization (if matplotlib available)
        try:
            chart_file = output_dir / f"backtest_charts_{symbol}_{timestamp}.png"
            dates = [row.date for _, row in historical_data.iterrows()]
            equity_array = backtest_result.equity_curve
            
            # Calculate drawdowns for visualization
            peak = [max(equity_array[:i+1]) for i in range(len(equity_array))]
            drawdowns = [(equity_array[i] - peak[i]) / peak[i] for i in range(len(equity_array))]
            
            chart_path = performance_analyzer.create_charts(
                equity_curve=backtest_result.equity_curve,
                daily_returns=backtest_result.daily_returns,
                drawdowns=drawdowns,
                dates=dates,
                save_path=str(chart_file)
            )
            
            if chart_path:
                print(f"✅ Performance charts saved to: {chart_file}")
            else:
                print("ℹ️  Chart generation skipped (matplotlib not available)")
                
        except Exception as e:
            print(f"⚠️  Chart generation failed: {e}")
        
        # 11. Summary and recommendations
        print("\n🎉 Demo Completed Successfully!")
        print("=" * 50)
        print("📝 Summary:")
        print(f"   • Processed {len(historical_data)} days of market data")
        print(f"   • Generated {backtest_result.total_trades} trading signals")
        print(f"   • Achieved {backtest_result.total_return:.2%} total return")
        print(f"   • System performed {'above' if backtest_result.total_return > 0 else 'below'} expectations")
        
        print(f"\n💡 Next Steps:")
        print(f"   • Review the detailed report: {report_file}")
        print(f"   • Analyze agent performance and adjust weights if needed")
        print(f"   • Test with different time periods and symbols")
        print(f"   • Consider parameter optimization for better performance")
        
        logger.info("Backtest demo completed successfully")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")
        raise


if __name__ == "__main__":
    main() 