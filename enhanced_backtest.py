#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版强化学习回测系统
集成50维状态空间和LLM分析
"""

import os
import sys
import numpy as np
import pandas as pd
import paddle
import parl
from parl.algorithms import SAC
from parl.utils import ReplayMemory
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入增强组件
from enhanced_stock_env import EnhancedStockTradingEnv
from enhanced_rl_models import EnhancedStockModel, AdaptiveRewardShaper
from czsc_func import get_kline

# 尝试导入fund_trading_system
try:
    sys.path.append('fund_trading_system_v3.4_backtest_czsc')
    from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
    FUND_SYSTEM_AVAILABLE = True
except ImportError:
    print("警告: fund_trading_system不可用，将使用简化版特征")
    FUND_SYSTEM_AVAILABLE = False


class EnhancedStockAgent(parl.Agent):
    """增强版股票交易智能体"""
    
    def __init__(self, algorithm, llm_analyzer=None, reward_shaper=None):
        super(EnhancedStockAgent, self).__init__(algorithm)
        self.alg.sync_target(decay=0)
        self.llm_analyzer = llm_analyzer
        self.reward_shaper = reward_shaper or AdaptiveRewardShaper()
        
    def predict(self, obs, llm_features=None):
        """预测动作"""
        obs = paddle.to_tensor(obs.reshape(1, -1), dtype='float32')
        
        if hasattr(self.alg.model.actor_model, 'forward') and llm_features is not None:
            # 如果是LLM增强版Actor
            llm_features = paddle.to_tensor(llm_features.reshape(1, -1), dtype='float32')
            action = self.alg.predict(obs, llm_features)
        else:
            action = self.alg.predict(obs)
            
        return action.cpu().numpy()[0]
    
    def sample(self, obs, llm_features=None):
        """采样动作"""
        obs = paddle.to_tensor(obs.reshape(1, -1), dtype='float32')
        
        if hasattr(self.alg.model.actor_model, 'forward') and llm_features is not None:
            llm_features = paddle.to_tensor(llm_features.reshape(1, -1), dtype='float32')
            action, _ = self.alg.sample(obs, llm_features)
        else:
            action, _ = self.alg.sample(obs)
            
        return action.cpu().numpy()[0]
    
    def learn(self, obs, action, reward, next_obs, terminal):
        """学习更新"""
        terminal = np.expand_dims(terminal, -1)
        reward = np.expand_dims(reward, -1)

        obs = paddle.to_tensor(obs, dtype='float32')
        action = paddle.to_tensor(action, dtype='float32')
        reward = paddle.to_tensor(reward, dtype='float32')
        next_obs = paddle.to_tensor(next_obs, dtype='float32')
        terminal = paddle.to_tensor(terminal, dtype='float32')
        
        critic_loss, actor_loss = self.alg.learn(obs, action, reward, next_obs, terminal)
        return critic_loss, actor_loss


class EnhancedBacktestSystem:
    """增强版回测系统"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = self._setup_logger()
        
        # 初始化fund_trading_system (如果可用)
        self.fund_system = None
        if FUND_SYSTEM_AVAILABLE:
            try:
                self.fund_system = MultiAgentCoordinatorV3()
                self.logger.info("Fund trading system初始化成功")
            except Exception as e:
                self.logger.warning(f"Fund trading system初始化失败: {e}")
        
        # 初始化组件
        self.reward_shaper = AdaptiveRewardShaper()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('EnhancedBacktest')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def prepare_data(self, fund_code: str = '518880', freq: str = '15min') -> pd.DataFrame:
        """准备训练数据"""
        self.logger.info(f"准备数据: {fund_code}, 频率: {freq}")
        
        try:
            # 获取K线数据
            df = get_kline(fund_code, freq=freq)
            df['volume'] = df['vol']
            df['amount'] = df['close'] * df['volume']
            df['date'] = pd.to_datetime(df['dt'], format='%Y-%m-%d')
            
            self.logger.info(f"数据准备完成，共{len(df)}条记录")
            return df
            
        except Exception as e:
            self.logger.error(f"数据准备失败: {e}")
            raise
    
    def create_environments(self, df: pd.DataFrame, train_ratio: float = 0.7):
        """创建训练和测试环境"""
        split_point = int(len(df) * train_ratio)
        
        train_df = df[:split_point].reset_index(drop=True)
        test_df = df[split_point:].reset_index(drop=True)
        
        train_env = EnhancedStockTradingEnv(
            train_df, 
            fund_trading_system=self.fund_system
        )
        
        test_env = EnhancedStockTradingEnv(
            test_df,
            fund_trading_system=self.fund_system
        )
        
        self.logger.info(f"环境创建完成 - 训练集: {len(train_df)}, 测试集: {len(test_df)}")
        return train_env, test_env
    
    def create_agent(self):
        """创建智能体"""
        # 创建模型
        model = EnhancedStockModel(
            obs_dim=self.config['obs_dim'],
            action_dim=self.config['action_dim']
        )
        
        # 创建算法
        algorithm = SAC(
            model,
            gamma=self.config['gamma'],
            tau=self.config['tau'],
            alpha=self.config['alpha'],
            actor_lr=self.config['actor_lr'],
            critic_lr=self.config['critic_lr']
        )
        
        # 创建智能体
        agent = EnhancedStockAgent(
            algorithm,
            reward_shaper=self.reward_shaper
        )
        
        self.logger.info("智能体创建完成")
        return agent
    
    def get_llm_features(self, fund_code: str, current_step: int) -> Optional[np.ndarray]:
        """获取LLM分析特征"""
        if not self.fund_system:
            return None
            
        try:
            # 获取LLM分析结果
            analysis_result = self.fund_system.coordinate_analysis(fund_code)
            llm_analysis = analysis_result.get('llm_analysis', {})
            
            if 'error' in llm_analysis:
                return None
            
            # 提取LLM特征向量
            features = []
            
            # 置信度
            features.append(llm_analysis.get('confidence_level', 0.5))
            
            # 市场情绪 (编码)
            sentiment_map = {'积极': 1.0, '中性': 0.5, '谨慎': 0.0}
            sentiment = llm_analysis.get('market_sentiment', '中性')
            features.append(sentiment_map.get(sentiment, 0.5))
            
            # 驱动因素数量 (归一化)
            drivers_count = len(llm_analysis.get('market_drivers', []))
            features.append(min(1.0, drivers_count / 5))
            
            # 风险点数量 (归一化)
            risks_count = len(llm_analysis.get('risk_points', []))
            features.append(min(1.0, risks_count / 5))
            
            # 机会数量 (归一化)
            opportunities_count = len(llm_analysis.get('opportunities', []))
            features.append(min(1.0, opportunities_count / 5))
            
            # 策略建议长度 (归一化)
            strategy_length = len(llm_analysis.get('strategy_suggestion', ''))
            features.append(min(1.0, strategy_length / 200))
            
            # 填充到10维
            while len(features) < 10:
                features.append(0.5)
            
            return np.array(features[:10], dtype=np.float32)
            
        except Exception as e:
            self.logger.warning(f"LLM特征提取失败: {e}")
            return None
    
    def run_train_episode(self, agent, env, rpm, episode_num: int, fund_code: str = '518880'):
        """运行训练回合"""
        obs = env.reset()
        done = False
        episode_reward = 0
        episode_steps = 0
        
        while not done:
            episode_steps += 1
            
            # 获取LLM特征
            llm_features = self.get_llm_features(fund_code, env.current_step)
            
            # 选择动作
            if rpm.size() < self.config['warmup_steps']:
                action = np.random.uniform(-1, 1, size=env.action_space.shape[0])
            else:
                action = agent.sample(obs, llm_features)
            
            # 动作后处理
            action = (action + 1.0) / 2.0
            
            # 执行动作
            next_obs, base_reward, done, info = env.step(action)
            
            # 奖励塑形
            if llm_features is not None and self.fund_system:
                try:
                    analysis_result = self.fund_system.coordinate_analysis(fund_code)
                    llm_analysis = analysis_result.get('llm_analysis', {})
                    reward = self.reward_shaper.shape_reward(base_reward, llm_analysis)
                except:
                    reward = base_reward
            else:
                reward = base_reward
            
            # 存储经验
            rpm.append(obs, action, reward, next_obs, float(done))
            
            obs = next_obs
            episode_reward += reward
            
            # 训练
            if rpm.size() >= self.config['warmup_steps']:
                batch_obs, batch_action, batch_reward, batch_next_obs, batch_terminal = rpm.sample_batch(
                    self.config['batch_size']
                )
                agent.learn(batch_obs, batch_action, batch_reward, batch_next_obs, batch_terminal)
        
        return episode_reward, episode_steps, info
    
    def run_evaluate_episodes(self, agent, env, eval_episodes: int, fund_code: str = '518880'):
        """运行评估回合"""
        total_reward = 0
        total_profit = 0
        
        for episode in range(eval_episodes):
            obs = env.reset()
            done = False
            episode_reward = 0
            
            while not done:
                llm_features = self.get_llm_features(fund_code, env.current_step)
                action = agent.predict(obs, llm_features)
                obs, reward, done, info = env.step(action)
                episode_reward += reward
            
            total_reward += episode_reward
            total_profit += info['profit']
        
        avg_reward = total_reward / eval_episodes
        avg_profit = total_profit / eval_episodes
        
        return avg_reward, avg_profit
    
    def train(self, fund_code: str = '518880'):
        """执行训练"""
        self.logger.info(f"开始训练 - 基金代码: {fund_code}")
        
        # 准备数据和环境
        df = self.prepare_data(fund_code)
        train_env, eval_env = self.create_environments(df)
        
        # 创建智能体和经验池
        agent = self.create_agent()
        rpm = ReplayMemory(
            max_size=self.config['memory_size'],
            obs_dim=self.config['obs_dim'],
            act_dim=self.config['action_dim']
        )
        
        # 训练循环
        best_reward = -float('inf')
        total_steps = 0
        episode_num = 0
        
        while total_steps < self.config['max_train_steps']:
            episode_num += 1
            
            # 训练回合
            episode_reward, episode_steps, info = self.run_train_episode(
                agent, train_env, rpm, episode_num, fund_code
            )
            total_steps += episode_steps
            
            self.logger.info(f"Episode {episode_num}: Reward={episode_reward:.3f}, Steps={episode_steps}")
            if info.get('status'):
                self.logger.info(f"Status: {info['status']}")
            
            # 定期评估
            if episode_num % self.config['eval_freq'] == 0:
                avg_reward, avg_profit = self.run_evaluate_episodes(
                    agent, eval_env, self.config['eval_episodes'], fund_code
                )
                
                self.logger.info(f"Evaluation - Avg Reward: {avg_reward:.3f}, Avg Profit: {avg_profit:.2f}")
                
                # 保存最佳模型
                if avg_reward > best_reward:
                    best_reward = avg_reward
                    model_path = f"./models/enhanced_sac_{fund_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ckpt"
                    agent.save(model_path)
                    self.logger.info(f"保存最佳模型: {model_path}")
        
        self.logger.info("训练完成")
        return agent
    
    def test(self, agent, fund_code: str = '518880', model_path: str = None):
        """测试模型"""
        if model_path:
            agent.restore(model_path)
            self.logger.info(f"加载模型: {model_path}")
        
        # 准备测试数据
        df = self.prepare_data(fund_code)
        _, test_env = self.create_environments(df)
        
        # 运行测试
        avg_reward, avg_profit = self.run_evaluate_episodes(
            agent, test_env, self.config['test_episodes'], fund_code
        )
        
        self.logger.info(f"测试结果 - 平均奖励: {avg_reward:.3f}, 平均收益: {avg_profit:.2f}")
        
        return avg_reward, avg_profit


def create_default_config():
    """创建默认配置"""
    return {
        'obs_dim': 50,
        'action_dim': 2,
        'gamma': 0.995,
        'tau': 0.005,
        'alpha': 0.2,
        'actor_lr': 1e-4,
        'critic_lr': 1e-4,
        'batch_size': 64,
        'memory_size': int(1e5),
        'warmup_steps': 1000,
        'max_train_steps': int(5e4),
        'eval_freq': 10,
        'eval_episodes': 3,
        'test_episodes': 5
    }


def main():
    """主函数"""
    # 创建配置
    config = create_default_config()
    
    # 创建回测系统
    backtest_system = EnhancedBacktestSystem(config)
    
    # 训练
    agent = backtest_system.train(fund_code='518880')
    
    # 测试
    backtest_system.test(agent, fund_code='518880')


if __name__ == "__main__":
    main()