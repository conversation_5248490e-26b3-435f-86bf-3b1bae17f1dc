#!/usr/bin/env python3
"""
Trading System v3.5 Main Entry Point

Supports both demo mode and dual-mode operation (backtest/trade) with
command-line interface as specified in PRD.
"""

import argparse
import logging
import sys
from pathlib import Path
from datetime import datetime
from coordinators.trading_agent import TradingAgent, AgentWeight
from agents.base_agent import MarketData
from core.config import TradingConfig
from backtest.engines.backtest_engine import BacktestEngine, BacktestConfig
from backtest.engines.data_manager import DataManager
from backtest.analyzers.performance_analyzer import PerformanceAnalyzer


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('trading_system_v3.log'),
            logging.StreamHandler()
        ]
    )


def create_sample_market_data(symbol: str = "000001.SZ") -> MarketData:
    """Create sample market data for testing"""
    return MarketData(
        symbol=symbol,
        timestamp=datetime.now(),
        open=15.20,
        high=15.45,
        low=15.10,
        close=15.35,
        volume=2500000,
        technical_indicators={
            'ma_5': 15.25,
            'ma_20': 15.18,
            'rsi': 55.2,
            'macd': 0.05,
            'kdj_k': 65.3,
            'bollinger_upper': 15.50,
            'bollinger_lower': 15.00,
            'atr': 0.25,
            'volume_ratio': 1.2,
            'price_position': 0.7
        },
        czsc_structure={
            'bi_direction': 1,
            'bi_strength': 0.6,
            'duan_level': 2,
            'zhongshu_status': 0,
            'trend_direction': 1
        },
        news_data=[
            {"title": "市场表现积极", "sentiment": 0.7},
            {"title": "政策利好", "sentiment": 0.8}
        ]
    )


def run_backtest_mode(args):
    """Run system in backtest mode"""
    print("📈 Trading System v3.5 - Backtest Mode")
    print("=" * 50)
    
    logger = logging.getLogger("BacktestMode")
    
    try:
        # Load configuration
        config = TradingConfig(args.config) if args.config else TradingConfig()
        
        # Setup backtest configuration
        backtest_config = BacktestConfig(
            start_date=args.start_date or config.backtest_config.start_date,
            end_date=args.end_date or config.backtest_config.end_date,
            initial_capital=args.initial_capital or config.backtest_config.initial_capital,
            commission=config.backtest_config.commission,
            slippage=config.backtest_config.slippage
        )
        
        logger.info(f"Running backtest from {backtest_config.start_date} to {backtest_config.end_date}")
        
        # Initialize components
        data_manager = DataManager(data_dir=args.data_dir or "data")
        backtest_engine = BacktestEngine(config, backtest_config)
        performance_analyzer = PerformanceAnalyzer()
        
        # Load historical data
        symbol = args.symbol or "000001.SZ"
        logger.info(f"Loading data for {symbol}")
        
        historical_data = data_manager.load_data(
            symbol=symbol,
            start_date=backtest_config.start_date,
            end_date=backtest_config.end_date
        )
        
        # Run backtest
        logger.info("Starting backtest execution...")
        backtest_result = backtest_engine.run_backtest(historical_data)
        
        # Analyze performance
        performance_metrics = performance_analyzer.analyze_performance(
            equity_curve=backtest_result.equity_curve,
            daily_returns=backtest_result.daily_returns,
            trades=backtest_result.trades
        )
        
        # Generate comprehensive report
        report = performance_analyzer.generate_report(
            metrics=performance_metrics,
            equity_curve=backtest_result.equity_curve,
            daily_returns=backtest_result.daily_returns,
            trades=backtest_result.trades,
            start_date=backtest_config.start_date,
            end_date=backtest_config.end_date,
            initial_capital=backtest_config.initial_capital
        )
        
        # Display results
        print(f"\n📊 Backtest Results for {symbol}")
        print(f"Total Return: {backtest_result.total_return:.2%}")
        print(f"Annualized Return: {backtest_result.annualized_return:.2%}")
        print(f"Max Drawdown: {backtest_result.max_drawdown:.2%}")
        print(f"Sharpe Ratio: {backtest_result.sharpe_ratio:.2f}")
        print(f"Total Trades: {backtest_result.total_trades}")
        print(f"Win Rate: {backtest_result.win_rate:.1%}")
        
        # Agent performance breakdown
        print(f"\n🤖 Agent Performance:")
        for agent, perf in backtest_result.agent_performance.items():
            print(f"  {agent.upper()}: {perf['accuracy']:.1%} accuracy ({perf['total_decisions']} decisions)")
        
        # Save report
        if args.output:
            output_path = Path(args.output)
            output_path.mkdir(parents=True, exist_ok=True)
            
            report_file = output_path / f"backtest_report_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            performance_analyzer.save_report(report, str(report_file))
            
            # Create charts if requested
            if args.charts:
                chart_file = output_path / f"backtest_charts_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                dates = [row.date for _, row in historical_data.iterrows()]
                equity_array = backtest_result.equity_curve
                drawdowns = [(equity_array[i] - max(equity_array[:i+1])) / max(equity_array[:i+1]) for i in range(len(equity_array))]
                
                performance_analyzer.create_charts(
                    equity_curve=backtest_result.equity_curve,
                    daily_returns=backtest_result.daily_returns,
                    drawdowns=drawdowns,
                    dates=dates,
                    save_path=str(chart_file)
                )
        
        logger.info("Backtest completed successfully")
        
    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        raise


def run_trade_mode(args):
    """Run system in live/paper trading mode"""
    print("🚀 Trading System v3.5 - Trade Mode")
    print("=" * 50)
    
    logger = logging.getLogger("TradeMode")
    
    try:
        # Load configuration
        config = TradingConfig(args.config) if args.config else TradingConfig()
        
        # Create trading agent with configured weights
        weights = AgentWeight(
            rl_weight=config.coordination_config.rl_weight,
            llm_weight=config.coordination_config.llm_weight,
            czsc_weight=config.coordination_config.czsc_weight
        )
        
        agent = TradingAgent(weights, risk_tolerance=args.risk_tolerance or "MEDIUM")
        logger.info("Trading agent initialized for live trading")
        
        # In real implementation, this would connect to live data feed
        # For now, we'll demonstrate with sample data
        symbol = args.symbol or "000001.SZ"
        
        # Create sample market data (in real mode, this would come from live feed)
        market_data = create_sample_market_data(symbol)
        logger.info(f"Processing live market data for {symbol}")
        
        # Convert to price data format
        price_data = {
            'timestamp': market_data.timestamp,
            'open': market_data.open,
            'high': market_data.high,
            'low': market_data.low,
            'close': market_data.close,
            'volume': market_data.volume
        }
        
        # Get trading recommendation
        recommendation = agent.analyze_and_recommend(
            symbol=symbol,
            price_data=price_data
        )
        
        # Display real-time results
        print(f"\n📊 Live Analysis Results for {symbol}")
        print(f"Action: {recommendation.action.upper()}")
        print(f"Confidence: {recommendation.confidence:.1%}")
        print(f"Risk Level: {recommendation.risk_level}")
        print(f"Reasoning: {recommendation.reasoning}")
        
        # Display system status
        status = agent.get_system_status()
        print(f"\n⚙️ System Status: {status['status'].upper()}")
        print(f"Active Agents: {', '.join(status['active_agents'])}")
        
        # In real implementation, this would:
        # 1. Connect to broker API
        # 2. Execute trades based on recommendations
        # 3. Monitor positions continuously
        # 4. Log all activities
        
        logger.info("Trade mode demonstration completed")
        
    except Exception as e:
        logger.error(f"Trade mode failed: {e}")
        raise


def run_demo_mode():
    """Run original demo mode for backward compatibility"""
    print("🚀 Trading System v3.5 - Demo Mode")
    print("=" * 50)
    
    logger = logging.getLogger("DemoMode")
    
    try:
        # Create trading agent with balanced weights
        weights = AgentWeight(
            rl_weight=0.4,    # 40% weight for RL quantitative analysis
            llm_weight=0.3,   # 30% weight for LLM semantic analysis  
            czsc_weight=0.3   # 30% weight for CZSC technical analysis
        )
        
        agent = TradingAgent(weights, risk_tolerance="MEDIUM")
        logger.info("Trading agent initialized successfully")
        
        # Create sample market data
        market_data = create_sample_market_data()
        logger.info(f"Processing market data for {market_data.symbol}")
        
        # Convert to price data format expected by trading agent
        price_data = {
            'timestamp': market_data.timestamp,
            'open': market_data.open,
            'high': market_data.high,
            'low': market_data.low,
            'close': market_data.close,
            'volume': market_data.volume
        }
        
        # Get trading recommendation
        recommendation = agent.analyze_and_recommend(
            symbol=market_data.symbol,
            price_data=price_data
        )
        
        # Display results
        print(f"\n📊 Analysis Results for {market_data.symbol}")
        print(f"Action: {recommendation.action.upper()}")
        print(f"Confidence: {recommendation.confidence:.1%}")
        print(f"Risk Level: {recommendation.risk_level}")
        print(f"Expected Return: {recommendation.expected_return:.2%}")
        print(f"Reasoning: {recommendation.reasoning}")
        
        # Show agent reasoning breakdown
        print(f"\n🤖 Agent Analysis:")
        reasoning_parts = recommendation.reasoning.split(" | ")
        for part in reasoning_parts[1:]:  # Skip the "Coordinated Decision -" part
            if ":" in part:
                print(f"  {part}")
        
        # Display system status
        status = agent.get_system_status()
        print(f"\n⚙️ System Status: {status['status'].upper()}")
        print(f"Active Agents: {', '.join(status['active_agents'])}")
        print(f"Performance: {status['performance']['accuracy_rate']:.1%} accuracy")
        
        logger.info("Demo completed successfully")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Trading System v3.5 - Multi-Agent Trading Platform",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run demo mode
  python main.py

  # Run backtest mode
  python main.py --mode backtest --symbol 000001.SZ --start-date 2020-01-01 --end-date 2023-12-31
  
  # Run trade mode
  python main.py --mode trade --symbol 000001.SZ --risk-tolerance HIGH
  
  # Run backtest with custom config
  python main.py --mode backtest --config my_config.json --output ./results --charts
        """
    )
    
    parser.add_argument(
        '--mode',
        choices=['demo', 'backtest', 'trade'],
        default='demo',
        help='Operation mode (default: demo)'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Path to configuration file (JSON)'
    )
    
    parser.add_argument(
        '--symbol',
        type=str,
        default='000001.SZ',
        help='Trading symbol (default: 000001.SZ)'
    )
    
    # Backtest specific arguments
    parser.add_argument(
        '--start-date',
        type=str,
        help='Backtest start date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        help='Backtest end date (YYYY-MM-DD)'
    )
    
    parser.add_argument(
        '--initial-capital',
        type=float,
        help='Initial capital for backtest'
    )
    
    parser.add_argument(
        '--data-dir',
        type=str,
        default='data',
        help='Data directory path (default: data)'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        help='Output directory for results'
    )
    
    parser.add_argument(
        '--charts',
        action='store_true',
        help='Generate performance charts'
    )
    
    # Trade specific arguments
    parser.add_argument(
        '--risk-tolerance',
        choices=['LOW', 'MEDIUM', 'HIGH'],
        default='MEDIUM',
        help='Risk tolerance level (default: MEDIUM)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    return parser.parse_args()


def main():
    """Main entry point with command-line interface"""
    try:
        args = parse_arguments()
        
        # Setup logging
        setup_logging(args.log_level)
        logger = logging.getLogger("Main")
        
        logger.info(f"Starting Trading System v3.5 in {args.mode} mode")
        
        # Route to appropriate mode
        if args.mode == 'backtest':
            run_backtest_mode(args)
        elif args.mode == 'trade':
            run_trade_mode(args)
        else:  # demo mode
            run_demo_mode()
            
        logger.info("System execution completed successfully")
        
    except KeyboardInterrupt:
        print("\n⚠️ Execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger = logging.getLogger("Main")
        logger.error(f"System execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 