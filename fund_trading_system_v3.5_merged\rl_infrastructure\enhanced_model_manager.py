"""
Enhanced RL Model Management System

Advanced model management with hot-swapping, performance monitoring,
model compression, and intelligent deployment capabilities.
"""

import logging
import time
import threading
import hashlib
import pickle
import gzip
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from pathlib import Path
from collections import deque, defaultdict
import json
import shutil
from enum import Enum

try:
    import torch
    import torch.nn as nn
    import torch.quantization as quantization
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False


class ModelStatus(Enum):
    """Model status enumeration"""
    LOADING = "loading"
    ACTIVE = "active"
    STANDBY = "standby"
    DEPRECATED = "deprecated"
    ERROR = "error"


@dataclass
class ModelPerformanceMetrics:
    """Model performance metrics"""
    model_id: str
    version: str
    accuracy: float = 0.0
    latency_ms: float = 0.0
    memory_usage_mb: float = 0.0
    throughput_qps: float = 0.0
    error_rate: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'model_id': self.model_id,
            'version': self.version,
            'accuracy': self.accuracy,
            'latency_ms': self.latency_ms,
            'memory_usage_mb': self.memory_usage_mb,
            'throughput_qps': self.throughput_qps,
            'error_rate': self.error_rate,
            'last_updated': self.last_updated.isoformat()
        }


@dataclass
class ModelDeploymentConfig:
    """Model deployment configuration"""
    model_id: str
    version: str
    max_memory_mb: float = 1024.0
    max_latency_ms: float = 100.0
    min_accuracy: float = 0.8
    auto_fallback: bool = True
    compression_enabled: bool = True
    monitoring_enabled: bool = True


class ModelCompressor:
    """
    Model compression utilities
    
    Provides various model compression techniques including
    quantization, pruning, and knowledge distillation.
    """
    
    def __init__(self):
        self.logger = logging.getLogger("ModelCompressor")
    
    def quantize_model(self, model: Any, quantization_type: str = "dynamic") -> Any:
        """
        Quantize model to reduce size and improve inference speed
        
        Args:
            model: Model to quantize
            quantization_type: Type of quantization (dynamic, static, qat)
            
        Returns:
            Quantized model
        """
        if not TORCH_AVAILABLE:
            self.logger.warning("PyTorch not available, skipping quantization")
            return model
        
        try:
            if quantization_type == "dynamic":
                # Dynamic quantization
                quantized_model = torch.quantization.quantize_dynamic(
                    model, {nn.Linear}, dtype=torch.qint8
                )
            elif quantization_type == "static":
                # Static quantization (requires calibration data)
                model.eval()
                model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
                torch.quantization.prepare(model, inplace=True)
                # Note: Would need calibration data here
                quantized_model = torch.quantization.convert(model, inplace=False)
            else:
                self.logger.warning(f"Unknown quantization type: {quantization_type}")
                return model
            
            self.logger.info(f"Model quantized using {quantization_type} quantization")
            return quantized_model
            
        except Exception as e:
            self.logger.error(f"Model quantization failed: {e}")
            return model
    
    def compress_model_file(self, model_path: str, compression_level: int = 6) -> str:
        """
        Compress model file using gzip
        
        Args:
            model_path: Path to model file
            compression_level: Compression level (1-9)
            
        Returns:
            Path to compressed file
        """
        try:
            compressed_path = f"{model_path}.gz"
            
            with open(model_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb', compresslevel=compression_level) as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Calculate compression ratio
            original_size = Path(model_path).stat().st_size
            compressed_size = Path(compressed_path).stat().st_size
            ratio = compressed_size / original_size
            
            self.logger.info(f"Model compressed: {ratio:.2%} of original size")
            return compressed_path
            
        except Exception as e:
            self.logger.error(f"Model compression failed: {e}")
            return model_path
    
    def get_model_size(self, model: Any) -> float:
        """Get model size in MB"""
        if not TORCH_AVAILABLE:
            return 0.0
        
        try:
            param_size = 0
            buffer_size = 0
            
            for param in model.parameters():
                param_size += param.nelement() * param.element_size()
            
            for buffer in model.buffers():
                buffer_size += buffer.nelement() * buffer.element_size()
            
            size_mb = (param_size + buffer_size) / (1024 * 1024)
            return size_mb
            
        except Exception as e:
            self.logger.error(f"Failed to calculate model size: {e}")
            return 0.0


class ModelPerformanceMonitor:
    """
    Real-time model performance monitoring
    
    Tracks model performance metrics, detects anomalies,
    and triggers alerts when performance degrades.
    """
    
    def __init__(self, 
                 history_size: int = 1000,
                 alert_threshold: float = 0.1):
        """
        Initialize performance monitor
        
        Args:
            history_size: Size of performance history
            alert_threshold: Threshold for performance alerts
        """
        self.history_size = history_size
        self.alert_threshold = alert_threshold
        
        # Performance tracking
        self.performance_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=history_size))
        self.current_metrics: Dict[str, ModelPerformanceMetrics] = {}
        self.alerts: List[Dict[str, Any]] = []
        
        # Monitoring state
        self.monitoring_active = False
        self.monitor_thread = None
        
        self.logger = logging.getLogger("ModelPerformanceMonitor")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("Model performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Model performance monitoring stopped")
    
    def record_prediction(self, model_id: str, version: str, 
                         latency_ms: float, success: bool = True):
        """Record a prediction event"""
        key = f"{model_id}_{version}"
        
        # Update current metrics
        if key not in self.current_metrics:
            self.current_metrics[key] = ModelPerformanceMetrics(model_id, version)
        
        metrics = self.current_metrics[key]
        
        # Update latency (moving average)
        if metrics.latency_ms == 0:
            metrics.latency_ms = latency_ms
        else:
            metrics.latency_ms = 0.9 * metrics.latency_ms + 0.1 * latency_ms
        
        # Update error rate
        history = self.performance_history[key]
        history.append({'latency': latency_ms, 'success': success, 'timestamp': time.time()})
        
        # Calculate error rate from recent history
        recent_events = [event for event in history if time.time() - event['timestamp'] < 300]  # 5 minutes
        if recent_events:
            error_count = sum(1 for event in recent_events if not event['success'])
            metrics.error_rate = error_count / len(recent_events)
        
        # Calculate throughput
        if len(recent_events) > 1:
            time_span = recent_events[-1]['timestamp'] - recent_events[0]['timestamp']
            if time_span > 0:
                metrics.throughput_qps = len(recent_events) / time_span
        
        metrics.last_updated = datetime.now()
        
        # Check for alerts
        self._check_performance_alerts(key, metrics)
    
    def get_performance_metrics(self, model_id: str, version: str) -> Optional[ModelPerformanceMetrics]:
        """Get current performance metrics for model"""
        key = f"{model_id}_{version}"
        return self.current_metrics.get(key)
    
    def get_performance_trend(self, model_id: str, version: str, 
                            hours: int = 24) -> Dict[str, List[float]]:
        """Get performance trend over time"""
        key = f"{model_id}_{version}"
        history = self.performance_history.get(key, deque())
        
        cutoff_time = time.time() - (hours * 3600)
        recent_history = [event for event in history if event['timestamp'] >= cutoff_time]
        
        return {
            'timestamps': [event['timestamp'] for event in recent_history],
            'latencies': [event['latency'] for event in recent_history],
            'success_rates': [1.0 if event['success'] else 0.0 for event in recent_history]
        }
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Update memory usage for all models
                for key, metrics in self.current_metrics.items():
                    # Placeholder for memory usage calculation
                    # In real implementation, would measure actual memory usage
                    pass
                
                time.sleep(10)  # Monitor every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(30)
    
    def _check_performance_alerts(self, key: str, metrics: ModelPerformanceMetrics):
        """Check for performance alerts"""
        alerts = []
        
        # High latency alert
        if metrics.latency_ms > 200:  # 200ms threshold
            alerts.append({
                'type': 'high_latency',
                'message': f"High latency detected: {metrics.latency_ms:.2f}ms",
                'severity': 'warning'
            })
        
        # High error rate alert
        if metrics.error_rate > 0.05:  # 5% error rate threshold
            alerts.append({
                'type': 'high_error_rate',
                'message': f"High error rate detected: {metrics.error_rate:.2%}",
                'severity': 'error'
            })
        
        # Low throughput alert
        if metrics.throughput_qps < 1.0 and metrics.throughput_qps > 0:
            alerts.append({
                'type': 'low_throughput',
                'message': f"Low throughput detected: {metrics.throughput_qps:.2f} QPS",
                'severity': 'warning'
            })
        
        # Add alerts with timestamp and model info
        for alert in alerts:
            alert.update({
                'model_id': metrics.model_id,
                'version': metrics.version,
                'timestamp': datetime.now().isoformat()
            })
            self.alerts.append(alert)
            
            self.logger.warning(f"Performance alert: {alert['message']}")
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get active performance alerts"""
        # Return alerts from last hour
        cutoff_time = datetime.now() - timedelta(hours=1)
        return [
            alert for alert in self.alerts
            if datetime.fromisoformat(alert['timestamp']) >= cutoff_time
        ]


class HotSwapModelManager:
    """
    Hot-swap model manager for seamless model updates
    
    Enables zero-downtime model updates with automatic
    fallback and rollback capabilities.
    """
    
    def __init__(self, models_dir: str = "models"):
        """
        Initialize hot-swap manager
        
        Args:
            models_dir: Directory containing models
        """
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(exist_ok=True)
        
        # Model management
        self.active_models: Dict[str, Any] = {}
        self.standby_models: Dict[str, Any] = {}
        self.model_status: Dict[str, ModelStatus] = {}
        self.deployment_configs: Dict[str, ModelDeploymentConfig] = {}
        
        # Components
        self.compressor = ModelCompressor()
        self.performance_monitor = ModelPerformanceMonitor()
        
        # Thread safety
        self._lock = threading.RLock()
        
        self.logger = logging.getLogger("HotSwapModelManager")
        self.logger.info("Hot-swap model manager initialized")
    
    def deploy_model(self, model: Any, model_id: str, version: str,
                    config: Optional[ModelDeploymentConfig] = None) -> bool:
        """
        Deploy model with hot-swap capability
        
        Args:
            model: Model to deploy
            model_id: Model identifier
            version: Model version
            config: Deployment configuration
            
        Returns:
            True if deployment successful
        """
        with self._lock:
            try:
                key = f"{model_id}_{version}"
                
                # Use default config if not provided
                if config is None:
                    config = ModelDeploymentConfig(model_id, version)
                
                self.deployment_configs[key] = config
                
                # Compress model if enabled
                if config.compression_enabled:
                    model = self.compressor.quantize_model(model)
                
                # Validate model performance
                if not self._validate_model_performance(model, config):
                    self.logger.error(f"Model validation failed: {key}")
                    return False
                
                # Deploy to standby first
                self.standby_models[key] = model
                self.model_status[key] = ModelStatus.STANDBY
                
                # Perform hot swap
                success = self._perform_hot_swap(key)
                
                if success:
                    self.logger.info(f"Model deployed successfully: {key}")
                    
                    # Start monitoring if enabled
                    if config.monitoring_enabled:
                        self.performance_monitor.start_monitoring()
                
                return success
                
            except Exception as e:
                self.logger.error(f"Model deployment failed: {e}")
                return False
    
    def get_active_model(self, model_id: str) -> Optional[Any]:
        """Get currently active model"""
        with self._lock:
            for key, model in self.active_models.items():
                if key.startswith(f"{model_id}_"):
                    return model
            return None
    
    def rollback_model(self, model_id: str) -> bool:
        """Rollback to previous model version"""
        with self._lock:
            try:
                # Find current and previous versions
                current_key = None
                previous_key = None
                
                for key in self.model_status.keys():
                    if key.startswith(f"{model_id}_"):
                        if self.model_status[key] == ModelStatus.ACTIVE:
                            current_key = key
                        elif self.model_status[key] == ModelStatus.STANDBY:
                            previous_key = key
                
                if not previous_key:
                    self.logger.error(f"No previous version available for rollback: {model_id}")
                    return False
                
                # Perform rollback
                if current_key:
                    self.model_status[current_key] = ModelStatus.DEPRECATED
                    if current_key in self.active_models:
                        del self.active_models[current_key]
                
                self.active_models[previous_key] = self.standby_models[previous_key]
                self.model_status[previous_key] = ModelStatus.ACTIVE
                
                self.logger.info(f"Model rollback successful: {model_id}")
                return True
                
            except Exception as e:
                self.logger.error(f"Model rollback failed: {e}")
                return False
    
    def get_model_status(self, model_id: str, version: str) -> ModelStatus:
        """Get model status"""
        key = f"{model_id}_{version}"
        return self.model_status.get(key, ModelStatus.ERROR)
    
    def get_performance_metrics(self, model_id: str, version: str) -> Optional[ModelPerformanceMetrics]:
        """Get model performance metrics"""
        return self.performance_monitor.get_performance_metrics(model_id, version)
    
    def _validate_model_performance(self, model: Any, config: ModelDeploymentConfig) -> bool:
        """Validate model meets performance requirements"""
        try:
            # Check model size
            model_size = self.compressor.get_model_size(model)
            if model_size > config.max_memory_mb:
                self.logger.warning(f"Model size ({model_size:.2f}MB) exceeds limit ({config.max_memory_mb}MB)")
                return False
            
            # TODO: Add latency and accuracy validation
            # This would require running test predictions
            
            return True
            
        except Exception as e:
            self.logger.error(f"Model validation error: {e}")
            return False
    
    def _perform_hot_swap(self, key: str) -> bool:
        """Perform hot swap from standby to active"""
        try:
            if key not in self.standby_models:
                return False
            
            # Move from standby to active
            self.active_models[key] = self.standby_models[key]
            self.model_status[key] = ModelStatus.ACTIVE
            
            # Clean up old active models for same model_id
            model_id = key.split('_')[0]
            for old_key in list(self.active_models.keys()):
                if old_key != key and old_key.startswith(f"{model_id}_"):
                    self.model_status[old_key] = ModelStatus.DEPRECATED
                    del self.active_models[old_key]
            
            return True
            
        except Exception as e:
            self.logger.error(f"Hot swap failed: {e}")
            return False
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        with self._lock:
            return {
                'active_models': len(self.active_models),
                'standby_models': len(self.standby_models),
                'model_status': {k: v.value for k, v in self.model_status.items()},
                'performance_alerts': self.performance_monitor.get_active_alerts(),
                'monitoring_active': self.performance_monitor.monitoring_active
            }
