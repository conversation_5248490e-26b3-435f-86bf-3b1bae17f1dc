#!/usr/bin/env python3
"""
调试数据结构 - 查看传递给LLM分析器的实际数据
"""

import sys
import os
import logging
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3

def debug_data_structure():
    """调试数据结构"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🔍 调试LLM分析器数据结构")
    print("-" * 50)
    
    try:
        # 初始化协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 测试基金代码
        test_fund_code = '513500'
        
        # 获取传统智能体数据
        technical_data = coordinator.technical_agent.process({'fund_code': test_fund_code})
        gua_data = coordinator.gua_agent.process({'fund_code': test_fund_code})
        flow_data = coordinator.flow_agent.process({'fund_code': test_fund_code})
        
        print("📊 传统智能体数据结构:")
        print(f"Technical Data Keys: {list(technical_data.keys())}")
        print(f"Gua Data Keys: {list(gua_data.keys())}")
        print(f"Flow Data Keys: {list(flow_data.keys())}")
        
        # 检查价格数据
        if 'price_data' in flow_data:
            price_data = flow_data['price_data']
            print(f"\n💰 Flow Data中的价格数据:")
            print(f"Price Data Keys: {list(price_data.keys())}")
            print(f"Price Data Content: {json.dumps(price_data, ensure_ascii=False, indent=2)}")
        else:
            print(f"\n❌ Flow Data中没有price_data字段")
            print(f"Flow Data完整内容: {json.dumps(flow_data, ensure_ascii=False, indent=2)}")
        
        # 整合数据
        integrated_data = coordinator._integrate_agent_data(
            test_fund_code, technical_data, gua_data, flow_data
        )
        
        print(f"\n🔗 整合后的数据结构:")
        print(f"Integrated Data Keys: {list(integrated_data.keys())}")
        
        if 'price_data' in integrated_data:
            price_data = integrated_data['price_data']
            print(f"\n💰 整合数据中的价格数据:")
            print(f"Price Data Keys: {list(price_data.keys())}")
            print(f"Price Data Content: {json.dumps(price_data, ensure_ascii=False, indent=2)}")
        else:
            print(f"\n❌ 整合数据中没有price_data字段")
        
        # 模拟LLM分析器接收的数据
        market_data = {
            'fund_code': test_fund_code,
            'price_data': integrated_data.get('price_data', {}),
            'technical_analysis': integrated_data.get('technical_analysis', {}),
            'gua_data': integrated_data.get('gua_data', {}),
            'flow_data': integrated_data.get('flow_data', {}),
            'volume_analysis': integrated_data.get('volume_analysis', {}),
        }
        
        print(f"\n🤖 LLM分析器接收的市场数据:")
        print(f"Market Data Keys: {list(market_data.keys())}")
        
        if market_data['price_data']:
            print(f"Price Data: {json.dumps(market_data['price_data'], ensure_ascii=False, indent=2)}")
        else:
            print("❌ 价格数据为空!")
            
        # 检查LLM分析器构建的提示词
        from analyzers.llm_market_analyzer import LLMMarketAnalyzer
        llm_analyzer = LLMMarketAnalyzer()
        prompt = llm_analyzer._build_analysis_prompt(market_data, test_fund_code)
        
        print(f"\n📝 LLM分析器构建的提示词:")
        print(prompt)
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_data_structure()