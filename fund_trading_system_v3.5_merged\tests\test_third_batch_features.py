"""
第三批任务功能测试

测试新增的RL训练管理、评估系统和高级风险管理功能
"""

import unittest
import sys
import os
import tempfile
import shutil
from datetime import datetime
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入测试模块
try:
    from rl_infrastructure import (
        OnlineLearningManager, OnlineLearningConfig,
        ABTestingFramework, ABTestConfig, ModelVariant,
        AdvancedEvaluator, EvaluationReportGenerator, ReportConfig,
        ENHANCED_FEATURES
    )
    from risk_management.portfolio_risk_analyzer import AdvancedRiskMonitor, RiskAlert
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"导入失败: {e}")
    IMPORTS_SUCCESSFUL = False


class TestOnlineLearning(unittest.TestCase):
    """测试在线学习功能"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("导入失败，跳过测试")
        
        self.config = OnlineLearningConfig(
            batch_size=16,
            buffer_size=1000,
            update_frequency=50
        )
        self.manager = OnlineLearningManager(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.manager)
        self.assertEqual(self.manager.config.batch_size, 16)
        self.assertEqual(self.manager.samples_processed, 0)
    
    def test_data_buffer(self):
        """测试数据缓冲区"""
        # 添加训练数据
        state = np.random.rand(10)
        action = np.random.rand(2)
        reward = 0.5
        next_state = np.random.rand(10)
        done = False
        
        initial_size = self.manager.data_buffer.size()
        self.manager.add_training_data(state, action, reward, next_state, done)
        
        self.assertEqual(self.manager.data_buffer.size(), initial_size + 1)
        self.assertEqual(self.manager.samples_processed, 1)
    
    def test_learning_status(self):
        """测试学习状态获取"""
        status = self.manager.get_learning_status()
        
        self.assertIn('is_learning', status)
        self.assertIn('samples_processed', status)
        self.assertIn('buffer_size', status)
        self.assertIn('model_version', status)
        
        self.assertFalse(status['is_learning'])
        self.assertEqual(status['samples_processed'], 0)


class TestABTesting(unittest.TestCase):
    """测试A/B测试功能"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("导入失败，跳过测试")
        
        # 创建测试变体
        self.variants = [
            ModelVariant(
                variant_id="model_a",
                model_name="Model A",
                model_path="/path/to/model_a",
                traffic_weight=1.0,
                description="基础模型"
            ),
            ModelVariant(
                variant_id="model_b", 
                model_name="Model B",
                model_path="/path/to/model_b",
                traffic_weight=1.0,
                description="改进模型"
            )
        ]
        
        self.config = ABTestConfig(
            test_name="RL_Model_Test",
            variants=self.variants,
            min_sample_size=10,
            max_duration_hours=1
        )
        
        self.framework = ABTestingFramework(self.config)
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.framework)
        self.assertEqual(len(self.framework.config.variants), 2)
        self.assertEqual(self.framework.config.test_name, "RL_Model_Test")
    
    def test_start_test(self):
        """测试开始测试"""
        self.framework.start_test()
        self.assertEqual(self.framework.status.value, "running")
        self.assertIsNotNone(self.framework.start_time)
    
    def test_variant_allocation(self):
        """测试变体分配"""
        self.framework.start_test()
        
        # 分配多个变体
        allocations = []
        for i in range(20):
            variant = self.framework.allocate_variant(f"request_{i}")
            allocations.append(variant)
            self.assertIn(variant, ["model_a", "model_b"])
        
        # 检查两个变体都被分配到
        unique_variants = set(allocations)
        self.assertEqual(len(unique_variants), 2)
    
    def test_result_recording(self):
        """测试结果记录"""
        self.framework.start_test()
        
        # 记录一些结果
        for i in range(10):
            variant = self.framework.allocate_variant()
            metrics = {
                'accuracy': np.random.uniform(0.7, 0.9),
                'precision': np.random.uniform(0.6, 0.8),
                'recall': np.random.uniform(0.65, 0.85)
            }
            self.framework.record_result(variant, metrics)
        
        # 获取结果
        results = self.framework.get_current_results()
        self.assertGreater(len(results), 0)
        
        for variant_id, result in results.items():
            self.assertIn('sample_count', result)
            self.assertIn('metrics', result)
            self.assertGreater(result['sample_count'], 0)


class TestAdvancedEvaluator(unittest.TestCase):
    """测试高级评估器"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("导入失败，跳过测试")
        
        self.evaluator = AdvancedEvaluator(risk_free_rate=0.02)
        
        # 生成测试数据
        np.random.seed(42)
        self.returns = np.random.normal(0.001, 0.02, 252)  # 一年的日收益率
        self.prices = np.cumprod(1 + self.returns) * 100
        
        # 生成测试交易
        self.trades = []
        for i in range(20):
            self.trades.append({
                'return': np.random.normal(0.02, 0.05),
                'entry_price': 100 + i,
                'exit_price': 100 + i + np.random.normal(2, 5),
                'pnl': np.random.normal(200, 500)
            })
    
    def test_performance_evaluation(self):
        """测试性能评估"""
        result = self.evaluator.evaluate_performance(
            returns=self.returns.tolist(),
            prices=self.prices.tolist(),
            trades=self.trades
        )
        
        self.assertIsNotNone(result)
        self.assertIn('performance_metrics', result.__dict__)
        self.assertIn('risk_metrics', result.__dict__)
        self.assertIn('trade_statistics', result.__dict__)
        
        # 检查关键指标
        perf = result.performance_metrics
        self.assertIn('total_return', perf)
        self.assertIn('sharpe_ratio', perf)
        self.assertIn('win_rate', perf)
        
        risk = result.risk_metrics
        self.assertIn('volatility', risk)
        self.assertIn('max_drawdown', risk)
        self.assertIn('var_95', risk)
    
    def test_rolling_metrics(self):
        """测试滚动指标"""
        rolling_metrics = self.evaluator.calculate_rolling_metrics(
            returns=self.returns.tolist(),
            window=60
        )
        
        self.assertIn('rolling_return', rolling_metrics)
        self.assertIn('rolling_volatility', rolling_metrics)
        self.assertIn('rolling_sharpe', rolling_metrics)
        self.assertIn('rolling_max_drawdown', rolling_metrics)
        
        # 检查数据长度
        expected_length = len(self.returns) - 60 + 1
        for metric_values in rolling_metrics.values():
            self.assertEqual(len(metric_values), expected_length)


class TestReportGenerator(unittest.TestCase):
    """测试报告生成器"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("导入失败，跳过测试")
        
        self.temp_dir = tempfile.mkdtemp()
        self.config = ReportConfig(
            title="测试报告",
            include_charts=False,  # 避免matplotlib依赖
            output_formats=['json', 'markdown']
        )
        self.generator = EvaluationReportGenerator(self.config)
        
        # 创建测试评估结果
        self.evaluation_result = self._create_test_evaluation_result()
    
    def tearDown(self):
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def _create_test_evaluation_result(self):
        """创建测试评估结果"""
        from rl_infrastructure.advanced_evaluator import EvaluationResult
        
        return EvaluationResult(
            timestamp=datetime.now().isoformat(),
            total_samples=1000,
            performance_metrics={
                'total_return': 0.15,
                'annualized_return': 0.12,
                'sharpe_ratio': 1.2,
                'win_rate': 0.65
            },
            risk_metrics={
                'volatility': 0.18,
                'max_drawdown': -0.08,
                'var_95': -0.025,
                'cvar_95': -0.035
            },
            trade_statistics={
                'total_trades': 50,
                'winning_trades': 32,
                'losing_trades': 18,
                'average_trade_return': 0.003
            },
            period_analysis={}
        )
    
    def test_report_generation(self):
        """测试报告生成"""
        generated_files = self.generator.generate_report(
            self.evaluation_result,
            self.temp_dir,
            "test_report"
        )
        
        self.assertIn('json', generated_files)
        self.assertIn('markdown', generated_files)
        
        # 检查文件是否存在
        for file_path in generated_files.values():
            self.assertTrue(os.path.exists(file_path))
    
    def test_json_report_content(self):
        """测试JSON报告内容"""
        json_path = self.generator._generate_json_report(
            self.evaluation_result,
            self.temp_dir,
            "test_json"
        )
        
        self.assertTrue(os.path.exists(json_path))
        
        # 读取并验证JSON内容
        import json
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertIn('performance_metrics', data)
        self.assertIn('risk_metrics', data)
        self.assertIn('total_samples', data)
        self.assertEqual(data['total_samples'], 1000)


class TestRiskMonitor(unittest.TestCase):
    """测试风险监控系统"""
    
    def setUp(self):
        if not IMPORTS_SUCCESSFUL:
            self.skipTest("导入失败，跳过测试")
        
        self.monitor = AdvancedRiskMonitor()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.monitor)
        self.assertIn('risk_thresholds', self.monitor.config)
        self.assertEqual(len(self.monitor.active_alerts), 0)
    
    def test_alert_creation(self):
        """测试告警创建"""
        alert = RiskAlert(
            alert_type='TEST_ALERT',
            severity='medium',
            message='这是一个测试告警'
        )
        
        self.assertEqual(alert.alert_type, 'TEST_ALERT')
        self.assertEqual(alert.severity, 'medium')
        self.assertFalse(alert.acknowledged)
        
        # 测试转换为字典
        alert_dict = alert.to_dict()
        self.assertIn('alert_type', alert_dict)
        self.assertIn('timestamp', alert_dict)
    
    def test_alert_summary(self):
        """测试告警摘要"""
        # 添加一些测试告警
        test_alerts = [
            RiskAlert('TEST1', 'low', 'Test 1'),
            RiskAlert('TEST2', 'high', 'Test 2'),
            RiskAlert('TEST3', 'critical', 'Test 3')
        ]
        
        self.monitor.active_alerts.extend(test_alerts)
        
        summary = self.monitor.get_alert_summary()
        
        self.assertEqual(summary['total_active_alerts'], 3)
        self.assertEqual(summary['severity_breakdown']['low'], 1)
        self.assertEqual(summary['severity_breakdown']['high'], 1)
        self.assertEqual(summary['severity_breakdown']['critical'], 1)


if __name__ == '__main__':
    # 打印功能状态
    if IMPORTS_SUCCESSFUL:
        print("=== 第三批任务功能测试 ===")
        print(f"增强功能状态: {ENHANCED_FEATURES}")
        print()
    
    # 运行测试
    unittest.main(verbosity=2)
