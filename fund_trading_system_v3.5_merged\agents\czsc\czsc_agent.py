"""
Enhanced CZSC (Chan Theory) Agent Implementation

This agent implements advanced CZSC (缠中说禅) technical analysis using Chan Theory
structure analysis, pattern recognition, and multi-timeframe analysis for trading signals.
"""

import logging
from datetime import datetime
from typing import Dict, Any, List
from dataclasses import dataclass

from agents.base_agent import BaseAgent, TradingSignal, MarketData

# 尝试导入可选依赖
try:
    import numpy as np
    import pandas as pd
    from concurrent.futures import ThreadPoolExecutor
    import threading
    ADVANCED_FEATURES = True
except ImportError:
    # 如果没有高级依赖，使用基础实现
    ADVANCED_FEATURES = False
    np = None
    pd = None


@dataclass
class CZSCStructure:
    """Enhanced CZSC structure analysis results"""
    # 基础结构
    bi_direction: int  # 笔的方向 (-1: 下笔, 1: 上笔, 0: 无明确方向)
    bi_strength: float  # 笔的强度 (0.0-1.0)
    bi_count: int  # 当前笔的数量

    # 段级别分析
    duan_level: int  # 段的级别 (1-5)
    duan_direction: int  # 段的方向
    duan_strength: float  # 段的强度

    # 中枢分析
    zhongshu_status: int  # 中枢状态 (-1: 下破, 0: 中枢内, 1: 上破)
    zhongshu_level: float  # 中枢位置
    zhongshu_strength: float  # 中枢强度

    # 趋势分析
    trend_direction: int  # 趋势方向 (-1: 下降, 0: 震荡, 1: 上升)
    trend_strength: float  # 趋势强度 (0.0-1.0)
    trend_duration: int  # 趋势持续时间

    # 分型模式
    fractal_pattern: str  # 分型模式 ("top", "bottom", "none")
    fractal_strength: float  # 分型强度

    # 市场结构
    market_structure: str  # 市场结构状态
    support_resistance: float  # 关键支撑阻力位
    volatility_pattern: str  # 波动率模式

    # 多时间框架
    timeframe: str  # 时间框架
    higher_tf_trend: int  # 更高时间框架趋势
    lower_tf_signal: int  # 更低时间框架信号


class EnhancedCZSCAnalyzer:
    """Enhanced CZSC analyzer with optimized algorithms"""

    def __init__(self, lookback_period: int = 50, min_bi_length: int = 5):
        self.lookback_period = lookback_period
        self.min_bi_length = min_bi_length
        self.logger = logging.getLogger("CZSCAnalyzer")

        # 缓存计算结果
        self._cache = {}
        self._cache_lock = threading.Lock()

    def analyze_structure(self, price_data: List[Dict[str, Any]], timeframe: str = "1d") -> CZSCStructure:
        """Analyze CZSC structure with optimized algorithms"""
        try:
            # 转换数据格式
            df = self._prepare_data(price_data)

            # 并行计算各种结构
            with ThreadPoolExecutor(max_workers=4) as executor:
                # 提交并行任务
                bi_future = executor.submit(self._analyze_bi_structure, df)
                duan_future = executor.submit(self._analyze_duan_structure, df)
                zhongshu_future = executor.submit(self._analyze_zhongshu, df)
                trend_future = executor.submit(self._analyze_trend, df)
                fractal_future = executor.submit(self._analyze_fractals, df)

                # 获取结果
                bi_result = bi_future.result()
                duan_result = duan_future.result()
                zhongshu_result = zhongshu_future.result()
                trend_result = trend_future.result()
                fractal_result = fractal_future.result()

            # 计算支撑阻力位
            support_resistance = self._calculate_support_resistance(df)

            # 分析波动率模式
            volatility_pattern = self._analyze_volatility_pattern(df)

            return CZSCStructure(
                bi_direction=bi_result['direction'],
                bi_strength=bi_result['strength'],
                bi_count=bi_result['count'],
                duan_level=duan_result['level'],
                duan_direction=duan_result['direction'],
                duan_strength=duan_result['strength'],
                zhongshu_status=zhongshu_result['status'],
                zhongshu_level=zhongshu_result['level'],
                zhongshu_strength=zhongshu_result['strength'],
                trend_direction=trend_result['direction'],
                trend_strength=trend_result['strength'],
                trend_duration=trend_result['duration'],
                fractal_pattern=fractal_result['pattern'],
                fractal_strength=fractal_result['strength'],
                market_structure=self._determine_market_structure(trend_result, zhongshu_result),
                support_resistance=support_resistance,
                volatility_pattern=volatility_pattern,
                timeframe=timeframe,
                higher_tf_trend=0,  # 需要更高时间框架数据
                lower_tf_signal=0   # 需要更低时间框架数据
            )

        except Exception as e:
            self.logger.error(f"CZSC structure analysis failed: {e}")
            return self._create_default_structure(timeframe)

    def _prepare_data(self, price_data: List[Dict[str, Any]]):
        """Prepare price data for analysis"""
        # 转换为DataFrame
        df = pd.DataFrame(price_data)

        # 确保必要的列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                df[col] = 0.0

        # 计算基础技术指标
        df['hl2'] = (df['high'] + df['low']) / 2
        df['hlc3'] = (df['high'] + df['low'] + df['close']) / 3
        df['ohlc4'] = (df['open'] + df['high'] + df['low'] + df['close']) / 4

        # 计算移动平均
        df['ma5'] = df['close'].rolling(5).mean()
        df['ma20'] = df['close'].rolling(20).mean()

        return df.tail(self.lookback_period)

    def _analyze_bi_structure(self, df) -> Dict[str, Any]:
        """Analyze bi (笔) structure"""
        try:
            # 寻找分型点
            highs = self._find_fractal_highs(df)
            lows = self._find_fractal_lows(df)

            # 构建笔
            bi_points = self._construct_bi(highs, lows)

            if len(bi_points) < 2:
                return {'direction': 0, 'strength': 0.0, 'count': 0}

            # 分析最近的笔
            last_bi = bi_points[-1]
            prev_bi = bi_points[-2] if len(bi_points) > 1 else None

            # 计算笔的方向
            direction = 1 if last_bi['high'] > prev_bi['high'] else -1 if prev_bi else 0

            # 计算笔的强度
            if prev_bi:
                price_change = abs(last_bi['price'] - prev_bi['price']) / prev_bi['price']
                volume_ratio = last_bi.get('volume', 1) / prev_bi.get('volume', 1)
                strength = min(1.0, price_change * 10 * min(2.0, volume_ratio))
            else:
                strength = 0.5

            return {
                'direction': direction,
                'strength': strength,
                'count': len(bi_points)
            }

        except Exception as e:
            self.logger.warning(f"Bi analysis failed: {e}")
            return {'direction': 0, 'strength': 0.0, 'count': 0}

    def _find_fractal_highs(self, df, window: int = 3) -> List[Dict]:
        """Find fractal high points"""
        highs = []
        for i in range(window, len(df) - window):
            if all(df.iloc[i]['high'] >= df.iloc[j]['high'] for j in range(i-window, i+window+1) if j != i):
                highs.append({
                    'index': i,
                    'price': df.iloc[i]['high'],
                    'volume': df.iloc[i]['volume']
                })
        return highs

    def _find_fractal_lows(self, df, window: int = 3) -> List[Dict]:
        """Find fractal low points"""
        lows = []
        for i in range(window, len(df) - window):
            if all(df.iloc[i]['low'] <= df.iloc[j]['low'] for j in range(i-window, i+window+1) if j != i):
                lows.append({
                    'index': i,
                    'price': df.iloc[i]['low'],
                    'volume': df.iloc[i]['volume']
                })
        return lows

    def _construct_bi(self, highs: List[Dict], lows: List[Dict]) -> List[Dict]:
        """Construct bi (笔) from fractal points"""
        all_points = []

        # 合并高点和低点
        for high in highs:
            all_points.append({**high, 'type': 'high', 'high': high['price']})
        for low in lows:
            all_points.append({**low, 'type': 'low', 'low': low['price']})

        # 按索引排序
        all_points.sort(key=lambda x: x['index'])

        # 构建笔
        bi_points = []
        if len(all_points) >= 2:
            for i in range(len(all_points) - 1):
                current = all_points[i]
                next_point = all_points[i + 1]

                if current['type'] != next_point['type']:  # 高低点交替
                    bi_points.append({
                        'start_index': current['index'],
                        'end_index': next_point['index'],
                        'start_price': current['price'],
                        'end_price': next_point['price'],
                        'price': next_point['price'],
                        'high': max(current['price'], next_point['price']),
                        'low': min(current['price'], next_point['price']),
                        'volume': next_point.get('volume', 0),
                        'direction': 1 if next_point['price'] > current['price'] else -1
                    })

        return bi_points

    def _analyze_duan_structure(self, df) -> Dict[str, Any]:
        """Analyze duan (段) structure"""
        try:
            # 简化的段分析
            close_prices = df['close'].values

            # 计算段的级别（基于价格波动幅度）
            price_range = np.max(close_prices) - np.min(close_prices)
            avg_price = np.mean(close_prices)
            volatility = price_range / avg_price

            if volatility > 0.1:
                level = 3
            elif volatility > 0.05:
                level = 2
            else:
                level = 1

            # 计算段的方向
            start_price = close_prices[0]
            end_price = close_prices[-1]
            direction = 1 if end_price > start_price else -1

            # 计算段的强度
            strength = min(1.0, abs(end_price - start_price) / start_price * 10)

            return {
                'level': level,
                'direction': direction,
                'strength': strength
            }

        except Exception as e:
            self.logger.warning(f"Duan analysis failed: {e}")
            return {'level': 1, 'direction': 0, 'strength': 0.0}

    def _analyze_zhongshu(self, df) -> Dict[str, Any]:
        """Analyze zhongshu (中枢) structure"""
        try:
            close_prices = df['close'].values
            high_prices = df['high'].values
            low_prices = df['low'].values

            # 计算中枢位置（简化为价格中位数）
            zhongshu_level = np.median(close_prices)

            # 计算当前价格相对中枢的位置
            current_price = close_prices[-1]
            price_range = np.max(high_prices) - np.min(low_prices)

            if current_price > zhongshu_level + price_range * 0.1:
                status = 1  # 上破
            elif current_price < zhongshu_level - price_range * 0.1:
                status = -1  # 下破
            else:
                status = 0  # 中枢内

            # 计算中枢强度（基于价格在中枢附近的时间）
            in_zhongshu_count = np.sum(
                (close_prices >= zhongshu_level - price_range * 0.1) &
                (close_prices <= zhongshu_level + price_range * 0.1)
            )
            strength = in_zhongshu_count / len(close_prices)

            return {
                'status': status,
                'level': zhongshu_level,
                'strength': strength
            }

        except Exception as e:
            self.logger.warning(f"Zhongshu analysis failed: {e}")
            return {'status': 0, 'level': 0.0, 'strength': 0.0}

    def _analyze_trend(self, df) -> Dict[str, Any]:
        """Analyze trend structure"""
        try:
            close_prices = df['close'].values

            # 使用线性回归分析趋势
            x = np.arange(len(close_prices))
            slope = np.polyfit(x, close_prices, 1)[0]

            # 计算趋势方向
            if slope > close_prices[0] * 0.001:  # 0.1% threshold
                direction = 1
            elif slope < -close_prices[0] * 0.001:
                direction = -1
            else:
                direction = 0

            # 计算趋势强度
            r_squared = np.corrcoef(x, close_prices)[0, 1] ** 2
            strength = min(1.0, r_squared)

            # 计算趋势持续时间（简化）
            duration = len(close_prices)

            return {
                'direction': direction,
                'strength': strength,
                'duration': duration
            }

        except Exception as e:
            self.logger.warning(f"Trend analysis failed: {e}")
            return {'direction': 0, 'strength': 0.0, 'duration': 0}

    def _analyze_fractals(self, df) -> Dict[str, Any]:
        """Analyze fractal patterns"""
        try:
            # 寻找最近的分型
            highs = self._find_fractal_highs(df, window=2)
            lows = self._find_fractal_lows(df, window=2)

            # 确定最近的分型类型
            last_high_idx = highs[-1]['index'] if highs else -1
            last_low_idx = lows[-1]['index'] if lows else -1

            if last_high_idx > last_low_idx:
                pattern = "top"
                strength = 0.7
            elif last_low_idx > last_high_idx:
                pattern = "bottom"
                strength = 0.7
            else:
                pattern = "none"
                strength = 0.0

            return {
                'pattern': pattern,
                'strength': strength
            }

        except Exception as e:
            self.logger.warning(f"Fractal analysis failed: {e}")
            return {'pattern': "none", 'strength': 0.0}

    def _calculate_support_resistance(self, df) -> float:
        """Calculate key support/resistance levels"""
        try:
            high_prices = df['high'].values
            low_prices = df['low'].values
            close_prices = df['close'].values

            # 计算关键价位
            recent_high = np.max(high_prices[-10:])
            recent_low = np.min(low_prices[-10:])
            current_price = close_prices[-1]

            # 返回最近的关键位
            if abs(current_price - recent_high) < abs(current_price - recent_low):
                return recent_high
            else:
                return recent_low

        except Exception as e:
            self.logger.warning(f"Support/resistance calculation failed: {e}")
            return 0.0

    def _analyze_volatility_pattern(self, df) -> str:
        """Analyze volatility patterns"""
        try:
            close_prices = df['close'].values

            # 计算波动率
            returns = np.diff(close_prices) / close_prices[:-1]
            volatility = np.std(returns)

            # 分类波动率模式
            if volatility > 0.03:
                return "high_volatility"
            elif volatility < 0.01:
                return "low_volatility"
            else:
                return "normal_volatility"

        except Exception as e:
            self.logger.warning(f"Volatility analysis failed: {e}")
            return "unknown"

    def _determine_market_structure(self, trend_result: Dict, zhongshu_result: Dict) -> str:
        """Determine overall market structure"""
        trend_dir = trend_result.get('direction', 0)
        zhongshu_status = zhongshu_result.get('status', 0)

        if trend_dir == 1 and zhongshu_status == 1:
            return "strong_uptrend"
        elif trend_dir == 1 and zhongshu_status == 0:
            return "uptrend_consolidation"
        elif trend_dir == -1 and zhongshu_status == -1:
            return "strong_downtrend"
        elif trend_dir == -1 and zhongshu_status == 0:
            return "downtrend_consolidation"
        elif trend_dir == 0:
            return "sideways"
        else:
            return "mixed_signals"

    def _create_default_structure(self, timeframe: str) -> CZSCStructure:
        """Create default structure when analysis fails"""
        return CZSCStructure(
            bi_direction=0, bi_strength=0.0, bi_count=0,
            duan_level=1, duan_direction=0, duan_strength=0.0,
            zhongshu_status=0, zhongshu_level=0.0, zhongshu_strength=0.0,
            trend_direction=0, trend_strength=0.0, trend_duration=0,
            fractal_pattern="none", fractal_strength=0.0,
            market_structure="unknown", support_resistance=0.0,
            volatility_pattern="unknown", timeframe=timeframe,
            higher_tf_trend=0, lower_tf_signal=0
        )


class CZSCAgent(BaseAgent):
    """Enhanced CZSC Agent with optimized algorithms and multi-timeframe analysis"""

    def __init__(self, name: str = "CZSC", lookback_period: int = 50,
                 min_bi_length: int = 5, enable_multi_timeframe: bool = False):
        super().__init__(name)
        self.agent_type = "czsc"

        # Initialize analyzer
        self.analyzer = EnhancedCZSCAnalyzer(lookback_period, min_bi_length)
        self.enable_multi_timeframe = enable_multi_timeframe

        # Performance tracking
        self.analysis_count = 0
        self.cache_hits = 0

        self.logger.info(f"CZSC Agent initialized with lookback={lookback_period}")
        self.is_initialized = True

    def generate_signal(self, data: MarketData) -> TradingSignal:
        """Generate trading signal using CZSC analysis"""
        try:
            self.analysis_count += 1

            # Prepare price data
            price_data = self._prepare_price_data(data)

            # Analyze CZSC structure
            structure = self.analyzer.analyze_structure(price_data, "1d")

            # Generate trading signal based on structure
            action = self._determine_action(structure)
            strength = self._calculate_strength(structure)
            confidence = self._calculate_confidence(structure)
            reasoning = self._generate_reasoning(structure)

            return TradingSignal(
                agent_type=self.agent_type,
                action=action,
                strength=strength,
                confidence=confidence,
                timestamp=datetime.now(),
                reasoning=reasoning,
                metadata={
                    'czsc_structure': {
                        'bi_direction': structure.bi_direction,
                        'duan_level': structure.duan_level,
                        'zhongshu_status': structure.zhongshu_status,
                        'trend_direction': structure.trend_direction,
                        'fractal_pattern': structure.fractal_pattern,
                        'market_structure': structure.market_structure
                    },
                    'analysis_count': self.analysis_count
                }
            )

        except Exception as e:
            self.logger.error(f"Error generating CZSC signal: {e}")
            return TradingSignal(
                agent_type=self.agent_type,
                action="hold",
                strength=0.0,
                confidence=0.1,
                timestamp=datetime.now(),
                reasoning=f"CZSC analysis failed: {str(e)}",
                metadata={'error': True}
            )

    def get_confidence(self) -> float:
        """Get current confidence level"""
        # Base confidence on recent performance
        recent_accuracy = self.performance_metrics.get('accuracy_rate', 0.5)

        # Adjust based on analysis complexity
        complexity_factor = 0.8 if self.enable_multi_timeframe else 0.9

        return min(0.9, recent_accuracy * complexity_factor)

    def get_explanation(self) -> str:
        """Get explanation of current analysis approach"""
        return f"CZSC Agent using {'advanced' if ADVANCED_FEATURES else 'basic'} analysis with {self.analyzer.lookback_period} period lookback"

    def _prepare_price_data(self, data: MarketData) -> List[Dict[str, Any]]:
        """Prepare price data for CZSC analysis"""
        # 如果有历史数据，使用历史数据
        if hasattr(data, 'historical_data') and data.historical_data:
            return data.historical_data

        # 否则创建单个数据点
        return [{
            'open': data.open,
            'high': data.high,
            'low': data.low,
            'close': data.close,
            'volume': data.volume,
            'timestamp': data.timestamp
        }]

    def _determine_action(self, structure: CZSCStructure) -> str:
        """Determine trading action based on CZSC structure"""
        # 综合多个信号
        signals = []

        # 笔的信号
        if structure.bi_direction == 1 and structure.bi_strength > 0.6:
            signals.append('buy')
        elif structure.bi_direction == -1 and structure.bi_strength > 0.6:
            signals.append('sell')

        # 趋势信号
        if structure.trend_direction == 1 and structure.trend_strength > 0.7:
            signals.append('buy')
        elif structure.trend_direction == -1 and structure.trend_strength > 0.7:
            signals.append('sell')

        # 中枢突破信号
        if structure.zhongshu_status == 1:
            signals.append('buy')
        elif structure.zhongshu_status == -1:
            signals.append('sell')

        # 分型信号
        if structure.fractal_pattern == "bottom" and structure.fractal_strength > 0.6:
            signals.append('buy')
        elif structure.fractal_pattern == "top" and structure.fractal_strength > 0.6:
            signals.append('sell')

        # 投票决策
        buy_votes = signals.count('buy')
        sell_votes = signals.count('sell')

        if buy_votes > sell_votes and buy_votes >= 2:
            return 'buy'
        elif sell_votes > buy_votes and sell_votes >= 2:
            return 'sell'
        else:
            return 'hold'

    def _calculate_strength(self, structure: CZSCStructure) -> float:
        """Calculate signal strength based on CZSC structure"""
        strength_factors = []

        # 笔的强度
        strength_factors.append(structure.bi_strength)

        # 趋势强度
        strength_factors.append(structure.trend_strength)

        # 分型强度
        strength_factors.append(structure.fractal_strength)

        # 中枢强度（反向，中枢越强，突破信号越弱）
        if structure.zhongshu_status != 0:
            strength_factors.append(1.0 - structure.zhongshu_strength)

        # 计算平均强度
        if strength_factors:
            return min(1.0, np.mean(strength_factors))
        else:
            return 0.0

    def _calculate_confidence(self, structure: CZSCStructure) -> float:
        """Calculate confidence based on signal consistency"""
        confidence_factors = []

        # 信号一致性
        signals = []
        if structure.bi_direction != 0:
            signals.append(structure.bi_direction)
        if structure.trend_direction != 0:
            signals.append(structure.trend_direction)
        if structure.zhongshu_status != 0:
            signals.append(structure.zhongshu_status)

        if signals:
            # 计算信号一致性
            consistency = abs(np.mean(signals))
            confidence_factors.append(consistency)

        # 结构强度
        avg_strength = (structure.bi_strength + structure.trend_strength +
                       structure.fractal_strength) / 3
        confidence_factors.append(avg_strength)

        # 市场结构清晰度
        if structure.market_structure in ["strong_uptrend", "strong_downtrend"]:
            confidence_factors.append(0.9)
        elif structure.market_structure in ["uptrend_consolidation", "downtrend_consolidation"]:
            confidence_factors.append(0.7)
        else:
            confidence_factors.append(0.5)

        return min(0.95, np.mean(confidence_factors)) if confidence_factors else 0.3

    def _generate_reasoning(self, structure: CZSCStructure) -> str:
        """Generate human-readable reasoning for the signal"""
        reasoning_parts = []

        # 笔分析
        if structure.bi_direction == 1:
            reasoning_parts.append(f"上笔形成，强度{structure.bi_strength:.2f}")
        elif structure.bi_direction == -1:
            reasoning_parts.append(f"下笔形成，强度{structure.bi_strength:.2f}")

        # 趋势分析
        if structure.trend_direction == 1:
            reasoning_parts.append(f"上升趋势，强度{structure.trend_strength:.2f}")
        elif structure.trend_direction == -1:
            reasoning_parts.append(f"下降趋势，强度{structure.trend_strength:.2f}")

        # 中枢分析
        if structure.zhongshu_status == 1:
            reasoning_parts.append("中枢上破")
        elif structure.zhongshu_status == -1:
            reasoning_parts.append("中枢下破")
        elif structure.zhongshu_status == 0:
            reasoning_parts.append("中枢震荡")

        # 分型分析
        if structure.fractal_pattern != "none":
            reasoning_parts.append(f"{structure.fractal_pattern}分型")

        # 市场结构
        reasoning_parts.append(f"市场结构: {structure.market_structure}")

        return "CZSC分析: " + ", ".join(reasoning_parts)

    def analyze_structure(self, data: MarketData) -> CZSCStructure:
        """Public method to get CZSC structure analysis"""
        price_data = self._prepare_price_data(data)
        return self.analyzer.analyze_structure(price_data)

    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get analysis statistics"""
        return {
            'analysis_count': self.analysis_count,
            'cache_hits': self.cache_hits,
            'cache_hit_rate': self.cache_hits / max(self.analysis_count, 1),
            'multi_timeframe_enabled': self.enable_multi_timeframe,
            'lookback_period': self.analyzer.lookback_period
        }