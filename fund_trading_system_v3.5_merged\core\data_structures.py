"""
Core Data Structures for Trading System v3.5

Defines standardized data structures used throughout the trading system,
including market data, trading signals, and performance metrics.
"""

import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum


class ActionType(Enum):
    """Trading action types"""
    BUY = "BUY"
    SELL = "SELL" 
    HOLD = "HOLD"


class RiskLevel(Enum):
    """Risk level categories"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


@dataclass
class MarketData:
    """Enhanced market data structure with comprehensive information"""
    symbol: str
    timestamp: datetime
    
    # Basic OHLCV data
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    # Technical indicators (20-dimensional)
    technical_indicators: Dict[str, float] = field(default_factory=dict)
    
    # CZSC structure data (10-dimensional)
    czsc_structure: Dict[str, Any] = field(default_factory=dict)
    
    # News and sentiment data for LLM analysis
    news_data: Dict[str, Any] = field(default_factory=dict)
    
    # Additional context and metadata
    additional_context: Dict[str, Any] = field(default_factory=dict)
    
    def get_price_change(self) -> float:
        """Calculate price change percentage"""
        if self.open > 0:
            return (self.close - self.open) / self.open
        return 0.0
    
    def get_volatility(self) -> float:
        """Calculate intraday volatility"""
        if self.open > 0:
            return (self.high - self.low) / self.open
        return 0.0
    
    def to_feature_vector(self) -> np.ndarray:
        """Convert to 50-dimensional feature vector"""
        features = np.zeros(50)
        
        # Technical indicators (20 dimensions)
        for i, key in enumerate(sorted(self.technical_indicators.keys())[:20]):
            if i < 20:
                features[i] = self.technical_indicators[key]
        
        # CZSC features (10 dimensions)
        czsc_values = []
        for key in sorted(self.czsc_structure.keys())[:10]:
            value = self.czsc_structure[key]
            if isinstance(value, (int, float)):
                czsc_values.append(float(value))
            else:
                czsc_values.append(0.0)
        
        for i, value in enumerate(czsc_values[:10]):
            features[20 + i] = value
        
        # LLM context features (20 dimensions) - placeholder for now
        # These would be populated by the LLM agent
        for i in range(20):
            features[30 + i] = 0.0  # Will be filled by LLM processing
        
        return features


@dataclass
class TradingSignal:
    """Enhanced trading signal with comprehensive information"""
    symbol: str
    timestamp: datetime
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0.0 to 1.0
    agent_name: str
    reasoning: str
    features: Dict[str, Any] = field(default_factory=dict)
    
    # Risk management information
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    position_size: Optional[float] = None
    
    # Metadata
    processing_time: Optional[float] = None
    model_version: Optional[str] = None
    
    def get_action_enum(self) -> ActionType:
        """Get action as enum"""
        return ActionType(self.action)
    
    def is_actionable(self, min_confidence: float = 0.3) -> bool:
        """Check if signal is actionable based on confidence"""
        return (self.confidence >= min_confidence and 
                self.action in ['BUY', 'SELL'])
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'action': self.action,
            'confidence': self.confidence,
            'agent_name': self.agent_name,
            'reasoning': self.reasoning,
            'features': self.features,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'position_size': self.position_size,
            'processing_time': self.processing_time,
            'model_version': self.model_version
        }


@dataclass
class Position:
    """Trading position information"""
    symbol: str
    quantity: float
    entry_price: float
    entry_time: datetime
    current_price: float
    
    # Risk management
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # Metadata
    agent_signals: List[TradingSignal] = field(default_factory=list)
    
    def get_unrealized_pnl(self) -> float:
        """Calculate unrealized P&L"""
        return (self.current_price - self.entry_price) * self.quantity
    
    def get_unrealized_pnl_pct(self) -> float:
        """Calculate unrealized P&L percentage"""
        if self.entry_price > 0:
            return (self.current_price - self.entry_price) / self.entry_price
        return 0.0
    
    def should_stop_loss(self) -> bool:
        """Check if position should be stopped out"""
        if self.stop_loss is None:
            return False
        
        if self.quantity > 0:  # Long position
            return self.current_price <= self.stop_loss
        else:  # Short position
            return self.current_price >= self.stop_loss
    
    def should_take_profit(self) -> bool:
        """Check if position should take profit"""
        if self.take_profit is None:
            return False
        
        if self.quantity > 0:  # Long position
            return self.current_price >= self.take_profit
        else:  # Short position
            return self.current_price <= self.take_profit


@dataclass
class Trade:
    """Completed trade record"""
    symbol: str
    entry_time: datetime
    exit_time: datetime
    entry_price: float
    exit_price: float
    quantity: float
    
    # Performance metrics
    pnl: float
    pnl_pct: float
    holding_period: timedelta
    
    # Trade metadata
    entry_signals: List[TradingSignal] = field(default_factory=list)
    exit_reason: str = ""
    commission: float = 0.0
    slippage: float = 0.0
    
    @classmethod
    def from_position(cls, position: Position, exit_price: float, 
                     exit_time: datetime, exit_reason: str = "") -> 'Trade':
        """Create trade record from closed position"""
        pnl = (exit_price - position.entry_price) * position.quantity
        pnl_pct = pnl / (position.entry_price * abs(position.quantity)) if position.entry_price > 0 else 0.0
        holding_period = exit_time - position.entry_time
        
        return cls(
            symbol=position.symbol,
            entry_time=position.entry_time,
            exit_time=exit_time,
            entry_price=position.entry_price,
            exit_price=exit_price,
            quantity=position.quantity,
            pnl=pnl,
            pnl_pct=pnl_pct,
            holding_period=holding_period,
            entry_signals=position.agent_signals.copy(),
            exit_reason=exit_reason
        )


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics for trading analysis"""
    
    # Basic metrics
    total_return: float = 0.0
    total_return_pct: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    volatility: float = 0.0
    
    # Trading statistics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    
    # Risk metrics
    var_95: float = 0.0  # Value at Risk (95%)
    expected_shortfall: float = 0.0
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0
    
    # Time-based metrics
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    total_days: int = 0
    trading_days: int = 0
    
    # Benchmark comparison
    benchmark_return: float = 0.0
    alpha: float = 0.0
    beta: float = 0.0
    information_ratio: float = 0.0
    
    # Agent-specific metrics
    agent_performance: Dict[str, Dict[str, float]] = field(default_factory=dict)
    coordination_stats: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def calculate_from_trades(cls, trades: List[Trade], 
                            initial_capital: float = 100000.0,
                            benchmark_returns: Optional[List[float]] = None) -> 'PerformanceMetrics':
        """Calculate performance metrics from trade history"""
        if not trades:
            return cls()
        
        # Basic calculations
        total_pnl = sum(trade.pnl for trade in trades)
        total_return_pct = total_pnl / initial_capital
        
        # Trade statistics
        winning_trades = [t for t in trades if t.pnl > 0]
        losing_trades = [t for t in trades if t.pnl < 0]
        
        win_rate = len(winning_trades) / len(trades) if trades else 0.0
        avg_win = np.mean([t.pnl for t in winning_trades]) if winning_trades else 0.0
        avg_loss = np.mean([t.pnl for t in losing_trades]) if losing_trades else 0.0
        
        profit_factor = (abs(avg_win * len(winning_trades)) / 
                        abs(avg_loss * len(losing_trades))) if losing_trades else float('inf')
        
        # Time-based calculations
        start_date = min(trade.entry_time for trade in trades)
        end_date = max(trade.exit_time for trade in trades)
        total_days = (end_date - start_date).days
        
        # Risk calculations
        returns = [trade.pnl_pct for trade in trades]
        volatility = np.std(returns) if len(returns) > 1 else 0.0
        
        # Sharpe ratio (assuming risk-free rate = 0 for simplicity)
        sharpe_ratio = np.mean(returns) / volatility if volatility > 0 else 0.0
        
        # Max drawdown calculation
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(np.min(drawdowns)) if len(drawdowns) > 0 else 0.0
        
        # VaR calculation (95th percentile)
        var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0.0
        
        return cls(
            total_return=total_pnl,
            total_return_pct=total_return_pct,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            volatility=volatility,
            total_trades=len(trades),
            winning_trades=len(winning_trades),
            losing_trades=len(losing_trades),
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            var_95=var_95,
            start_date=start_date,
            end_date=end_date,
            total_days=total_days
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'total_return': self.total_return,
            'total_return_pct': self.total_return_pct,
            'sharpe_ratio': self.sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'volatility': self.volatility,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'var_95': self.var_95,
            'start_date': self.start_date.isoformat() if self.start_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'total_days': self.total_days,
            'trading_days': self.trading_days,
            'benchmark_return': self.benchmark_return,
            'alpha': self.alpha,
            'beta': self.beta,
            'information_ratio': self.information_ratio,
            'agent_performance': self.agent_performance,
            'coordination_stats': self.coordination_stats
        }
    
    def get_summary_string(self) -> str:
        """Get a formatted summary string"""
        return f"""Performance Summary:
Total Return: {self.total_return_pct:.2%}
Sharpe Ratio: {self.sharpe_ratio:.3f}
Max Drawdown: {self.max_drawdown:.2%}
Win Rate: {self.win_rate:.1%}
Total Trades: {self.total_trades}
Volatility: {self.volatility:.2%}
Profit Factor: {self.profit_factor:.2f}"""


@dataclass
class SystemState:
    """Overall system state information"""
    timestamp: datetime
    active_positions: List[Position] = field(default_factory=list)
    recent_trades: List[Trade] = field(default_factory=list)
    current_performance: Optional[PerformanceMetrics] = None
    
    # System health
    agent_status: Dict[str, str] = field(default_factory=dict)
    coordination_status: str = "operational"
    last_error: Optional[str] = None
    
    # Resource usage
    memory_usage: float = 0.0
    cpu_usage: float = 0.0
    
    def get_total_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """Calculate total portfolio value"""
        total_value = 0.0
        for position in self.active_positions:
            if position.symbol in current_prices:
                position.current_price = current_prices[position.symbol]
                total_value += position.current_price * position.quantity
        return total_value
    
    def get_portfolio_pnl(self) -> float:
        """Calculate total unrealized P&L"""
        return sum(pos.get_unrealized_pnl() for pos in self.active_positions)
    
    def is_healthy(self) -> bool:
        """Check if system is in healthy state"""
        healthy_agents = all(status == "operational" for status in self.agent_status.values())
        return (healthy_agents and 
                self.coordination_status == "operational" and
                self.last_error is None) 