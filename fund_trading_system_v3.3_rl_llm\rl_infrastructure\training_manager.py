"""
训练管理器 - 负责RL模型的训练、评估和管理
支持多种RL算法的训练、超参数优化和模型选择
"""

import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Callable
import os
import json
import pickle
import sys
from collections import deque

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .enhanced_trading_env import EnhancedTradingEnv
from rl_llm_collaboration.rl_decision_optimizer import PPOAgent, RLAlgorithm


class TrainingManager:
    """
    @class TrainingManager
    @brief 训练管理器
    @details 管理RL模型的完整训练流程，包括训练、评估、超参数优化和模型选择
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 训练参数
        self.algorithms = self.config.get('algorithms', ['ppo'])
        self.state_dim = self.config.get('state_dim', 50)
        self.action_dim = self.config.get('action_dim', 2)
        self.hidden_dim = self.config.get('hidden_dim', 256)
        
        # 训练配置
        self.max_episodes = self.config.get('max_episodes', 1000)
        self.max_steps_per_episode = self.config.get('max_steps_per_episode', 1000)
        self.evaluation_frequency = self.config.get('evaluation_frequency', 100)
        self.save_frequency = self.config.get('save_frequency', 200)
        
        # 模型存储
        self.model_save_path = self.config.get('model_save_path', 'models/')
        self.checkpoint_path = self.config.get('checkpoint_path', 'checkpoints/')
        
        # 训练状态
        self.trained_models = {}
        self.training_history = {}
        self.evaluation_results = {}
        self.best_models = {}
        
        # 性能跟踪
        self.training_metrics = {
            'total_episodes': 0,
            'total_training_time': 0.0,
            'best_reward': float('-inf'),
            'convergence_episodes': 0
        }
        
        # 创建目录
        os.makedirs(self.model_save_path, exist_ok=True)
        os.makedirs(self.checkpoint_path, exist_ok=True)
        
        self.logger.info("训练管理器初始化完成")
    
    def train_all_algorithms(self, training_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        @brief 训练所有算法
        @param training_data: 训练数据
        @return: 训练结果摘要
        """
        try:
            self.logger.info("开始训练所有算法...")
            training_summary = {}
            
            for algorithm in self.algorithms:
                self.logger.info(f"开始训练算法: {algorithm}")
                
                # 训练单个算法
                algorithm_result = self.train_single_algorithm(algorithm, training_data)
                training_summary[algorithm] = algorithm_result
                
                # 评估算法性能
                evaluation_result = self.evaluate_algorithm(algorithm)
                training_summary[algorithm]['evaluation'] = evaluation_result
                
                self.logger.info(f"算法 {algorithm} 训练完成")
            
            # 选择最佳模型
            best_algorithm = self.select_best_model()
            training_summary['best_algorithm'] = best_algorithm
            
            # 保存训练摘要
            self._save_training_summary(training_summary)
            
            self.logger.info("所有算法训练完成")
            return training_summary
            
        except Exception as e:
            self.logger.error(f"训练所有算法失败: {str(e)}")
            return {'error': str(e)}
    
    def train_single_algorithm(self, algorithm: str, training_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        @brief 训练单个算法
        @param algorithm: 算法名称
        @param training_data: 训练数据
        @return: 训练结果
        """
        try:
            start_time = datetime.now()
            self.logger.info(f"开始训练 {algorithm} 算法...")
            
            # 创建环境
            env = self._create_training_environment(training_data)
            
            # 创建智能体
            agent = self._create_agent(algorithm)
            
            # 训练循环
            training_result = self._run_training_loop(agent, env, algorithm)
            
            # 计算训练时间
            training_time = (datetime.now() - start_time).total_seconds()
            training_result['training_time'] = training_time
            
            # 保存模型
            model_path = self._save_model(agent, algorithm)
            training_result['model_path'] = model_path
            
            # 更新训练状态
            self.trained_models[algorithm] = agent
            self.training_history[algorithm] = training_result
            
            # 更新性能指标
            self.training_metrics['total_training_time'] += training_time
            if training_result.get('best_reward', float('-inf')) > self.training_metrics['best_reward']:
                self.training_metrics['best_reward'] = training_result['best_reward']
            
            self.logger.info(f"{algorithm} 算法训练完成，耗时: {training_time:.2f}秒")
            return training_result
            
        except Exception as e:
            self.logger.error(f"训练 {algorithm} 算法失败: {str(e)}")
            return {'error': str(e), 'algorithm': algorithm}
    
    def _create_training_environment(self, training_data: Dict[str, Any] = None) -> EnhancedTradingEnv:
        """创建训练环境"""
        try:
            env_config = self.config.get('env_config', {})
            env_config.update({
                'state_dim': self.state_dim,
                'action_dim': self.action_dim,
                'max_steps': self.max_steps_per_episode
            })
            
            env = EnhancedTradingEnv(env_config)
            
            # 如果有训练数据，设置到环境中
            if training_data and 'market_data' in training_data:
                env.set_data(training_data['market_data'])
            
            return env
            
        except Exception as e:
            self.logger.error(f"创建训练环境失败: {str(e)}")
            # 返回默认环境
            return EnhancedTradingEnv()
    
    def _create_agent(self, algorithm: str):
        """创建智能体"""
        try:
            if algorithm.lower() == 'ppo':
                return PPOAgent(
                    state_dim=self.state_dim,
                    action_dim=self.action_dim,
                    hidden_dim=self.hidden_dim,
                    lr=self.config.get('learning_rate', 3e-4),
                    gamma=self.config.get('gamma', 0.99),
                    eps_clip=self.config.get('eps_clip', 0.2)
                )
            else:
                self.logger.warning(f"算法 {algorithm} 未实现，使用PPO")
                return PPOAgent(self.state_dim, self.action_dim, self.hidden_dim)
                
        except Exception as e:
            self.logger.error(f"创建智能体失败: {str(e)}")
            return PPOAgent(self.state_dim, self.action_dim, self.hidden_dim)
    
    def _run_training_loop(self, agent, env, algorithm: str) -> Dict[str, Any]:
        """运行训练循环"""
        try:
            episode_rewards = []
            episode_lengths = []
            best_reward = float('-inf')
            convergence_threshold = self.config.get('convergence_threshold', 0.01)
            convergence_window = self.config.get('convergence_window', 100)
            
            for episode in range(self.max_episodes):
                # 重置环境
                state = env.reset()
                episode_reward = 0.0
                episode_length = 0
                done = False
                
                # 收集轨迹
                trajectory = {
                    'states': [],
                    'actions': [],
                    'rewards': [],
                    'values': [],
                    'log_probs': []
                }
                
                while not done and episode_length < self.max_steps_per_episode:
                    # 智能体动作选择
                    action, value = agent.act(state)
                    
                    # 环境步进
                    next_state, reward, done, info = env.step(action)
                    
                    # 记录轨迹
                    trajectory['states'].append(state)
                    trajectory['actions'].append(action)
                    trajectory['rewards'].append(reward)
                    trajectory['values'].append(value)
                    
                    # 更新状态
                    state = next_state
                    episode_reward += reward
                    episode_length += 1
                
                # 记录episode结果
                episode_rewards.append(episode_reward)
                episode_lengths.append(episode_length)
                
                # 更新最佳奖励
                if episode_reward > best_reward:
                    best_reward = episode_reward
                
                # 定期评估和日志
                if episode % 50 == 0:
                    avg_reward = np.mean(episode_rewards[-50:])
                    self.logger.info(f"Episode {episode}, Avg Reward: {avg_reward:.3f}, Best: {best_reward:.3f}")
                
                # 检查收敛
                if len(episode_rewards) >= convergence_window:
                    recent_rewards = episode_rewards[-convergence_window:]
                    reward_std = np.std(recent_rewards)
                    if reward_std < convergence_threshold:
                        self.logger.info(f"算法 {algorithm} 在第 {episode} episode收敛")
                        self.training_metrics['convergence_episodes'] = episode
                        break
                
                # 定期保存检查点
                if episode % self.save_frequency == 0 and episode > 0:
                    self._save_checkpoint(agent, algorithm, episode)
            
            # 返回训练结果
            training_result = {
                'algorithm': algorithm,
                'total_episodes': len(episode_rewards),
                'episode_rewards': episode_rewards,
                'episode_lengths': episode_lengths,
                'best_reward': best_reward,
                'final_avg_reward': np.mean(episode_rewards[-100:]) if len(episode_rewards) >= 100 else np.mean(episode_rewards),
                'convergence_episode': self.training_metrics.get('convergence_episodes', None),
                'training_completed': True
            }
            
            return training_result
            
        except Exception as e:
            self.logger.error(f"训练循环失败: {str(e)}")
            return {
                'algorithm': algorithm,
                'error': str(e),
                'training_completed': False
            }
    
    def evaluate_algorithm(self, algorithm: str, num_episodes: int = 10) -> Dict[str, Any]:
        """
        @brief 评估算法性能
        @param algorithm: 算法名称
        @param num_episodes: 评估episodes数量
        @return: 评估结果
        """
        try:
            self.logger.info(f"开始评估算法: {algorithm}")
            
            if algorithm not in self.trained_models:
                return {'error': f'算法 {algorithm} 尚未训练'}
            
            agent = self.trained_models[algorithm]
            env = self._create_training_environment()
            
            evaluation_rewards = []
            evaluation_lengths = []
            win_rate = 0
            
            for episode in range(num_episodes):
                state = env.reset()
                episode_reward = 0.0
                episode_length = 0
                done = False
                
                while not done and episode_length < self.max_steps_per_episode:
                    # 使用确定性策略（不添加探索噪声）
                    action = agent.predict(state)
                    state, reward, done, info = env.step(action)
                    
                    episode_reward += reward
                    episode_length += 1
                
                evaluation_rewards.append(episode_reward)
                evaluation_lengths.append(episode_length)
                
                # 统计胜率（正收益率）
                if episode_reward > 0:
                    win_rate += 1
            
            # 计算性能指标
            evaluation_result = {
                'algorithm': algorithm,
                'num_episodes': num_episodes,
                'avg_reward': np.mean(evaluation_rewards),
                'std_reward': np.std(evaluation_rewards),
                'min_reward': np.min(evaluation_rewards),
                'max_reward': np.max(evaluation_rewards),
                'avg_length': np.mean(evaluation_lengths),
                'win_rate': win_rate / num_episodes,
                'sharpe_ratio': self._calculate_sharpe_ratio(evaluation_rewards),
                'max_drawdown': self._calculate_max_drawdown(evaluation_rewards),
                'evaluation_timestamp': datetime.now().isoformat()
            }
            
            # 保存评估结果
            self.evaluation_results[algorithm] = evaluation_result
            
            self.logger.info(f"算法 {algorithm} 评估完成，平均奖励: {evaluation_result['avg_reward']:.3f}")
            return evaluation_result
            
        except Exception as e:
            self.logger.error(f"评估算法 {algorithm} 失败: {str(e)}")
            return {'error': str(e), 'algorithm': algorithm}
    
    def select_best_model(self) -> Dict[str, Any]:
        """
        @brief 选择最佳模型
        @return: 最佳模型信息
        """
        try:
            if not self.evaluation_results:
                return {'error': '没有可用的评估结果'}
            
            best_algorithm = None
            best_score = float('-inf')
            
            # 计算综合评分
            for algorithm, evaluation in self.evaluation_results.items():
                if 'error' in evaluation:
                    continue
                
                # 综合评分公式：平均奖励 - 风险惩罚
                avg_reward = evaluation.get('avg_reward', 0)
                std_reward = evaluation.get('std_reward', 1)
                win_rate = evaluation.get('win_rate', 0)
                sharpe_ratio = evaluation.get('sharpe_ratio', 0)
                
                # 综合评分
                score = (
                    0.4 * avg_reward +
                    0.2 * win_rate + 
                    0.2 * sharpe_ratio - 
                    0.2 * std_reward
                )
                
                if score > best_score:
                    best_score = score
                    best_algorithm = algorithm
            
            if best_algorithm:
                best_model_info = {
                    'best_algorithm': best_algorithm,
                    'best_score': best_score,
                    'evaluation_result': self.evaluation_results[best_algorithm],
                    'model_path': self._get_model_path(best_algorithm),
                    'selection_timestamp': datetime.now().isoformat()
                }
                
                self.best_models['current_best'] = best_model_info
                self.logger.info(f"选择最佳模型: {best_algorithm} (评分: {best_score:.3f})")
                
                return best_model_info
            else:
                return {'error': '无法选择最佳模型'}
                
        except Exception as e:
            self.logger.error(f"选择最佳模型失败: {str(e)}")
            return {'error': str(e)}
    
    def hyperparameter_optimization(self, algorithm: str, param_space: Dict[str, List]) -> Dict[str, Any]:
        """
        @brief 超参数优化
        @param algorithm: 算法名称
        @param param_space: 参数空间
        @return: 最优参数
        """
        try:
            self.logger.info(f"开始 {algorithm} 超参数优化...")
            
            best_params = None
            best_score = float('-inf')
            optimization_results = []
            
            # 网格搜索（简化版）
            import itertools
            
            # 生成参数组合
            param_names = list(param_space.keys())
            param_values = list(param_space.values())
            param_combinations = list(itertools.product(*param_values))
            
            max_combinations = self.config.get('max_param_combinations', 10)
            param_combinations = param_combinations[:max_combinations]
            
            for i, param_combination in enumerate(param_combinations):
                params = dict(zip(param_names, param_combination))
                self.logger.info(f"测试参数组合 {i+1}/{len(param_combinations)}: {params}")
                
                # 更新配置
                temp_config = self.config.copy()
                temp_config.update(params)
                
                # 创建临时训练管理器
                temp_manager = TrainingManager(temp_config)
                
                # 训练并评估
                training_result = temp_manager.train_single_algorithm(algorithm)
                evaluation_result = temp_manager.evaluate_algorithm(algorithm, num_episodes=5)
                
                if 'error' not in evaluation_result:
                    score = evaluation_result.get('avg_reward', float('-inf'))
                    optimization_results.append({
                        'params': params,
                        'score': score,
                        'evaluation': evaluation_result
                    })
                    
                    if score > best_score:
                        best_score = score
                        best_params = params
            
            optimization_summary = {
                'algorithm': algorithm,
                'best_params': best_params,
                'best_score': best_score,
                'all_results': optimization_results,
                'optimization_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"{algorithm} 超参数优化完成，最佳评分: {best_score:.3f}")
            return optimization_summary
            
        except Exception as e:
            self.logger.error(f"超参数优化失败: {str(e)}")
            return {'error': str(e)}
    
    def _save_model(self, agent, algorithm: str) -> str:
        """保存模型"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            model_filename = f"{algorithm}_{timestamp}.pth"
            model_path = os.path.join(self.model_save_path, model_filename)
            
            if hasattr(agent, 'policy_net'):
                checkpoint = {
                    'policy_net': agent.policy_net.state_dict(),
                    'value_net': agent.value_net.state_dict() if hasattr(agent, 'value_net') else {},
                    'algorithm': algorithm,
                    'state_dim': self.state_dim,
                    'action_dim': self.action_dim,
                    'hidden_dim': self.hidden_dim,
                    'save_timestamp': timestamp,
                    'training_config': self.config
                }
                torch.save(checkpoint, model_path)
                self.logger.info(f"模型已保存: {model_path}")
                return model_path
            else:
                self.logger.warning(f"智能体 {algorithm} 没有可保存的模型")
                return ""
                
        except Exception as e:
            self.logger.error(f"保存模型失败: {str(e)}")
            return ""
    
    def _save_checkpoint(self, agent, algorithm: str, episode: int) -> None:
        """保存训练检查点"""
        try:
            checkpoint_filename = f"{algorithm}_checkpoint_ep{episode}.pth"
            checkpoint_path = os.path.join(self.checkpoint_path, checkpoint_filename)
            
            if hasattr(agent, 'policy_net'):
                checkpoint = {
                    'policy_net': agent.policy_net.state_dict(),
                    'value_net': agent.value_net.state_dict() if hasattr(agent, 'value_net') else {},
                    'episode': episode,
                    'algorithm': algorithm,
                    'save_timestamp': datetime.now().isoformat()
                }
                torch.save(checkpoint, checkpoint_path)
                self.logger.debug(f"检查点已保存: {checkpoint_path}")
                
        except Exception as e:
            self.logger.error(f"保存检查点失败: {str(e)}")
    
    def _save_training_summary(self, training_summary: Dict[str, Any]) -> None:
        """保存训练摘要"""
        try:
            summary_filename = f"training_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            summary_path = os.path.join(self.model_save_path, summary_filename)
            
            # 转换numpy类型为Python原生类型
            serializable_summary = self._make_serializable(training_summary)
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_summary, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"训练摘要已保存: {summary_path}")
            
        except Exception as e:
            self.logger.error(f"保存训练摘要失败: {str(e)}")
    
    def _make_serializable(self, obj) -> Any:
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32)):
            return int(obj)
        elif isinstance(obj, (np.float64, np.float32)):
            return float(obj)
        else:
            return obj
    
    def _calculate_sharpe_ratio(self, rewards: List[float]) -> float:
        """计算夏普比率"""
        try:
            if len(rewards) < 2:
                return 0.0
            
            mean_return = np.mean(rewards)
            std_return = np.std(rewards)
            
            if std_return > 0:
                return mean_return / std_return
            else:
                return 0.0
        except:
            return 0.0
    
    def _calculate_max_drawdown(self, rewards: List[float]) -> float:
        """计算最大回撤"""
        try:
            if len(rewards) == 0:
                return 0.0
            
            cumulative_rewards = np.cumsum(rewards)
            peak = np.maximum.accumulate(cumulative_rewards)
            drawdowns = (peak - cumulative_rewards) / peak
            
            return float(np.max(drawdowns))
        except:
            return 0.0
    
    def _get_model_path(self, algorithm: str) -> str:
        """获取模型路径"""
        try:
            model_files = [f for f in os.listdir(self.model_save_path) if f.startswith(algorithm) and f.endswith('.pth')]
            if model_files:
                # 返回最新的模型文件
                model_files.sort(reverse=True)
                return os.path.join(self.model_save_path, model_files[0])
            else:
                return ""
        except:
            return ""
    
    def load_model(self, model_path: str, algorithm: str = None):
        """
        @brief 加载模型
        @param model_path: 模型路径
        @param algorithm: 算法名称
        @return: 加载的智能体
        """
        try:
            if not os.path.exists(model_path):
                self.logger.error(f"模型文件不存在: {model_path}")
                return None
            
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # 获取算法类型
            model_algorithm = algorithm or checkpoint.get('algorithm', 'ppo')
            
            # 创建智能体
            agent = self._create_agent(model_algorithm)
            
            # 加载模型参数
            if hasattr(agent, 'policy_net') and 'policy_net' in checkpoint:
                agent.policy_net.load_state_dict(checkpoint['policy_net'])
            if hasattr(agent, 'value_net') and 'value_net' in checkpoint:
                agent.value_net.load_state_dict(checkpoint['value_net'])
            
            self.logger.info(f"模型加载成功: {model_path}")
            return agent
            
        except Exception as e:
            self.logger.error(f"加载模型失败: {str(e)}")
            return None
    
    def get_training_metrics(self) -> Dict[str, Any]:
        """获取训练指标"""
        metrics = self.training_metrics.copy()
        metrics['trained_algorithms'] = list(self.trained_models.keys())
        metrics['available_evaluations'] = list(self.evaluation_results.keys())
        return metrics
    
    def get_training_history(self, algorithm: str = None) -> Dict[str, Any]:
        """获取训练历史"""
        if algorithm:
            return self.training_history.get(algorithm, {})
        else:
            return self.training_history.copy()
    
    def get_evaluation_results(self, algorithm: str = None) -> Dict[str, Any]:
        """获取评估结果"""
        if algorithm:
            return self.evaluation_results.get(algorithm, {})
        else:
            return self.evaluation_results.copy()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'algorithms': ['ppo'],
            'state_dim': 50,
            'action_dim': 2,
            'hidden_dim': 256,
            'max_episodes': 1000,
            'max_steps_per_episode': 1000,
            'evaluation_frequency': 100,
            'save_frequency': 200,
            'learning_rate': 3e-4,
            'gamma': 0.99,
            'eps_clip': 0.2,
            'convergence_threshold': 0.01,
            'convergence_window': 100,
            'max_param_combinations': 10,
            'model_save_path': 'models/',
            'checkpoint_path': 'checkpoints/',
            'env_config': {
                'initial_balance': 100000.0,
                'transaction_cost': 0.001,
                'max_position_size': 1.0
            }
        } 