2025-07-22 16:18:41,208 - TradingConfig - INFO - Configuration validation passed
2025-07-22 16:18:41,208 - TradingConfig - INFO - Trading configuration initialized successfully
2025-07-22 16:18:41,208 - RLTrainingMain - INFO - Configuration loaded successfully
2025-07-22 16:18:41,209 - <PERSON><PERSON><PERSON>ger - INFO - Data manager initialized with data directory: data
2025-07-22 16:18:41,209 - DataPreparation - INFO - Loading training data for 000001.SZ: 2020-01-01 to 2022-12-31
2025-07-22 16:18:41,218 - Data<PERSON>anager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:18:41,220 - DataManager - INFO - Loaded 49 records for 000001.SZ from 2020-01-01 to 2022-12-31
2025-07-22 16:18:41,221 - DataPreparation - INFO - Loading validation data for 000001.SZ: 2023-01-01 to 2023-06-30
2025-07-22 16:18:41,232 - <PERSON><PERSON><PERSON>ger - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:18:41,234 - Data<PERSON>anager - INFO - Loaded 0 records for 000001.SZ from 2023-01-01 to 2023-06-30
2025-07-22 16:18:41,234 - DataPreparation - INFO - Training data: 49 records
2025-07-22 16:18:41,234 - DataPreparation - INFO - Validation data: 0 records
2025-07-22 16:18:41,249 - TradingEnvironment - INFO - Trading environment initialized with 29 steps
2025-07-22 16:18:41,261 - TradingEnvironment - INFO - Trading environment initialized with -20 steps
2025-07-22 16:18:41,262 - EnvironmentSetup - INFO - Training and validation environments created
2025-07-22 16:18:41,263 - RLModelManager - INFO - RL Model Manager initialized with models directory: models
2025-07-22 16:18:41,264 - TrainingSession - INFO - Creating RL model...
2025-07-22 16:18:41,274 - RLModelManager - INFO - Created DQN model with architecture: {'state_dim': 50, 'action_dim': 3, 'hidden_dims': [256, 256, 128]}
2025-07-22 16:18:41,274 - TrainingSession - INFO - Starting model training...
2025-07-22 16:18:41,274 - RLModelManager - INFO - Starting model training for 50 episodes
2025-07-22 16:18:41,276 - RLTrainingMain - ERROR - Training failed: State vector must be 50-dimensional, got 51
2025-07-22 16:19:46,757 - TradingConfig - INFO - Configuration validation passed
2025-07-22 16:19:46,757 - TradingConfig - INFO - Trading configuration initialized successfully
2025-07-22 16:19:46,757 - RLTrainingMain - INFO - Configuration loaded successfully
2025-07-22 16:19:46,758 - DataManager - INFO - Data manager initialized with data directory: data
2025-07-22 16:19:46,758 - DataPreparation - INFO - Loading training data for 000001.SZ: 2020-01-01 to 2022-12-31
2025-07-22 16:19:46,767 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:19:46,768 - DataManager - INFO - Loaded 49 records for 000001.SZ from 2020-01-01 to 2022-12-31
2025-07-22 16:19:46,769 - DataPreparation - INFO - Loading validation data for 000001.SZ: 2023-01-01 to 2023-06-30
2025-07-22 16:19:46,779 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:19:46,781 - DataManager - INFO - Loaded 0 records for 000001.SZ from 2023-01-01 to 2023-06-30
2025-07-22 16:19:46,781 - DataPreparation - INFO - Training data: 49 records
2025-07-22 16:19:46,781 - DataPreparation - INFO - Validation data: 0 records
2025-07-22 16:19:46,792 - TradingEnvironment - INFO - Trading environment initialized with 29 steps
2025-07-22 16:19:46,799 - TradingEnvironment - INFO - Trading environment initialized with -20 steps
2025-07-22 16:19:46,799 - EnvironmentSetup - INFO - Training and validation environments created
2025-07-22 16:19:46,800 - RLModelManager - INFO - RL Model Manager initialized with models directory: models
2025-07-22 16:19:46,801 - TrainingSession - INFO - Creating RL model...
2025-07-22 16:19:46,803 - RLModelManager - INFO - Created DQN model with architecture: {'state_dim': 50, 'action_dim': 3, 'hidden_dims': [256, 256, 128]}
2025-07-22 16:19:46,803 - TrainingSession - INFO - Starting model training...
2025-07-22 16:19:46,804 - RLModelManager - INFO - Starting model training for 10 episodes
2025-07-22 16:19:46,806 - RLTrainingMain - ERROR - Training failed: State vector must be 50-dimensional, got 51
2025-07-22 16:20:37,307 - TradingConfig - INFO - Configuration validation passed
2025-07-22 16:20:37,307 - TradingConfig - INFO - Trading configuration initialized successfully
2025-07-22 16:20:37,308 - RLTrainingMain - INFO - Configuration loaded successfully
2025-07-22 16:20:37,308 - DataManager - INFO - Data manager initialized with data directory: data
2025-07-22 16:20:37,309 - DataPreparation - INFO - Loading training data for 000001.SZ: 2020-01-01 to 2022-12-31
2025-07-22 16:20:37,319 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:20:37,321 - DataManager - INFO - Loaded 49 records for 000001.SZ from 2020-01-01 to 2022-12-31
2025-07-22 16:20:37,321 - DataPreparation - INFO - Loading validation data for 000001.SZ: 2023-01-01 to 2023-06-30
2025-07-22 16:20:37,333 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:20:37,335 - DataManager - INFO - Loaded 0 records for 000001.SZ from 2023-01-01 to 2023-06-30
2025-07-22 16:20:37,335 - DataPreparation - INFO - Training data: 49 records
2025-07-22 16:20:37,335 - DataPreparation - INFO - Validation data: 0 records
2025-07-22 16:20:37,348 - TradingEnvironment - INFO - Trading environment initialized with 29 steps
2025-07-22 16:20:37,358 - TradingEnvironment - INFO - Trading environment initialized with -20 steps
2025-07-22 16:20:37,358 - EnvironmentSetup - INFO - Training and validation environments created
2025-07-22 16:20:37,359 - RLModelManager - INFO - RL Model Manager initialized with models directory: models
2025-07-22 16:20:37,360 - TrainingSession - INFO - Creating RL model...
2025-07-22 16:20:37,362 - RLModelManager - INFO - Created DQN model with architecture: {'state_dim': 50, 'action_dim': 3, 'hidden_dims': [256, 256, 128]}
2025-07-22 16:20:37,362 - TrainingSession - INFO - Starting model training...
2025-07-22 16:20:37,362 - RLModelManager - INFO - Starting model training for 20 episodes
2025-07-22 16:20:37,391 - RLModelManager - INFO - Episode 0/20, Avg Reward: 5.40, Epsilon: 0.995
2025-07-22 16:20:37,880 - RLModelManager - INFO - Running model validation...
2025-07-22 16:20:37,880 - RLTrainingMain - ERROR - Training failed: single positional indexer is out-of-bounds
2025-07-22 16:23:11,451 - TradingConfig - INFO - Configuration validation passed
2025-07-22 16:23:11,451 - TradingConfig - INFO - Trading configuration initialized successfully
2025-07-22 16:23:11,452 - RLTrainingMain - INFO - Configuration loaded successfully
2025-07-22 16:23:11,453 - DataManager - INFO - Data manager initialized with data directory: data
2025-07-22 16:23:11,453 - DataPreparation - INFO - Loading training data for 000001.SZ: 2020-01-01 to 2020-02-29
2025-07-22 16:23:11,464 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:23:11,465 - DataManager - INFO - Loaded 35 records for 000001.SZ from 2020-01-01 to 2020-02-29
2025-07-22 16:23:11,465 - DataPreparation - INFO - Loading validation data for 000001.SZ: 2020-03-01 to 2020-03-24
2025-07-22 16:23:11,474 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:23:11,476 - DataManager - INFO - Loaded 14 records for 000001.SZ from 2020-03-01 to 2020-03-24
2025-07-22 16:23:11,476 - DataPreparation - INFO - Training data: 35 records
2025-07-22 16:23:11,476 - DataPreparation - INFO - Validation data: 14 records
2025-07-22 16:23:11,492 - TradingEnvironment - INFO - Trading environment initialized with 15 steps
2025-07-22 16:23:11,501 - TradingEnvironment - INFO - Trading environment initialized with -6 steps
2025-07-22 16:23:11,502 - EnvironmentSetup - INFO - Training and validation environments created
2025-07-22 16:23:11,503 - RLModelManager - INFO - RL Model Manager initialized with models directory: models
2025-07-22 16:23:11,505 - TrainingSession - INFO - Creating RL model...
2025-07-22 16:23:11,506 - RLModelManager - INFO - Created DQN model with architecture: {'state_dim': 50, 'action_dim': 3, 'hidden_dims': [256, 256, 128]}
2025-07-22 16:23:11,506 - TrainingSession - INFO - Starting model training...
2025-07-22 16:23:11,507 - RLModelManager - INFO - Starting model training for 20 episodes
2025-07-22 16:23:11,526 - RLModelManager - INFO - Episode 0/20, Avg Reward: 1.90, Epsilon: 0.995
2025-07-22 16:23:11,784 - RLModelManager - INFO - Running model validation...
2025-07-22 16:23:11,785 - RLTrainingMain - ERROR - Training failed: single positional indexer is out-of-bounds
2025-07-22 16:24:20,082 - TradingConfig - INFO - Configuration validation passed
2025-07-22 16:24:20,082 - TradingConfig - INFO - Trading configuration initialized successfully
2025-07-22 16:24:20,083 - RLTrainingMain - INFO - Configuration loaded successfully
2025-07-22 16:24:20,083 - DataManager - INFO - Data manager initialized with data directory: data
2025-07-22 16:24:20,085 - DataPreparation - INFO - Loading training data for 000001.SZ: 2020-01-01 to 2020-02-29
2025-07-22 16:24:20,096 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:24:20,098 - DataManager - INFO - Loaded 35 records for 000001.SZ from 2020-01-01 to 2020-02-29
2025-07-22 16:24:20,098 - DataPreparation - INFO - Loading validation data for 000001.SZ: 2020-03-01 to 2020-03-24
2025-07-22 16:24:20,106 - DataManager - WARNING - Removed 4 invalid records for 000001.SZ
2025-07-22 16:24:20,108 - DataManager - INFO - Loaded 14 records for 000001.SZ from 2020-03-01 to 2020-03-24
2025-07-22 16:24:20,109 - DataPreparation - INFO - Training data: 35 records
2025-07-22 16:24:20,109 - DataPreparation - INFO - Validation data: 14 records
2025-07-22 16:24:20,123 - TradingEnvironment - INFO - Trading environment initialized with 15 steps
2025-07-22 16:24:20,136 - RLTrainingMain - ERROR - Training failed: Insufficient data: need at least 21 records, got 14 records. Calculated steps: -6
