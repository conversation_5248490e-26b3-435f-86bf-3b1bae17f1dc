import paddle
import paddle.nn as nn
import paddle.nn.functional as F
import parl
import numpy as np

LOG_SIG_MAX = 1.0
LOG_SIG_MIN = -1e9

class EnhancedStockModel(parl.Model):
    """增强版股票模型 - 支持50维输入"""
    
    def __init__(self, obs_dim=50, action_dim=2):
        super(EnhancedStockModel, self).__init__()
        self.actor_model = EnhancedActor(obs_dim, action_dim)
        self.critic_model = EnhancedCritic(obs_dim, action_dim)

    def policy(self, obs):
        return self.actor_model(obs)

    def value(self, obs, action):
        return self.critic_model(obs, action)

    def get_actor_params(self):
        return self.actor_model.parameters()

    def get_critic_params(self):
        return self.critic_model.parameters()


class EnhancedActor(parl.Model):
    """增强版Actor网络 - 更深的网络结构处理50维输入"""
    
    def __init__(self, obs_dim=50, action_dim=2):
        super(EnhancedActor, self).__init__()
        
        # 特征提取层 - 分层处理不同类型的特征
        self.price_encoder = nn.Sequential(
            nn.Linear(6, 32),  # 价格特征编码器
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.account_encoder = nn.Sequential(
            nn.Linear(7, 32),  # 账户特征编码器
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.technical_encoder = nn.Sequential(
            nn.Linear(15, 64),  # 技术指标编码器
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.czsc_encoder = nn.Sequential(
            nn.Linear(10, 32),  # CZSC特征编码器
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.dimension_encoder = nn.Sequential(
            nn.Linear(6, 24),  # 六大维度编码器
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.market_encoder = nn.Sequential(
            nn.Linear(6, 24),  # 市场环境编码器
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 特征融合层
        total_encoded_dim = 32 + 32 + 64 + 32 + 24 + 24  # 208维
        
        self.fusion_layers = nn.Sequential(
            nn.Linear(total_encoded_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU()
        )
        
        # 输出层
        self.mean_linear = nn.Linear(128, action_dim)
        self.std_linear = nn.Linear(128, action_dim)
        
        # 注意力机制 (可选)
        self.attention = nn.MultiHeadAttention(embed_dim=128, num_heads=4)

    def forward(self, obs):
        # 分解输入特征
        price_features = obs[:, :6]          # 价格特征
        account_features = obs[:, 6:13]      # 账户特征  
        technical_features = obs[:, 13:28]   # 技术指标特征
        czsc_features = obs[:, 28:38]        # CZSC特征
        dimension_features = obs[:, 38:44]   # 六大维度特征
        market_features = obs[:, 44:50]      # 市场环境特征
        
        # 分别编码各类特征
        price_encoded = self.price_encoder(price_features)
        account_encoded = self.account_encoder(account_features)
        technical_encoded = self.technical_encoder(technical_features)
        czsc_encoded = self.czsc_encoder(czsc_features)
        dimension_encoded = self.dimension_encoder(dimension_features)
        market_encoded = self.market_encoder(market_features)
        
        # 特征融合
        fused_features = paddle.concat([
            price_encoded, account_encoded, technical_encoded,
            czsc_encoded, dimension_encoded, market_encoded
        ], axis=1)
        
        # 深度特征提取
        x = self.fusion_layers(fused_features)
        
        # 可选：应用注意力机制
        # x_reshaped = x.unsqueeze(1)  # [batch, 1, 128]
        # x_attended, _ = self.attention(x_reshaped, x_reshaped, x_reshaped)
        # x = x_attended.squeeze(1)
        
        # 输出动作分布参数
        act_mean = self.mean_linear(x)
        act_std = self.std_linear(x)
        act_log_std = paddle.clip(act_std, min=LOG_SIG_MIN, max=LOG_SIG_MAX)
        
        return act_mean, act_log_std


class EnhancedCritic(parl.Model):
    """增强版Critic网络 - 双Q网络结构"""
    
    def __init__(self, obs_dim=50, action_dim=2):
        super(EnhancedCritic, self).__init__()
        
        # 状态编码器 (与Actor共享类似结构)
        self.state_encoder = nn.Sequential(
            nn.Linear(obs_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 动作编码器
        self.action_encoder = nn.Sequential(
            nn.Linear(action_dim, 32),
            nn.ReLU()
        )
        
        # Q1 网络
        self.q1_network = nn.Sequential(
            nn.Linear(128 + 32, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )
        
        # Q2 网络
        self.q2_network = nn.Sequential(
            nn.Linear(128 + 32, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 1)
        )

    def forward(self, obs, action):
        # 编码状态和动作
        state_encoded = self.state_encoder(obs)
        action_encoded = self.action_encoder(action)
        
        # 拼接状态和动作特征
        combined_features = paddle.concat([state_encoded, action_encoded], axis=1)
        
        # 计算两个Q值
        q1 = self.q1_network(combined_features)
        q2 = self.q2_network(combined_features)
        
        return q1, q2


class LLMEnhancedActor(EnhancedActor):
    """LLM增强版Actor - 集成LLM分析结果"""
    
    def __init__(self, obs_dim=50, action_dim=2, llm_feature_dim=10):
        super(LLMEnhancedActor, self).__init__(obs_dim, action_dim)
        
        # LLM特征编码器
        self.llm_encoder = nn.Sequential(
            nn.Linear(llm_feature_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 重新定义融合层以包含LLM特征
        total_encoded_dim = 32 + 32 + 64 + 32 + 24 + 24 + 32  # 240维
        
        self.fusion_layers = nn.Sequential(
            nn.Linear(total_encoded_dim, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU()
        )
    
    def forward(self, obs, llm_features=None):
        # 原有特征编码
        price_features = obs[:, :6]
        account_features = obs[:, 6:13]
        technical_features = obs[:, 13:28]
        czsc_features = obs[:, 28:38]
        dimension_features = obs[:, 38:44]
        market_features = obs[:, 44:50]
        
        price_encoded = self.price_encoder(price_features)
        account_encoded = self.account_encoder(account_features)
        technical_encoded = self.technical_encoder(technical_features)
        czsc_encoded = self.czsc_encoder(czsc_features)
        dimension_encoded = self.dimension_encoder(dimension_features)
        market_encoded = self.market_encoder(market_features)
        
        # LLM特征编码
        if llm_features is not None:
            llm_encoded = self.llm_encoder(llm_features)
        else:
            # 如果没有LLM特征，使用零向量
            batch_size = obs.shape[0]
            llm_encoded = paddle.zeros([batch_size, 32])
        
        # 特征融合 (包含LLM特征)
        fused_features = paddle.concat([
            price_encoded, account_encoded, technical_encoded,
            czsc_encoded, dimension_encoded, market_encoded, llm_encoded
        ], axis=1)
        
        x = self.fusion_layers(fused_features)
        
        act_mean = self.mean_linear(x)
        act_std = self.std_linear(x)
        act_log_std = paddle.clip(act_std, min=LOG_SIG_MIN, max=LOG_SIG_MAX)
        
        return act_mean, act_log_std


class AdaptiveRewardShaper:
    """自适应奖励塑形器 - 基于LLM分析调整奖励函数"""
    
    def __init__(self):
        self.base_reward_weight = 1.0
        self.llm_confidence_weight = 0.3
        self.risk_penalty_weight = 0.2
        
    def shape_reward(self, base_reward: float, llm_analysis: dict = None, 
                    market_data: dict = None) -> float:
        """
        基于LLM分析塑形奖励
        
        Args:
            base_reward: 基础奖励
            llm_analysis: LLM分析结果
            market_data: 市场数据
            
        Returns:
            塑形后的奖励
        """
        if llm_analysis is None:
            return base_reward
        
        shaped_reward = base_reward * self.base_reward_weight
        
        # LLM置信度调整
        confidence = llm_analysis.get('confidence_level', 0.5)
        confidence_multiplier = 1.0 + (confidence - 0.5) * self.llm_confidence_weight
        shaped_reward *= confidence_multiplier
        
        # 市场情绪调整
        sentiment = llm_analysis.get('market_sentiment', '中性')
        sentiment_multiplier = {
            '积极': 1.1,
            '中性': 1.0,
            '谨慎': 0.9
        }.get(sentiment, 1.0)
        shaped_reward *= sentiment_multiplier
        
        # 风险惩罚
        risk_points = llm_analysis.get('risk_points', [])
        risk_penalty = len(risk_points) * self.risk_penalty_weight
        shaped_reward -= risk_penalty
        
        return shaped_reward


def create_enhanced_model_config():
    """创建增强模型配置"""
    return {
        'obs_dim': 50,
        'action_dim': 2,
        'actor_hidden_dims': [512, 256, 128],
        'critic_hidden_dims': [256, 128],
        'dropout_rate': 0.1,
        'use_attention': False,
        'use_llm_enhancement': True,
        'llm_feature_dim': 10
    }


# 使用示例
if __name__ == "__main__":
    # 创建增强模型
    config = create_enhanced_model_config()
    model = EnhancedStockModel(
        obs_dim=config['obs_dim'],
        action_dim=config['action_dim']
    )
    
    # 测试模型
    batch_size = 32
    obs = paddle.randn([batch_size, 50])
    action = paddle.randn([batch_size, 2])
    
    # 测试Actor
    act_mean, act_log_std = model.policy(obs)
    print(f"Actor输出 - Mean shape: {act_mean.shape}, Std shape: {act_log_std.shape}")
    
    # 测试Critic
    q1, q2 = model.value(obs, action)
    print(f"Critic输出 - Q1 shape: {q1.shape}, Q2 shape: {q2.shape}")
    
    print("增强模型测试完成！")