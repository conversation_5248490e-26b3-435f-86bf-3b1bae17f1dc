#!/usr/bin/env python3
"""
RL Model Training Script

Comprehensive script for training reinforcement learning models
for trading strategy development.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, <PERSON><PERSON>
import itertools

from core.config import TradingConfig
from rl_infrastructure.trading_environment import TradingEnvironment
from rl_infrastructure.model_manager import RLModelManager, SimpleDQN
from backtest.engines.data_manager import DataManager

# Import enhanced training system
try:
    from rl_infrastructure.enhanced_training_system import (
        EnhancedTrainingSystem,
        BayesianHyperparameterOptimizer,
        AdaptiveLearningRateScheduler,
        EarlyStoppingManager,
        TrainingProgressMonitor
    )
    ENHANCED_TRAINING_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Enhanced training system not available: {e}")
    ENHANCED_TRAINING_AVAILABLE = False


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('rl_training.log')
        ]
    )


def prepare_training_data(data_manager: DataManager, 
                         symbol: str,
                         train_start: str,
                         train_end: str,
                         val_start: str,
                         val_end: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Prepare training and validation datasets"""
    logger = logging.getLogger("DataPreparation")
    
    # Load training data
    logger.info(f"Loading training data for {symbol}: {train_start} to {train_end}")
    train_data = data_manager.load_data(
        symbol=symbol,
        start_date=train_start,
        end_date=train_end
    )
    
    # Load validation data
    logger.info(f"Loading validation data for {symbol}: {val_start} to {val_end}")
    val_data = data_manager.load_data(
        symbol=symbol,
        start_date=val_start,
        end_date=val_end
    )
    
    logger.info(f"Training data: {len(train_data)} records")
    logger.info(f"Validation data: {len(val_data)} records")
    
    return train_data, val_data


def create_training_environments(train_data: pd.DataFrame,
                               val_data: pd.DataFrame,
                               config: TradingConfig) -> Tuple[TradingEnvironment, TradingEnvironment]:
    """Create training and validation environments"""
    logger = logging.getLogger("EnvironmentSetup")
    
    # Training environment
    train_env = TradingEnvironment(
        data=train_data,
        config=config,
        initial_capital=config.backtest_config.initial_capital,
        transaction_cost=config.backtest_config.commission,
        max_position=config.risk_config.max_position_size
    )
    
    # Validation environment
    val_env = TradingEnvironment(
        data=val_data,
        config=config,
        initial_capital=config.backtest_config.initial_capital,
        transaction_cost=config.backtest_config.commission,
        max_position=config.risk_config.max_position_size
    )
    
    logger.info("Training and validation environments created")
    return train_env, val_env


def run_training_session(model_manager: RLModelManager,
                        train_env: TradingEnvironment,
                        val_env: TradingEnvironment,
                        training_config: Dict[str, Any]) -> SimpleDQN:
    """Run complete training session"""
    logger = logging.getLogger("TrainingSession")
    
    # Create model
    logger.info("Creating RL model...")
    model = model_manager.create_model(
        algorithm=training_config['algorithm'],
        architecture_config=training_config['architecture']
    )
    
    # Train model
    logger.info("Starting model training...")
    training_result = model_manager.train_model(
        model=model,
        training_env=train_env,
        validation_env=val_env,
        episodes=training_config['episodes'],
        learning_rate=training_config['learning_rate'],
        epsilon_start=training_config['epsilon_start'],
        epsilon_end=training_config['epsilon_end'],
        epsilon_decay=training_config['epsilon_decay'],
        batch_size=training_config['batch_size'],
        save_frequency=training_config['save_frequency']
    )
    
    # Save trained model
    logger.info("Saving trained model...")
    model_path = model_manager.save_model(
        model=model,
        model_id=training_result.model_id,
        version=training_result.version,
        description=f"RL model trained on {training_config['episodes']} episodes"
    )
    
    logger.info(f"Training completed successfully!")
    logger.info(f"Model saved to: {model_path}")
    logger.info(f"Final performance: {training_result.final_performance}")
    
    return model


def evaluate_model(model: SimpleDQN,
                  test_env: TradingEnvironment,
                  num_episodes: int = 10) -> Dict[str, float]:
    """Evaluate trained model performance"""
    logger = logging.getLogger("ModelEvaluation")
    
    logger.info(f"Evaluating model on {num_episodes} episodes...")
    
    evaluation_results = []
    
    for episode in range(num_episodes):
        state = test_env.reset()
        total_reward = 0
        done = False
        steps = 0
        
        while not done and steps < 1000:  # Limit steps to prevent infinite loops
            action = model.predict(state)
            state, reward, done, info = test_env.step(action)
            total_reward += reward
            steps += 1
        
        episode_stats = test_env.get_portfolio_stats()
        episode_stats['total_reward'] = total_reward
        episode_stats['steps'] = steps
        evaluation_results.append(episode_stats)
        
        logger.debug(f"Episode {episode + 1}: Reward={total_reward:.2f}, "
                    f"Portfolio={episode_stats.get('portfolio_value', 0):.2f}")
    
    # Calculate average performance
    avg_performance = {}
    if evaluation_results:
        for key in evaluation_results[0].keys():
            values = [result[key] for result in evaluation_results if key in result]
            if values:
                avg_performance[f"avg_{key}"] = np.mean(values)
                avg_performance[f"std_{key}"] = np.std(values)
    
    logger.info(f"Evaluation completed. Average performance: {avg_performance}")
    return avg_performance


def save_evaluation_report(evaluation_results: Dict[str, float],
                          training_config: Dict[str, Any],
                          output_dir: str):
    """Save comprehensive evaluation report"""
    logger = logging.getLogger("ReportGeneration")
    
    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Generate report
    report = {
        "evaluation_timestamp": datetime.now().isoformat(),
        "training_configuration": training_config,
        "evaluation_results": evaluation_results,
        "summary": {
            "model_performance": "Good" if evaluation_results.get('avg_total_return', 0) > 0 else "Needs Improvement",
            "recommendation": "Deploy to production" if evaluation_results.get('avg_sharpe_ratio', 0) > 1.0 else "Requires further optimization"
        }
    }
    
    # Save report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = output_path / f"rl_training_report_{timestamp}.json"
    
    import json
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Evaluation report saved to: {report_file}")
    return str(report_file)


def run_hyperparameter_search(param_grid: Dict[str, list],
                             base_config: Dict[str, Any],
                             model_manager: RLModelManager,
                             train_env: TradingEnvironment,
                             val_env: TradingEnvironment,
                             max_trials: int = 5) -> pd.DataFrame:
    """批量超参数搜索与对比"""
    logger = logging.getLogger("HyperparamSearch")
    keys, values = zip(*param_grid.items())
    results = []
    for i, combo in enumerate(itertools.product(*values)):
        if i >= max_trials:
            break
        trial_config = base_config.copy()
        trial_config.update(dict(zip(keys, combo)))
        logger.info(f"Trial {i+1}: {trial_config}")
        model = model_manager.create_model(
            algorithm=trial_config['algorithm'],
            architecture_config=trial_config['architecture']
        )
        result = model_manager.train_model(
            model=model,
            training_env=train_env,
            validation_env=val_env,
            episodes=trial_config['episodes'],
            learning_rate=trial_config['learning_rate'],
            epsilon_start=trial_config['epsilon_start'],
            epsilon_end=trial_config['epsilon_end'],
            epsilon_decay=trial_config['epsilon_decay'],
            batch_size=trial_config['batch_size'],
            save_frequency=trial_config['save_frequency']
        )
        perf = result.final_performance.copy()
        perf['trial'] = i+1
        perf['config'] = str(trial_config)
        results.append(perf)
    import pandas as pd
    df = pd.DataFrame(results)
    logger.info(f"Hyperparameter search completed. Results:\n{df}")
    return df


def train_with_enhanced_system(training_config: Dict[str, Any],
                             model_manager: RLModelManager,
                             train_env: TradingEnvironment,
                             val_env: TradingEnvironment) -> Dict[str, Any]:
    """Train model using enhanced training system with Bayesian optimization"""
    if not ENHANCED_TRAINING_AVAILABLE:
        logger = logging.getLogger("EnhancedTraining")
        logger.warning("Enhanced training system not available, falling back to basic training")
        return {}

    logger = logging.getLogger("EnhancedTraining")
    logger.info("Starting enhanced RL training with Bayesian optimization")

    # Define hyperparameter search space
    search_space = {
        'learning_rate': {
            'type': 'real',
            'low': 1e-5,
            'high': 1e-2
        },
        'batch_size': {
            'type': 'categorical',
            'choices': [16, 32, 64, 128]
        },
        'epsilon_decay': {
            'type': 'real',
            'low': 0.99,
            'high': 0.999
        },
        'hidden_dim': {
            'type': 'categorical',
            'choices': [128, 256, 512]
        },
        'dropout_rate': {
            'type': 'real',
            'low': 0.0,
            'high': 0.3
        }
    }

    # Initialize enhanced training system
    enhanced_trainer = EnhancedTrainingSystem(
        config=training_config,
        save_dir="enhanced_models",
        enable_bayesian_opt=True
    )

    def model_factory(hyperparams: Dict[str, Any]):
        """Create model with given hyperparameters"""
        architecture_config = {
            'hidden_dims': [hyperparams.get('hidden_dim', 256)] * 2,
            'dropout_rate': hyperparams.get('dropout_rate', 0.1)
        }

        return model_manager.create_model(
            algorithm=training_config['algorithm'],
            architecture_config=architecture_config
        )

    def training_function(model, hyperparams: Dict[str, Any]) -> Dict[str, Any]:
        """Training function for a single model"""
        # Initialize training components
        lr_scheduler = AdaptiveLearningRateScheduler(
            initial_lr=hyperparams.get('learning_rate', 0.001)
        )
        early_stopping = EarlyStoppingManager(patience=50)
        progress_monitor = TrainingProgressMonitor()

        progress_monitor.start_monitoring()

        # Training loop
        best_performance = float('-inf')
        episode = 0
        max_episodes = training_config.get('episodes', 1000)

        while episode < max_episodes:
            # Simulate training episode (replace with actual training logic)
            episode_reward = np.random.normal(0, 1)  # Placeholder
            episode_loss = max(0.1, np.random.exponential(0.5))  # Placeholder

            # Update learning rate
            current_lr = lr_scheduler.step(episode_reward)

            # Update enhanced training system
            converged = enhanced_trainer.update_training_metrics(
                episode=episode,
                reward=episode_reward,
                loss=episode_loss,
                learning_rate=current_lr
            )

            # Update progress monitor
            progress_monitor.update(episode, {
                'reward': episode_reward,
                'loss': episode_loss,
                'learning_rate': current_lr
            })

            # Check early stopping
            if early_stopping.check(episode_reward):
                logger.info(f"Early stopping at episode {episode}")
                break

            # Check convergence
            if converged:
                logger.info(f"Training converged at episode {episode}")
                break

            # Save checkpoint periodically
            if episode % 100 == 0 and episode > 0:
                enhanced_trainer.save_checkpoint(model, episode, episode_reward)

            episode += 1

        # Get final performance
        final_performance = enhanced_trainer.training_metrics[-1].total_reward if enhanced_trainer.training_metrics else 0.0

        return {
            'final_performance': final_performance,
            'convergence_episode': episode,
            'training_summary': enhanced_trainer.get_training_summary(),
            'progress_summary': progress_monitor.get_summary()
        }

    # Run enhanced training with optimization
    result = enhanced_trainer.train_with_optimization(
        model_factory=model_factory,
        training_function=training_function,
        search_space=search_space
    )

    logger.info("Enhanced training completed")
    logger.info(f"Best performance: {result.get('final_performance', 0):.4f}")

    return result


def main():
    """Main training execution function"""
    parser = argparse.ArgumentParser(description="Train RL Trading Model")
    parser.add_argument("--symbol", type=str, default="000001.SZ", 
                       help="Trading symbol")
    parser.add_argument("--episodes", type=int, default=500,
                       help="Number of training episodes")
    parser.add_argument("--learning-rate", type=float, default=0.001,
                       help="Learning rate")
    parser.add_argument("--train-start", type=str, default="2020-01-01",
                       help="Training data start date")
    parser.add_argument("--train-end", type=str, default="2020-02-29",
                       help="Training data end date")
    parser.add_argument("--val-start", type=str, default="2020-03-01",
                       help="Validation data start date")
    parser.add_argument("--val-end", type=str, default="2020-03-24",
                       help="Validation data end date")
    parser.add_argument("--output-dir", type=str, default="output/rl_training",
                       help="Output directory for results")
    parser.add_argument("--config-file", type=str, default=None,
                       help="Configuration file path")
    parser.add_argument("--log-level", type=str, default="INFO",
                       help="Logging level")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger("RLTrainingMain")
    
    print("🤖 RL Model Training System")
    print("=" * 50)
    
    try:
        # 1. Initialize configuration
        print("\n📋 Step 1: Loading Configuration")
        config = TradingConfig(args.config_file)
        logger.info("Configuration loaded successfully")
        print(f"✅ Configuration loaded")
        
        # 2. Initialize data manager
        print("\n📊 Step 2: Preparing Data Infrastructure")
        data_manager = DataManager(data_dir="data")
        
        # Ensure sample data exists
        if not (Path("data") / f"{args.symbol}.csv").exists():
            logger.info(f"Creating sample data for {args.symbol}")
            data_manager.save_sample_data(args.symbol, args.train_start, args.val_end)
        
        print(f"✅ Data infrastructure ready")
        
        # 3. Prepare training data
        print("\n🔄 Step 3: Loading Training Data")
        train_data, val_data = prepare_training_data(
            data_manager, args.symbol,
            args.train_start, args.train_end,
            args.val_start, args.val_end
        )
        print(f"✅ Training data loaded: {len(train_data)} records")
        print(f"✅ Validation data loaded: {len(val_data)} records")
        
        # 4. Create environments
        print("\n🏗️ Step 4: Setting up Training Environments")
        train_env, val_env = create_training_environments(train_data, val_data, config)
        print("✅ Training and validation environments created")
        
        # 5. Initialize model manager
        print("\n🔧 Step 5: Initializing Model Manager")
        model_manager = RLModelManager(
            config=config,
            models_dir="models",
            enable_versioning=True
        )
        print("✅ Model manager initialized")
        
        # 6. Configure training parameters
        print("\n⚙️ Step 6: Configuring Training Parameters")
        training_config = {
            'algorithm': 'DQN',
            'architecture': {
                'state_dim': 50,
                'action_dim': 3,
                'hidden_dims': [256, 256, 128]
            },
            'episodes': args.episodes,
            'learning_rate': args.learning_rate,
            'epsilon_start': 1.0,
            'epsilon_end': 0.01,
            'epsilon_decay': 0.995,
            'batch_size': 32,
            'save_frequency': 50
        }
        print(f"✅ Training configured for {args.episodes} episodes")
        
        # 7. Run training
        print("\n🚀 Step 7: Running Model Training")
        print("   This may take several minutes...")
        trained_model = run_training_session(model_manager, train_env, val_env, training_config)
        print("✅ Model training completed")
        
        # 8. Evaluate model
        print("\n📈 Step 8: Evaluating Model Performance")
        evaluation_results = evaluate_model(trained_model, val_env, num_episodes=5)
        print("✅ Model evaluation completed")
        
        # 9. Generate report
        print("\n📋 Step 9: Generating Training Report")
        report_file = save_evaluation_report(evaluation_results, training_config, args.output_dir)
        print(f"✅ Report saved to: {report_file}")
        
        # 10. Display results
        print("\n🎯 Training Results Summary")
        print("=" * 40)
        print(f"📊 Model Performance:")
        if 'avg_total_return' in evaluation_results:
            print(f"   Average Return:     {evaluation_results['avg_total_return']:>8.2%}")
        if 'avg_sharpe_ratio' in evaluation_results:
            print(f"   Average Sharpe:     {evaluation_results['avg_sharpe_ratio']:>8.2f}")
        if 'avg_max_drawdown' in evaluation_results:
            print(f"   Average Drawdown:   {evaluation_results['avg_max_drawdown']:>8.2%}")
        if 'avg_total_trades' in evaluation_results:
            print(f"   Average Trades:     {evaluation_results['avg_total_trades']:>8.0f}")
        
        print(f"\n🔧 Training Configuration:")
        print(f"   Episodes:           {training_config['episodes']:>8d}")
        print(f"   Learning Rate:      {training_config['learning_rate']:>8.4f}")
        print(f"   Architecture:       {training_config['architecture']['hidden_dims']}")
        
        print(f"\n📂 Model Management:")
        available_models = model_manager.list_models()
        print(f"   Available Models:   {len(available_models):>8d}")
        for model_id, versions in available_models.items():
            print(f"   {model_id}: {len(versions)} versions")
        
        print(f"\n💡 Next Steps:")
        print(f"   • Review detailed report: {report_file}")
        print(f"   • Test model with different market conditions")
        print(f"   • Consider hyperparameter optimization")
        print(f"   • Deploy best performing model to production")
        
        print(f"\n🎉 RL Training Completed Successfully!")
        logger.info("RL training session completed successfully")
        
        # 7.1 超参数搜索示例
        print("\n🚀 Step 7.1: Hyperparameter Search (Demo)")
        param_grid = {
            'learning_rate': [0.001, 0.0005],
            'epsilon_decay': [0.995, 0.99],
            'batch_size': [16, 32],
        }
        base_config = training_config.copy()
        search_results = run_hyperparameter_search(param_grid, base_config, model_manager, train_env, val_env, max_trials=3)
        print("✅ Hyperparameter search completed. Top results:")
        print(search_results.sort_values('total_return', ascending=False).head())

        # 7.2 Enhanced Training System Demo
        if ENHANCED_TRAINING_AVAILABLE:
            print("\n🚀 Step 7.2: Enhanced Training System with Bayesian Optimization")
            try:
                enhanced_result = train_with_enhanced_system(
                    training_config=training_config,
                    model_manager=model_manager,
                    train_env=train_env,
                    val_env=val_env
                )

                print("✅ Enhanced training completed!")
                print(f"   Best Performance: {enhanced_result.get('final_performance', 0):.4f}")

                if 'optimization_history' in enhanced_result:
                    opt_history = enhanced_result['optimization_history']
                    print(f"   Optimization Trials: {len(opt_history)}")
                    print(f"   Best Hyperparameters: {enhanced_result.get('best_hyperparameters', {})}")

            except Exception as e:
                logger.warning(f"Enhanced training failed: {e}")
                print(f"⚠️ Enhanced training failed: {e}")
        else:
            print("\n⚠️ Enhanced training system not available")

    except Exception as e:
        logger.error(f"Training failed: {e}")
        print(f"\n❌ Training failed: {e}")
        raise


if __name__ == "__main__":
    main() 