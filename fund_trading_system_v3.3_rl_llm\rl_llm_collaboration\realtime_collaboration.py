"""
实时协作流程管理器 - 协调RL与LLM的实时决策流程
管理不同时间频率的协作：战略回顾、战术调整、执行决策
"""

import logging
import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from .strategy_tactical_layers import StrategyLayer, TacticalLayer
from .hybrid_decision_system import HybridDecisionSystem


class CollaborationFrequency(Enum):
    """协作频率枚举"""
    STRATEGIC_REVIEW = "1D"     # 每日战略回顾
    TACTICAL_ADJUSTMENT = "1H"   # 每小时战术调整
    EXECUTION_DECISION = "1M"    # 每分钟执行决策
    EMERGENCY_RESPONSE = "IMMEDIATE"  # 立即响应


class MarketCondition(Enum):
    """市场状况枚举"""
    NORMAL = "normal"
    VOLATILE = "volatile"
    TRENDING = "trending"
    CRISIS = "crisis"
    OPPORTUNITY = "opportunity"


class RealTimeCollaboration:
    """
    @class RealTimeCollaboration
    @brief 实时协作系统
    @details 管理RL与LLM在不同时间频率上的协作决策流程
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 初始化核心组件
        self.strategy_layer = StrategyLayer(self.config.get('strategy_config', {}))
        self.tactical_layer = TacticalLayer(self.config.get('tactical_config', {}))
        self.hybrid_system = HybridDecisionSystem(self.config.get('hybrid_config', {}))
        
        # 协作频率配置
        self.collaboration_frequencies = {
            'strategic_review': self.config.get('strategic_review_frequency', '1D'),
            'tactical_adjustment': self.config.get('tactical_adjustment_frequency', '1H'),
            'execution_decision': self.config.get('execution_decision_frequency', '1M')
        }
        
        # 协作状态
        self.is_running = False
        self.collaboration_threads = {}
        self.last_execution_times = {}
        self.collaboration_history = []
        
        # 市场监控
        self.market_monitor = MarketMonitor()
        self.emergency_triggers = self._setup_emergency_triggers()
        
        # 性能监控
        self.performance_metrics = {
            'strategic_reviews': 0,
            'tactical_adjustments': 0,
            'execution_decisions': 0,
            'emergency_responses': 0,
            'total_collaboration_time': 0.0,
            'average_response_time': 0.0
        }
        
        self.logger.info("实时协作系统初始化完成")
    
    def start_real_time_collaboration(self, market_data_stream: Callable = None) -> None:
        """
        @brief 启动实时协作系统
        @param market_data_stream: 市场数据流函数
        """
        try:
            if self.is_running:
                self.logger.warning("实时协作系统已在运行")
                return
            
            self.logger.info("启动实时协作决策系统...")
            self.is_running = True
            
            # 启动各个频率的协作线程
            self._start_strategic_review_thread()
            self._start_tactical_adjustment_thread()
            self._start_execution_decision_thread()
            
            # 启动市场监控线程
            self._start_market_monitoring_thread(market_data_stream)
            
            # 启动主协作循环
            self._start_main_collaboration_loop()
            
            self.logger.info("实时协作系统启动成功")
            
        except Exception as e:
            self.logger.error(f"启动实时协作系统失败: {str(e)}")
            self.stop_real_time_collaboration()
    
    def stop_real_time_collaboration(self) -> None:
        """停止实时协作系统"""
        try:
            self.logger.info("停止实时协作系统...")
            self.is_running = False
            
            # 等待所有线程结束
            for thread_name, thread in self.collaboration_threads.items():
                if thread.is_alive():
                    self.logger.info(f"等待线程 {thread_name} 结束...")
                    thread.join(timeout=5)
            
            self.collaboration_threads.clear()
            self.logger.info("实时协作系统已停止")
            
        except Exception as e:
            self.logger.error(f"停止实时协作系统失败: {str(e)}")
    
    def _start_strategic_review_thread(self) -> None:
        """启动战略回顾线程"""
        def strategic_review_loop():
            while self.is_running:
                try:
                    # 检查是否到了战略回顾时间
                    if self._should_execute_strategic_review():
                        self._execute_strategic_review()
                    
                    time.sleep(60)  # 每分钟检查一次
                    
                except Exception as e:
                    self.logger.error(f"战略回顾线程异常: {str(e)}")
                    time.sleep(60)
        
        thread = threading.Thread(target=strategic_review_loop, name="StrategyReview")
        thread.daemon = True
        self.collaboration_threads["strategic_review"] = thread
        thread.start()
        self.logger.info("战略回顾线程已启动")
    
    def _start_tactical_adjustment_thread(self) -> None:
        """启动战术调整线程"""
        def tactical_adjustment_loop():
            while self.is_running:
                try:
                    # 检查是否到了战术调整时间
                    if self._should_execute_tactical_adjustment():
                        self._execute_tactical_adjustment()
                    
                    time.sleep(30)  # 每30秒检查一次
                    
                except Exception as e:
                    self.logger.error(f"战术调整线程异常: {str(e)}")
                    time.sleep(30)
        
        thread = threading.Thread(target=tactical_adjustment_loop, name="TacticalAdjustment")
        thread.daemon = True
        self.collaboration_threads["tactical_adjustment"] = thread
        thread.start()
        self.logger.info("战术调整线程已启动")
    
    def _start_execution_decision_thread(self) -> None:
        """启动执行决策线程"""
        def execution_decision_loop():
            while self.is_running:
                try:
                    # 检查是否到了执行决策时间
                    if self._should_execute_decision():
                        self._execute_real_time_decision()
                    
                    time.sleep(10)  # 每10秒检查一次
                    
                except Exception as e:
                    self.logger.error(f"执行决策线程异常: {str(e)}")
                    time.sleep(10)
        
        thread = threading.Thread(target=execution_decision_loop, name="ExecutionDecision")
        thread.daemon = True
        self.collaboration_threads["execution_decision"] = thread
        thread.start()
        self.logger.info("执行决策线程已启动")
    
    def _start_market_monitoring_thread(self, market_data_stream: Callable = None) -> None:
        """启动市场监控线程"""
        def market_monitoring_loop():
            while self.is_running:
                try:
                    # 获取市场数据
                    market_data = self._get_current_market_data(market_data_stream)
                    
                    # 评估市场状况
                    market_condition = self._assess_market_condition(market_data)
                    
                    # 检查紧急触发条件
                    if self._check_emergency_triggers(market_data, market_condition):
                        self._trigger_emergency_response(market_data, market_condition)
                    
                    time.sleep(5)  # 每5秒监控一次
                    
                except Exception as e:
                    self.logger.error(f"市场监控线程异常: {str(e)}")
                    time.sleep(10)
        
        thread = threading.Thread(target=market_monitoring_loop, name="MarketMonitoring")
        thread.daemon = True
        self.collaboration_threads["market_monitoring"] = thread
        thread.start()
        self.logger.info("市场监控线程已启动")
    
    def _start_main_collaboration_loop(self) -> None:
        """启动主协作循环"""
        def main_collaboration_loop():
            while self.is_running:
                try:
                    # 协作状态检查
                    self._check_collaboration_health()
                    
                    # 性能指标更新
                    self._update_performance_metrics()
                    
                    # 协作历史清理
                    self._cleanup_collaboration_history()
                    
                    time.sleep(30)  # 每30秒主循环检查
                    
                except Exception as e:
                    self.logger.error(f"主协作循环异常: {str(e)}")
                    time.sleep(30)
        
        thread = threading.Thread(target=main_collaboration_loop, name="MainCollaboration")
        thread.daemon = True
        self.collaboration_threads["main_collaboration"] = thread
        thread.start()
        self.logger.info("主协作循环已启动")
    
    def _should_execute_strategic_review(self) -> bool:
        """检查是否应该执行战略回顾"""
        last_execution = self.last_execution_times.get('strategic_review')
        if last_execution is None:
            return True
        
        # 检查是否到了下一个战略回顾时间（每日）
        time_since_last = datetime.now() - last_execution
        return time_since_last >= timedelta(days=1)
    
    def _should_execute_tactical_adjustment(self) -> bool:
        """检查是否应该执行战术调整"""
        last_execution = self.last_execution_times.get('tactical_adjustment')
        if last_execution is None:
            return True
        
        # 检查是否到了下一个战术调整时间（每小时）
        time_since_last = datetime.now() - last_execution
        return time_since_last >= timedelta(hours=1)
    
    def _should_execute_decision(self) -> bool:
        """检查是否应该执行决策"""
        last_execution = self.last_execution_times.get('execution_decision')
        if last_execution is None:
            return True
        
        # 检查是否到了下一个执行决策时间（每分钟）
        time_since_last = datetime.now() - last_execution
        return time_since_last >= timedelta(minutes=1)
    
    def _execute_strategic_review(self) -> None:
        """执行战略回顾"""
        try:
            start_time = time.time()
            self.logger.info("开始执行战略回顾...")
            
            # 获取宏观和市场数据
            macro_data = self._get_macro_data()
            market_data = self._get_current_market_data()
            
            # 执行战略分析
            strategy_result = self.strategy_layer.generate_investment_strategy(
                macro_data, market_data
            )
            
            # 更新执行时间
            self.last_execution_times['strategic_review'] = datetime.now()
            
            # 记录协作历史
            self._record_collaboration_event('strategic_review', strategy_result)
            
            # 更新性能指标
            execution_time = time.time() - start_time
            self.performance_metrics['strategic_reviews'] += 1
            self.performance_metrics['total_collaboration_time'] += execution_time
            
            self.logger.info(f"战略回顾完成，耗时: {execution_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"执行战略回顾失败: {str(e)}")
    
    def _execute_tactical_adjustment(self) -> None:
        """执行战术调整"""
        try:
            start_time = time.time()
            self.logger.info("开始执行战术调整...")
            
            # 获取当前战略指导
            current_strategy = self.strategy_layer.current_strategy
            if current_strategy is None:
                self.logger.warning("没有当前战略，跳过战术调整")
                return
            
            # 获取实时市场数据
            real_time_data = self._get_current_market_data()
            
            # 执行战术调整
            tactical_result = self.tactical_layer.execute_strategy(
                current_strategy, real_time_data
            )
            
            # 更新执行时间
            self.last_execution_times['tactical_adjustment'] = datetime.now()
            
            # 记录协作历史
            self._record_collaboration_event('tactical_adjustment', tactical_result)
            
            # 更新性能指标
            execution_time = time.time() - start_time
            self.performance_metrics['tactical_adjustments'] += 1
            self.performance_metrics['total_collaboration_time'] += execution_time
            
            self.logger.info(f"战术调整完成，耗时: {execution_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"执行战术调整失败: {str(e)}")
    
    def _execute_real_time_decision(self) -> None:
        """执行实时决策"""
        try:
            start_time = time.time()
            
            # 获取实时市场数据
            market_data = self._get_current_market_data()
            
            # 使用混合决策系统
            decision_result = self.hybrid_system.make_decision(market_data)
            
            # 更新执行时间
            self.last_execution_times['execution_decision'] = datetime.now()
            
            # 记录协作历史
            self._record_collaboration_event('execution_decision', decision_result)
            
            # 更新性能指标
            execution_time = time.time() - start_time
            self.performance_metrics['execution_decisions'] += 1
            self.performance_metrics['total_collaboration_time'] += execution_time
            
            # 更新平均响应时间
            total_decisions = self.performance_metrics['execution_decisions']
            current_avg = self.performance_metrics['average_response_time']
            new_avg = (current_avg * (total_decisions - 1) + execution_time) / total_decisions
            self.performance_metrics['average_response_time'] = new_avg
            
            self.logger.debug(f"实时决策完成，耗时: {execution_time:.3f}秒")
            
        except Exception as e:
            self.logger.error(f"执行实时决策失败: {str(e)}")
    
    def _assess_market_condition(self, market_data: Dict[str, Any]) -> MarketCondition:
        """评估市场状况"""
        try:
            # 从市场数据中提取关键指标
            evaluations = market_data.get('evaluations', {})
            
            # 波动性评估
            volatility_eval = evaluations.get('波动性')
            volatility_score = volatility_eval.score if volatility_eval and hasattr(volatility_eval, 'score') else 0.5
            
            # 趋势评估
            trend_eval = evaluations.get('趋势')
            trend_score = trend_eval.score if trend_eval and hasattr(trend_eval, 'score') else 0.5
            
            # 情绪评估
            sentiment_eval = evaluations.get('情绪')
            sentiment_score = sentiment_eval.score if sentiment_eval and hasattr(sentiment_eval, 'score') else 0.5
            
            # 市场状况判断逻辑
            if volatility_score > 0.8:
                return MarketCondition.VOLATILE
            elif trend_score > 0.7:
                return MarketCondition.TRENDING
            elif sentiment_score > 0.8 or sentiment_score < 0.2:
                if sentiment_score > 0.8:
                    return MarketCondition.OPPORTUNITY
                else:
                    return MarketCondition.CRISIS
            else:
                return MarketCondition.NORMAL
                
        except Exception as e:
            self.logger.error(f"评估市场状况失败: {str(e)}")
            return MarketCondition.NORMAL
    
    def _check_emergency_triggers(self, market_data: Dict[str, Any], 
                                market_condition: MarketCondition) -> bool:
        """检查紧急触发条件"""
        try:
            # 检查市场状况是否触发紧急响应
            if market_condition in [MarketCondition.CRISIS, MarketCondition.VOLATILE]:
                return True
            
            # 检查价格异常波动
            price_data = market_data.get('price_data', {})
            change_pct = abs(price_data.get('change_pct', 0))
            if change_pct > 5.0:  # 5%以上的价格变动
                return True
            
            # 检查技术指标异常
            technical_data = market_data.get('technical_analysis', {})
            indicators = technical_data.get('indicators', {})
            rsi = indicators.get('rsi', 50)
            if rsi > 90 or rsi < 10:  # RSI极端值
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查紧急触发条件失败: {str(e)}")
            return False
    
    def _trigger_emergency_response(self, market_data: Dict[str, Any], 
                                  market_condition: MarketCondition) -> None:
        """触发紧急响应"""
        try:
            self.logger.warning(f"触发紧急响应 - 市场状况: {market_condition.value}")
            
            # 立即执行混合决策
            emergency_decision = self.hybrid_system.make_decision(market_data)
            
            # 记录紧急响应
            emergency_record = {
                'timestamp': datetime.now().isoformat(),
                'trigger_condition': market_condition.value,
                'market_data_snapshot': market_data,
                'emergency_decision': emergency_decision,
                'response_type': 'emergency'
            }
            
            self._record_collaboration_event('emergency_response', emergency_record)
            
            # 更新性能指标
            self.performance_metrics['emergency_responses'] += 1
            
            self.logger.info("紧急响应完成")
            
        except Exception as e:
            self.logger.error(f"紧急响应失败: {str(e)}")
    
    def _get_current_market_data(self, market_data_stream: Callable = None) -> Dict[str, Any]:
        """获取当前市场数据"""
        try:
            if market_data_stream:
                return market_data_stream()
            else:
                # 模拟市场数据
                return self._generate_mock_market_data()
                
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {str(e)}")
            return self._generate_mock_market_data()
    
    def _get_macro_data(self) -> Dict[str, Any]:
        """获取宏观数据"""
        # 简化实现，返回模拟宏观数据
        return {
            'interest_rate': 3.5,
            'inflation_rate': 2.1,
            'gdp_growth': 6.2,
            'policy_sentiment': 'neutral',
            'global_risk': 0.4
        }
    
    def _generate_mock_market_data(self) -> Dict[str, Any]:
        """生成模拟市场数据"""
        import random
        
        return {
            'price_data': {
                'current_price': 1.0 + random.uniform(-0.1, 0.1),
                'change_pct': random.uniform(-2.0, 2.0),
                'volume': random.randint(800000, 1200000)
            },
            'technical_analysis': {
                'indicators': {
                    'rsi': random.uniform(30, 70),
                    'macd': random.uniform(-0.05, 0.05)
                }
            },
            'evaluations': {},  # 简化版本
            'timestamp': datetime.now().isoformat()
        }
    
    def _record_collaboration_event(self, event_type: str, event_data: Dict[str, Any]) -> None:
        """记录协作事件"""
        collaboration_record = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'event_data': event_data
        }
        
        self.collaboration_history.append(collaboration_record)
        
        # 保持历史记录限制
        if len(self.collaboration_history) > 100:
            self.collaboration_history = self.collaboration_history[-100:]
    
    def _check_collaboration_health(self) -> None:
        """检查协作系统健康状态"""
        try:
            # 检查线程状态
            for thread_name, thread in self.collaboration_threads.items():
                if not thread.is_alive():
                    self.logger.warning(f"协作线程 {thread_name} 已停止，尝试重启...")
                    # 这里可以添加重启逻辑
            
            # 检查执行频率是否正常
            current_time = datetime.now()
            for event_type, last_time in self.last_execution_times.items():
                if last_time:
                    time_diff = current_time - last_time
                    if event_type == 'strategic_review' and time_diff > timedelta(days=2):
                        self.logger.warning(f"战略回顾超过2天未执行")
                    elif event_type == 'tactical_adjustment' and time_diff > timedelta(hours=2):
                        self.logger.warning(f"战术调整超过2小时未执行")
                    elif event_type == 'execution_decision' and time_diff > timedelta(minutes=5):
                        self.logger.warning(f"执行决策超过5分钟未执行")
            
        except Exception as e:
            self.logger.error(f"检查协作健康状态失败: {str(e)}")
    
    def _update_performance_metrics(self) -> None:
        """更新性能指标"""
        try:
            # 计算平均执行时间等指标
            total_events = (self.performance_metrics['strategic_reviews'] + 
                          self.performance_metrics['tactical_adjustments'] + 
                          self.performance_metrics['execution_decisions'])
            
            if total_events > 0:
                avg_time = self.performance_metrics['total_collaboration_time'] / total_events
                self.performance_metrics['average_collaboration_time'] = avg_time
            
        except Exception as e:
            self.logger.error(f"更新性能指标失败: {str(e)}")
    
    def _cleanup_collaboration_history(self) -> None:
        """清理协作历史"""
        try:
            # 删除超过24小时的历史记录
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.collaboration_history = [
                record for record in self.collaboration_history
                if datetime.fromisoformat(record['timestamp']) > cutoff_time
            ]
            
        except Exception as e:
            self.logger.error(f"清理协作历史失败: {str(e)}")
    
    def _setup_emergency_triggers(self) -> Dict[str, Any]:
        """设置紧急触发条件"""
        return {
            'max_price_change': 5.0,  # 最大价格变动百分比
            'min_rsi': 10,           # RSI最小值
            'max_rsi': 90,           # RSI最大值
            'max_volatility': 0.8,   # 最大波动性分数
            'market_conditions': [MarketCondition.CRISIS, MarketCondition.VOLATILE]
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'strategic_review_frequency': '1D',
            'tactical_adjustment_frequency': '1H',
            'execution_decision_frequency': '1M',
            'enable_emergency_response': True,
            'max_collaboration_history': 100,
            'health_check_interval': 30
        }
    
    def get_collaboration_status(self) -> Dict[str, Any]:
        """获取协作状态"""
        return {
            'is_running': self.is_running,
            'active_threads': [name for name, thread in self.collaboration_threads.items() if thread.is_alive()],
            'last_execution_times': {k: v.isoformat() if v else None for k, v in self.last_execution_times.items()},
            'performance_metrics': self.performance_metrics.copy(),
            'collaboration_history_length': len(self.collaboration_history)
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self.performance_metrics.copy()
    
    def get_collaboration_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取协作历史"""
        return self.collaboration_history[-limit:]


class MarketMonitor:
    """市场监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.monitoring_history = []
    
    def monitor_market_changes(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """监控市场变化"""
        try:
            # 简化的市场变化监控
            monitoring_result = {
                'timestamp': datetime.now().isoformat(),
                'market_status': 'normal',
                'key_changes': [],
                'alert_level': 'low'
            }
            
            # 检查价格变动
            price_data = market_data.get('price_data', {})
            change_pct = abs(price_data.get('change_pct', 0))
            
            if change_pct > 3.0:
                monitoring_result['alert_level'] = 'high'
                monitoring_result['key_changes'].append(f"价格异常变动: {change_pct:.2f}%")
            elif change_pct > 1.5:
                monitoring_result['alert_level'] = 'medium'
                monitoring_result['key_changes'].append(f"价格显著变动: {change_pct:.2f}%")
            
            return monitoring_result
            
        except Exception as e:
            self.logger.error(f"监控市场变化失败: {str(e)}")
            return {
                'timestamp': datetime.now().isoformat(),
                'market_status': 'unknown',
                'error': str(e)
            } 