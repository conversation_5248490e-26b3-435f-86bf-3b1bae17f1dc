"""
LLM增强的RL智能体 - 实现LLM与RL的深度融合
将LLM的语义理解能力与RL的精确执行能力相结合
"""

import logging
import numpy as np
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from analyzers.llm_market_analyzer import LLMMarketAnalyzer
from .rl_decision_optimizer import RLDecisionOptimizer


class LLMEnhancedRLAgent:
    """
    @class LLMEnhancedRLAgent
    @brief LLM增强的RL智能体
    @details 将LLM的市场理解能力与RL的量化决策能力深度融合
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or self._get_default_config()
        
        # 初始化核心组件
        self.llm_analyzer = LLMMarketAnalyzer()
        self.rl_optimizer = RLDecisionOptimizer(self.config.get('rl_config', {}))
        
        # 融合参数
        self.llm_influence_weight = self.config.get('llm_influence_weight', 0.3)
        self.state_enhancement_dim = self.config.get('state_enhancement_dim', 20)
        self.dynamic_weight_adjustment = self.config.get('dynamic_weight_adjustment', True)
        
        # 状态增强配置
        self.sentiment_encoding_method = self.config.get('sentiment_encoding_method', 'multi_dimensional')
        self.contextual_features = self.config.get('contextual_features', True)
        
        # 性能跟踪
        self.enhancement_history = []
        self.performance_comparison = {
            'standalone_rl': {'decisions': 0, 'success_rate': 0.0},
            'enhanced_rl': {'decisions': 0, 'success_rate': 0.0}
        }
        
        self.logger.info("LLM增强RL智能体初始化完成")
    
    def make_enhanced_decision(self, raw_market_data: Dict[str, Any], 
                             fund_code: str = None) -> Dict[str, Any]:
        """
        @brief 执行LLM增强的RL决策
        @param raw_market_data: 原始市场数据
        @param fund_code: 基金代码
        @return: 增强决策结果
        """
        try:
            self.logger.info(f"开始LLM增强RL决策 - 基金: {fund_code}")
            
            # 第一阶段：LLM市场理解和语义分析
            llm_insights = self._extract_llm_insights(raw_market_data, fund_code)
            
            # 第二阶段：增强状态表示
            enhanced_state = self._create_enhanced_state_representation(raw_market_data, llm_insights)
            
            # 第三阶段：动态奖励函数调整
            adjusted_rewards = self._adjust_reward_function(llm_insights, raw_market_data)
            
            # 第四阶段：RL决策执行
            rl_decision = self._execute_enhanced_rl_decision(enhanced_state, adjusted_rewards)
            
            # 第五阶段：决策后处理和解释
            final_decision = self._post_process_enhanced_decision(
                rl_decision, llm_insights, raw_market_data
            )
            
            # 记录增强历史
            self._record_enhancement_impact(llm_insights, rl_decision, final_decision)
            
            return final_decision
            
        except Exception as e:
            self.logger.error(f"LLM增强RL决策失败: {str(e)}")
            return self._get_fallback_enhanced_decision(raw_market_data, fund_code, error=str(e))
    
    def _extract_llm_insights(self, market_data: Dict[str, Any], fund_code: str) -> Dict[str, Any]:
        """提取LLM市场洞察"""
        try:
            # 执行LLM分析
            llm_analysis = self.llm_analyzer.analyze_market_narrative(market_data, fund_code)
            
            # 增强LLM分析结果
            enhanced_insights = self._enhance_llm_insights(llm_analysis, market_data)
            
            # 提取关键语义特征
            semantic_features = self._extract_semantic_features(enhanced_insights)
            
            # 生成市场情绪向量
            sentiment_vector = self._generate_sentiment_vector(enhanced_insights)
            
            insights = {
                'raw_llm_analysis': llm_analysis,
                'enhanced_insights': enhanced_insights,
                'semantic_features': semantic_features,
                'sentiment_vector': sentiment_vector,
                'extraction_timestamp': datetime.now().isoformat()
            }
            
            self.logger.info("LLM洞察提取完成")
            return insights
            
        except Exception as e:
            self.logger.error(f"提取LLM洞察失败: {str(e)}")
            return self._get_default_llm_insights(error=str(e))
    
    def _enhance_llm_insights(self, llm_analysis: Dict[str, Any], market_data: Dict[str, Any]) -> Dict[str, Any]:
        """增强LLM分析结果"""
        enhanced = llm_analysis.copy()
        
        # 量化市场情绪强度
        sentiment = llm_analysis.get('market_sentiment', '中性')
        sentiment_intensity = self._quantify_sentiment_intensity(llm_analysis)
        enhanced['sentiment_intensity'] = sentiment_intensity
        
        # 提取风险偏好
        risk_preference = self._extract_risk_preference(llm_analysis)
        enhanced['risk_preference'] = risk_preference
        
        # 分析时间偏好（短期vs长期）
        time_horizon = self._analyze_time_horizon(llm_analysis)
        enhanced['time_horizon'] = time_horizon
        
        # 提取关键驱动因素重要性
        driver_importance = self._extract_driver_importance(llm_analysis)
        enhanced['driver_importance'] = driver_importance
        
        # 分析确定性水平
        certainty_level = self._analyze_certainty_level(llm_analysis)
        enhanced['certainty_level'] = certainty_level
        
        return enhanced
    
    def _extract_semantic_features(self, enhanced_insights: Dict[str, Any]) -> Dict[str, float]:
        """提取语义特征"""
        features = {}
        
        try:
            # 情绪相关特征
            features['sentiment_polarity'] = enhanced_insights.get('market_sentiment_score', 0.5)
            features['sentiment_intensity'] = enhanced_insights.get('sentiment_intensity', 0.5)
            features['sentiment_stability'] = self._assess_sentiment_stability(enhanced_insights)
            
            # 置信度特征
            features['llm_confidence'] = enhanced_insights.get('confidence_level', 0.5)
            features['certainty_level'] = enhanced_insights.get('certainty_level', 0.5)
            
            # 风险特征
            features['risk_appetite'] = enhanced_insights.get('risk_preference', 0.5)
            features['risk_perception'] = enhanced_insights.get('risk_level', 0.5)
            
            # 时间特征
            features['time_urgency'] = enhanced_insights.get('time_horizon', 0.5)
            
            # 驱动因素特征
            driver_importance = enhanced_insights.get('driver_importance', {})
            features['fundamental_weight'] = driver_importance.get('fundamental', 0.5)
            features['technical_weight'] = driver_importance.get('technical', 0.5)
            features['sentiment_weight'] = driver_importance.get('sentiment', 0.5)
            
            return features
            
        except Exception as e:
            self.logger.error(f"提取语义特征失败: {str(e)}")
            return {f'feature_{i}': 0.5 for i in range(10)}  # 默认特征
    
    def _generate_sentiment_vector(self, enhanced_insights: Dict[str, Any]) -> np.ndarray:
        """生成情绪向量"""
        try:
            if self.sentiment_encoding_method == 'multi_dimensional':
                return self._create_multi_dimensional_sentiment_vector(enhanced_insights)
            else:
                return self._create_simple_sentiment_vector(enhanced_insights)
                
        except Exception as e:
            self.logger.error(f"生成情绪向量失败: {str(e)}")
            return np.full(self.state_enhancement_dim, 0.5)
    
    def _create_multi_dimensional_sentiment_vector(self, enhanced_insights: Dict[str, Any]) -> np.ndarray:
        """创建多维情绪向量"""
        vector_components = []
        
        # 基础情绪维度
        vector_components.append(enhanced_insights.get('market_sentiment_score', 0.5))
        vector_components.append(enhanced_insights.get('sentiment_intensity', 0.5))
        vector_components.append(enhanced_insights.get('certainty_level', 0.5))
        
        # 风险维度
        vector_components.append(enhanced_insights.get('risk_level', 0.5))
        vector_components.append(enhanced_insights.get('risk_preference', 0.5))
        
        # 机会维度
        vector_components.append(enhanced_insights.get('opportunity_score', 0.5))
        vector_components.append(enhanced_insights.get('trend_confidence', 0.5))
        
        # 时间维度
        vector_components.append(enhanced_insights.get('time_horizon', 0.5))
        
        # 驱动因素权重
        driver_importance = enhanced_insights.get('driver_importance', {})
        vector_components.append(driver_importance.get('fundamental', 0.5))
        vector_components.append(driver_importance.get('technical', 0.5))
        vector_components.append(driver_importance.get('sentiment', 0.5))
        vector_components.append(driver_importance.get('macro', 0.5))
        
        # 填充到目标维度
        while len(vector_components) < self.state_enhancement_dim:
            vector_components.append(0.5)
        
        return np.array(vector_components[:self.state_enhancement_dim])
    
    def _create_enhanced_state_representation(self, raw_market_data: Dict[str, Any], 
                                            llm_insights: Dict[str, Any]) -> np.ndarray:
        """创建增强状态表示"""
        try:
            # 获取基础技术状态（30维）
            technical_state = self._extract_technical_features(raw_market_data)
            
            # 获取LLM增强特征（20维）
            llm_enhancement = llm_insights.get('sentiment_vector', np.full(20, 0.5))
            
            # 融合状态（50维）
            enhanced_state = np.concatenate([technical_state, llm_enhancement])
            
            # 确保维度正确
            if len(enhanced_state) > 50:
                enhanced_state = enhanced_state[:50]
            elif len(enhanced_state) < 50:
                padding = np.full(50 - len(enhanced_state), 0.5)
                enhanced_state = np.concatenate([enhanced_state, padding])
            
            return enhanced_state.astype(np.float32)
            
        except Exception as e:
            self.logger.error(f"创建增强状态表示失败: {str(e)}")
            return np.full(50, 0.5, dtype=np.float32)
    
    def _extract_technical_features(self, market_data: Dict[str, Any]) -> np.ndarray:
        """提取技术特征（30维）"""
        features = []
        
        try:
            # 价格特征 (5维)
            price_data = market_data.get('price_data', {})
            features.extend([
                price_data.get('current_price', 1.0),
                price_data.get('change_pct', 0.0) / 100,
                price_data.get('volume', 1000000) / 1000000,  # 标准化
                0.5, 0.5  # 占位符
            ])
            
            # 技术指标 (15维)
            technical_indicators = market_data.get('technical_analysis', {}).get('indicators', {})
            tech_features = [
                technical_indicators.get('rsi', 50) / 100,
                technical_indicators.get('macd', 0.0),
                0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5  # 占位符
            ]
            features.extend(tech_features[:15])
            
            # 六大维度评估 (6维)
            evaluations = market_data.get('evaluations', {})
            dimension_features = []
            for dim_name in ['趋势', '波动性', '流动性', '情绪', '结构', '转换']:
                eval_result = evaluations.get(dim_name)
                if eval_result and hasattr(eval_result, 'score'):
                    dimension_features.append(eval_result.score)
                else:
                    dimension_features.append(0.5)
            features.extend(dimension_features[:6])
            
            # 宏观特征 (4维)
            macro_features = [0.5, 0.5, 0.5, 0.5]  # 占位符
            features.extend(macro_features)
            
            # 确保30维
            return np.array(features[:30], dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"提取技术特征失败: {str(e)}")
            return np.full(30, 0.5, dtype=np.float32)
    
    def _adjust_reward_function(self, llm_insights: Dict[str, Any], 
                              market_data: Dict[str, Any]) -> Dict[str, Any]:
        """根据LLM洞察调整奖励函数"""
        try:
            enhanced_insights = llm_insights.get('enhanced_insights', {})
            
            # 基础奖励参数
            base_alpha = 1.0  # 收益权重
            base_beta = 0.5   # 风险权重
            base_gamma = 0.3  # 回撤权重
            
            # 根据LLM风险偏好调整
            risk_preference = enhanced_insights.get('risk_preference', 0.5)
            if risk_preference < 0.4:  # 保守
                risk_multiplier = 1.5
                return_multiplier = 0.8
            elif risk_preference > 0.6:  # 激进
                risk_multiplier = 0.7
                return_multiplier = 1.2
            else:  # 中性
                risk_multiplier = 1.0
                return_multiplier = 1.0
            
            # 根据市场情绪调整
            sentiment_score = enhanced_insights.get('market_sentiment_score', 0.5)
            sentiment_adjustment = (sentiment_score - 0.5) * 0.2  # [-0.1, 0.1]
            
            # 根据置信度调整
            confidence = enhanced_insights.get('confidence_level', 0.5)
            confidence_multiplier = 0.5 + confidence  # [0.5, 1.5]
            
            adjusted_rewards = {
                'alpha': base_alpha * return_multiplier * confidence_multiplier,
                'beta': base_beta * risk_multiplier,
                'gamma': base_gamma * risk_multiplier,
                'delta': 0.1,  # LLM一致性奖励
                'adjustments': {
                    'risk_preference': risk_preference,
                    'sentiment_adjustment': sentiment_adjustment,
                    'confidence_multiplier': confidence_multiplier
                }
            }
            
            return adjusted_rewards
            
        except Exception as e:
            self.logger.error(f"调整奖励函数失败: {str(e)}")
            return {'alpha': 1.0, 'beta': 0.5, 'gamma': 0.3, 'delta': 0.1, 'error': str(e)}
    
    def _execute_enhanced_rl_decision(self, enhanced_state: np.ndarray, 
                                    adjusted_rewards: Dict[str, Any]) -> Dict[str, Any]:
        """执行增强的RL决策"""
        try:
            # 使用增强状态执行RL决策
            rl_decision = self.rl_optimizer.optimize_decision(
                enhanced_state, 
                market_context={'reward_adjustments': adjusted_rewards}
            )
            
            # 添加增强相关信息
            rl_decision['enhancement_applied'] = True
            rl_decision['reward_adjustments'] = adjusted_rewards
            rl_decision['enhanced_state_dim'] = len(enhanced_state)
            
            return rl_decision
            
        except Exception as e:
            self.logger.error(f"执行增强RL决策失败: {str(e)}")
            return {
                'action': 'hold',
                'position_change': 0.0,
                'confidence': 0.3,
                'enhancement_applied': False,
                'error': str(e)
            }
    
    def _post_process_enhanced_decision(self, rl_decision: Dict[str, Any], 
                                      llm_insights: Dict[str, Any], 
                                      market_data: Dict[str, Any]) -> Dict[str, Any]:
        """后处理增强决策"""
        try:
            enhanced_decision = rl_decision.copy()
            
            # 添加LLM解释
            llm_explanation = self._generate_llm_explanation(llm_insights, rl_decision)
            enhanced_decision['llm_explanation'] = llm_explanation
            
            # 计算LLM-RL一致性
            consistency_score = self._calculate_llm_rl_consistency(llm_insights, rl_decision)
            enhanced_decision['llm_rl_consistency'] = consistency_score
            
            # 综合置信度调整
            original_confidence = rl_decision.get('confidence', 0.5)
            llm_confidence = llm_insights.get('enhanced_insights', {}).get('confidence_level', 0.5)
            
            # 加权平均置信度
            weight_rl = 0.7
            weight_llm = 0.3
            combined_confidence = weight_rl * original_confidence + weight_llm * llm_confidence
            
            # 根据一致性调整最终置信度
            if consistency_score > 0.7:
                final_confidence = min(1.0, combined_confidence * 1.1)  # 一致时增强
            elif consistency_score < 0.3:
                final_confidence = combined_confidence * 0.8  # 不一致时降低
            else:
                final_confidence = combined_confidence
            
            enhanced_decision['final_confidence'] = final_confidence
            enhanced_decision['confidence'] = final_confidence  # 更新主置信度
            
            # 添加增强元信息
            enhanced_decision['enhancement_metadata'] = {
                'llm_influence_applied': True,
                'state_enhancement_dim': self.state_enhancement_dim,
                'processing_timestamp': datetime.now().isoformat(),
                'enhancement_version': '1.0'
            }
            
            return enhanced_decision
            
        except Exception as e:
            self.logger.error(f"后处理增强决策失败: {str(e)}")
            return {**rl_decision, 'post_processing_error': str(e)}
    
    def _generate_llm_explanation(self, llm_insights: Dict[str, Any], 
                                rl_decision: Dict[str, Any]) -> Dict[str, Any]:
        """生成LLM解释"""
        try:
            enhanced_insights = llm_insights.get('enhanced_insights', {})
            
            explanation = {
                'market_narrative': enhanced_insights.get('strategy_suggestion', '基于市场分析的建议'),
                'sentiment_influence': f"市场情绪评分: {enhanced_insights.get('market_sentiment_score', 0.5):.2f}",
                'risk_assessment': f"风险偏好: {enhanced_insights.get('risk_preference', 0.5):.2f}",
                'time_horizon': f"投资时间偏好: {enhanced_insights.get('time_horizon', 0.5):.2f}",
                'confidence_explanation': f"LLM分析置信度: {enhanced_insights.get('confidence_level', 0.5):.2f}",
                'key_drivers': enhanced_insights.get('market_drivers', ['市场综合分析']),
                'llm_rl_alignment': self._assess_llm_rl_alignment(llm_insights, rl_decision)
            }
            
            return explanation
            
        except Exception as e:
            return {'error': str(e), 'explanation': '解释生成失败'}
    
    def _calculate_llm_rl_consistency(self, llm_insights: Dict[str, Any], 
                                    rl_decision: Dict[str, Any]) -> float:
        """计算LLM-RL一致性"""
        try:
            enhanced_insights = llm_insights.get('enhanced_insights', {})
            
            # LLM倾向
            sentiment_score = enhanced_insights.get('market_sentiment_score', 0.5)
            if sentiment_score > 0.6:
                llm_tendency = 'buy'
            elif sentiment_score < 0.4:
                llm_tendency = 'sell'
            else:
                llm_tendency = 'hold'
            
            # RL决策
            rl_action = rl_decision.get('action', 'hold')
            
            # 计算一致性
            if llm_tendency == rl_action:
                base_consistency = 1.0
            elif (llm_tendency == 'hold' and rl_action != 'hold') or (llm_tendency != 'hold' and rl_action == 'hold'):
                base_consistency = 0.5
            else:
                base_consistency = 0.0
            
            # 考虑置信度差异
            llm_confidence = enhanced_insights.get('confidence_level', 0.5)
            rl_confidence = rl_decision.get('confidence', 0.5)
            confidence_diff = abs(llm_confidence - rl_confidence)
            confidence_adjustment = 1.0 - confidence_diff * 0.5
            
            return base_consistency * confidence_adjustment
            
        except Exception as e:
            self.logger.error(f"计算一致性失败: {str(e)}")
            return 0.5
    
    def _assess_llm_rl_alignment(self, llm_insights: Dict[str, Any], 
                               rl_decision: Dict[str, Any]) -> str:
        """评估LLM-RL对齐情况"""
        consistency = self._calculate_llm_rl_consistency(llm_insights, rl_decision)
        
        if consistency > 0.8:
            return "高度对齐 - LLM分析与RL决策高度一致"
        elif consistency > 0.6:
            return "较好对齐 - LLM分析与RL决策基本一致"
        elif consistency > 0.4:
            return "部分对齐 - LLM分析与RL决策存在一定分歧"
        else:
            return "对齐较差 - LLM分析与RL决策存在明显分歧"
    
    # 辅助方法
    def _quantify_sentiment_intensity(self, llm_analysis: Dict[str, Any]) -> float:
        """量化情绪强度"""
        try:
            # 从LLM分析文本中提取强度词汇
            text_content = str(llm_analysis.get('strategy_suggestion', ''))
            
            intensity_keywords = {
                'high': ['非常', '极其', '强烈', '显著', '明显'],
                'medium': ['较为', '相对', '一定程度'],
                'low': ['略有', '轻微', '少许']
            }
            
            intensity_score = 0.5  # 默认中等强度
            for level, keywords in intensity_keywords.items():
                if any(keyword in text_content for keyword in keywords):
                    if level == 'high':
                        intensity_score = 0.8
                    elif level == 'medium':
                        intensity_score = 0.6
                    elif level == 'low':
                        intensity_score = 0.4
                    break
            
            return intensity_score
            
        except Exception as e:
            return 0.5
    
    def _extract_risk_preference(self, llm_analysis: Dict[str, Any]) -> float:
        """提取风险偏好"""
        try:
            risk_level = llm_analysis.get('risk_level', 0.5)
            strategy_text = str(llm_analysis.get('strategy_suggestion', ''))
            
            # 从策略建议中推断风险偏好
            conservative_keywords = ['保守', '谨慎', '稳健', '降低', '减少']
            aggressive_keywords = ['积极', '激进', '增加', '提高', '扩大']
            
            conservative_count = sum(1 for keyword in conservative_keywords if keyword in strategy_text)
            aggressive_count = sum(1 for keyword in aggressive_keywords if keyword in strategy_text)
            
            if aggressive_count > conservative_count:
                return min(0.8, 0.5 + (aggressive_count - conservative_count) * 0.1)
            elif conservative_count > aggressive_count:
                return max(0.2, 0.5 - (conservative_count - aggressive_count) * 0.1)
            else:
                return risk_level
                
        except Exception as e:
            return 0.5
    
    def _analyze_time_horizon(self, llm_analysis: Dict[str, Any]) -> float:
        """分析时间偏好"""
        try:
            strategy_text = str(llm_analysis.get('strategy_suggestion', ''))
            
            short_term_keywords = ['短期', '当前', '即时', '立即']
            long_term_keywords = ['长期', '持续', '趋势', '战略']
            
            short_count = sum(1 for keyword in short_term_keywords if keyword in strategy_text)
            long_count = sum(1 for keyword in long_term_keywords if keyword in strategy_text)
            
            if long_count > short_count:
                return 0.7  # 偏向长期
            elif short_count > long_count:
                return 0.3  # 偏向短期
            else:
                return 0.5  # 中性
                
        except Exception as e:
            return 0.5
    
    def _extract_driver_importance(self, llm_analysis: Dict[str, Any]) -> Dict[str, float]:
        """提取驱动因素重要性"""
        try:
            analysis_text = str(llm_analysis)
            
            # 关键词映射
            factor_keywords = {
                'fundamental': ['基本面', '财务', '盈利', '估值', '业绩'],
                'technical': ['技术', '图表', '指标', '支撑', '阻力'],
                'sentiment': ['情绪', '心理', '恐慌', '乐观', '市场氛围'],
                'macro': ['宏观', '政策', '经济', '利率', '通胀']
            }
            
            importance = {}
            for factor, keywords in factor_keywords.items():
                count = sum(1 for keyword in keywords if keyword in analysis_text)
                importance[factor] = min(1.0, count * 0.2 + 0.2)  # 基础权重0.2
            
            # 归一化
            total = sum(importance.values())
            if total > 0:
                importance = {k: v / total for k, v in importance.items()}
            
            return importance
            
        except Exception as e:
            return {'fundamental': 0.25, 'technical': 0.25, 'sentiment': 0.25, 'macro': 0.25}
    
    def _analyze_certainty_level(self, llm_analysis: Dict[str, Any]) -> float:
        """分析确定性水平"""
        try:
            confidence = llm_analysis.get('confidence_level', 0.5)
            analysis_text = str(llm_analysis)
            
            uncertainty_keywords = ['不确定', '可能', '或许', '大概', '似乎']
            certainty_keywords = ['确定', '肯定', '明确', '显然', '必然']
            
            uncertainty_count = sum(1 for keyword in uncertainty_keywords if keyword in analysis_text)
            certainty_count = sum(1 for keyword in certainty_keywords if keyword in analysis_text)
            
            # 基于关键词调整确定性
            text_adjustment = (certainty_count - uncertainty_count) * 0.1
            return max(0.0, min(1.0, confidence + text_adjustment))
            
        except Exception as e:
            return 0.5
    
    def _assess_sentiment_stability(self, enhanced_insights: Dict[str, Any]) -> float:
        """评估情绪稳定性"""
        try:
            sentiment_score = enhanced_insights.get('market_sentiment_score', 0.5)
            certainty = enhanced_insights.get('certainty_level', 0.5)
            
            # 情绪稳定性与确定性和极端程度相关
            extremeness = abs(sentiment_score - 0.5) * 2  # 极端程度[0,1]
            stability = certainty * (1.0 - extremeness * 0.3)  # 越极端越不稳定
            
            return max(0.0, min(1.0, stability))
            
        except Exception as e:
            return 0.5
    
    def _record_enhancement_impact(self, llm_insights: Dict[str, Any], 
                                 rl_decision: Dict[str, Any], 
                                 final_decision: Dict[str, Any]) -> None:
        """记录增强影响"""
        enhancement_record = {
            'timestamp': datetime.now().isoformat(),
            'llm_insights_summary': {
                'sentiment_score': llm_insights.get('enhanced_insights', {}).get('market_sentiment_score', 0.5),
                'confidence': llm_insights.get('enhanced_insights', {}).get('confidence_level', 0.5)
            },
            'rl_decision_summary': {
                'action': rl_decision.get('action', 'unknown'),
                'confidence': rl_decision.get('confidence', 0.5)
            },
            'final_decision_summary': {
                'action': final_decision.get('action', 'unknown'),
                'confidence': final_decision.get('confidence', 0.5),
                'consistency': final_decision.get('llm_rl_consistency', 0.5)
            }
        }
        
        self.enhancement_history.append(enhancement_record)
        
        # 保持历史记录限制
        if len(self.enhancement_history) > 50:
            self.enhancement_history = self.enhancement_history[-50:]
    
    def _get_default_llm_insights(self, error: str = None) -> Dict[str, Any]:
        """获取默认LLM洞察"""
        return {
            'raw_llm_analysis': {
                'market_sentiment': '中性',
                'confidence_level': 0.5,
                'error': error
            },
            'enhanced_insights': {
                'market_sentiment_score': 0.5,
                'confidence_level': 0.5,
                'risk_preference': 0.5
            },
            'semantic_features': {f'feature_{i}': 0.5 for i in range(10)},
            'sentiment_vector': np.full(self.state_enhancement_dim, 0.5),
            'error': error
        }
    
    def _get_fallback_enhanced_decision(self, market_data: Dict[str, Any], 
                                      fund_code: str, error: str = None) -> Dict[str, Any]:
        """获取兜底增强决策"""
        return {
            'action': 'hold',
            'position_change': 0.0,
            'confidence': 0.2,
            'enhancement_applied': False,
            'error': error,
            'fund_code': fund_code,
            'decision_type': 'fallback_enhanced',
            'llm_explanation': {
                'error': error,
                'explanation': '增强决策系统异常，采用保守策略'
            }
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'llm_influence_weight': 0.3,
            'state_enhancement_dim': 20,
            'dynamic_weight_adjustment': True,
            'sentiment_encoding_method': 'multi_dimensional',
            'contextual_features': True,
            'rl_config': {
                'state_dim': 50,
                'action_dim': 2,
                'algorithm': 'ppo'
            }
        }
    
    def get_enhancement_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取增强历史"""
        return self.enhancement_history[-limit:]
    
    def get_performance_comparison(self) -> Dict[str, Any]:
        """获取性能对比"""
        return self.performance_comparison.copy() 