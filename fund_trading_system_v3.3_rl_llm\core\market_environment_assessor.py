"""
市场环境评估器
评估当前市场环境并提供风险调整建议
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import asdict

from .data_structures import MarketEnvironmentAssessment, DimensionEvaluationResult
from .enums import MarketRegime


class MarketEnvironmentAssessor:
    """
    @class MarketEnvironmentAssessor
    @brief 市场环境评估器
    @details 分析市场状态并提供环境评估结果
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.assessment_cache = {}
        self.cache_duration = timedelta(minutes=15)  # 缓存15分钟
    
    def assess_market_environment(self, dimension_evaluations: Dict[str, DimensionEvaluationResult],
                                 fund_code: str = None) -> MarketEnvironmentAssessment:
        """
        @brief 评估市场环境
        @param dimension_evaluations: 六大维度评估结果
        @param fund_code: 基金代码（可选）
        @return: 市场环境评估结果
        """
        try:
            # 检查缓存
            cache_key = self._generate_cache_key(dimension_evaluations)
            if self._is_cache_valid(cache_key):
                return self.assessment_cache[cache_key]['assessment']
            
            # 执行评估
            assessment = self._perform_assessment(dimension_evaluations, fund_code)
            
            # 更新缓存
            self.assessment_cache[cache_key] = {
                'assessment': assessment,
                'timestamp': datetime.now()
            }
            
            return assessment
            
        except Exception as e:
            self.logger.error(f"市场环境评估失败: {str(e)}")
            return self._get_default_assessment()
    
    def _perform_assessment(self, dimension_evaluations: Dict[str, DimensionEvaluationResult],
                           fund_code: str = None) -> MarketEnvironmentAssessment:
        """执行市场环境评估"""
        
        # 提取各维度评分
        trend_score = self._get_dimension_score(dimension_evaluations, '趋势')
        volatility_score = self._get_dimension_score(dimension_evaluations, '波动性')
        liquidity_score = self._get_dimension_score(dimension_evaluations, '流动性')
        sentiment_score = self._get_dimension_score(dimension_evaluations, '情绪')
        structural_score = self._get_dimension_score(dimension_evaluations, '结构')
        transition_score = self._get_dimension_score(dimension_evaluations, '转换')
        
        # 判断市场状态
        market_regime = self._determine_market_regime(
            trend_score, volatility_score, sentiment_score
        )
        
        # 评估波动性水平
        volatility_level = self._assess_volatility_level(volatility_score)
        
        # 评估流动性条件
        liquidity_condition = self._assess_liquidity_condition(liquidity_score)
        
        # 计算趋势强度
        trend_strength = self._calculate_trend_strength(
            trend_score, structural_score, transition_score
        )
        
        # 评估整体风险水平
        overall_risk_level = self._assess_overall_risk(
            volatility_score, liquidity_score, sentiment_score
        )
        
        # 生成策略建议
        recommended_strategy = self._generate_strategy_recommendation(
            market_regime, volatility_level, liquidity_condition, trend_strength
        )
        
        return MarketEnvironmentAssessment(
            assessment_time=datetime.now(),
            market_regime=market_regime,
            volatility_level=volatility_level,
            liquidity_condition=liquidity_condition,
            sentiment_score=sentiment_score,
            trend_strength=trend_strength,
            overall_risk_level=overall_risk_level,
            recommended_strategy=recommended_strategy
        )
    
    def _get_dimension_score(self, evaluations: Dict[str, DimensionEvaluationResult], 
                           dimension: str) -> float:
        """获取维度评分"""
        if dimension in evaluations and evaluations[dimension]:
            return evaluations[dimension].score
        return 0.5  # 默认中性评分
    
    def _determine_market_regime(self, trend_score: float, volatility_score: float, 
                               sentiment_score: float) -> str:
        """判断市场状态"""
        # 综合评分
        composite_score = (trend_score * 0.5 + sentiment_score * 0.3 + 
                          (1 - volatility_score) * 0.2)
        
        if composite_score > 0.7:
            return 'bull'  # 牛市
        elif composite_score < 0.3:
            return 'bear'  # 熊市
        else:
            return 'sideways'  # 震荡市
    
    def _assess_volatility_level(self, volatility_score: float) -> str:
        """评估波动性水平"""
        if volatility_score < 0.3:
            return 'low'
        elif volatility_score < 0.7:
            return 'medium'
        else:
            return 'high'
    
    def _assess_liquidity_condition(self, liquidity_score: float) -> str:
        """评估流动性条件"""
        if liquidity_score > 0.7:
            return 'good'
        elif liquidity_score > 0.4:
            return 'fair'
        else:
            return 'poor'
    
    def _calculate_trend_strength(self, trend_score: float, structural_score: float, 
                                transition_score: float) -> float:
        """计算趋势强度"""
        # 趋势强度 = 趋势评分 * 0.6 + 结构评分 * 0.3 + (1-转换评分) * 0.1
        # 转换评分高表示可能变盘，所以用1减去
        return (trend_score * 0.6 + structural_score * 0.3 + 
                (1 - transition_score) * 0.1)
    
    def _assess_overall_risk(self, volatility_score: float, liquidity_score: float, 
                           sentiment_score: float) -> str:
        """评估整体风险水平"""
        # 风险评分：波动性高、流动性差、情绪极端都增加风险
        risk_score = (volatility_score * 0.4 + 
                     (1 - liquidity_score) * 0.3 + 
                     abs(sentiment_score - 0.5) * 2 * 0.3)
        
        if risk_score < 0.3:
            return 'low'
        elif risk_score < 0.6:
            return 'medium'
        elif risk_score < 0.8:
            return 'high'
        else:
            return 'critical'
    
    def _generate_strategy_recommendation(self, market_regime: str, volatility_level: str,
                                        liquidity_condition: str, trend_strength: float) -> str:
        """生成策略建议"""
        recommendations = []
        
        # 基于市场状态的建议
        if market_regime == 'bull':
            if volatility_level == 'low':
                recommendations.append("适合积极买入")
            else:
                recommendations.append("谨慎买入，控制仓位")
        elif market_regime == 'bear':
            recommendations.append("避免买入，考虑减仓")
        else:  # sideways
            if trend_strength > 0.6:
                recommendations.append("等待突破方向确认")
            else:
                recommendations.append("区间操作，快进快出")
        
        # 基于流动性的建议
        if liquidity_condition == 'poor':
            recommendations.append("注意流动性风险")
        
        # 基于波动性的建议
        if volatility_level == 'high':
            recommendations.append("降低仓位规模")
        
        return "; ".join(recommendations) if recommendations else "保持观望"
    
    def _generate_cache_key(self, dimension_evaluations: Dict[str, DimensionEvaluationResult]) -> str:
        """生成缓存键"""
        scores = []
        for dimension in ['趋势', '波动性', '流动性', '情绪', '结构', '转换']:
            score = self._get_dimension_score(dimension_evaluations, dimension)
            scores.append(f"{score:.2f}")
        return "_".join(scores)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.assessment_cache:
            return False
        
        cache_time = self.assessment_cache[cache_key]['timestamp']
        return datetime.now() - cache_time < self.cache_duration
    
    def _get_default_assessment(self) -> MarketEnvironmentAssessment:
        """获取默认评估结果"""
        return MarketEnvironmentAssessment(
            assessment_time=datetime.now(),
            market_regime='sideways',
            volatility_level='medium',
            liquidity_condition='fair',
            sentiment_score=0.5,
            trend_strength=0.5,
            overall_risk_level='medium',
            recommended_strategy='保持观望'
        )
    
    def get_risk_adjustment_factors(self, assessment: MarketEnvironmentAssessment) -> Dict[str, float]:
        """
        @brief 获取风险调整因子
        @param assessment: 市场环境评估结果
        @return: 风险调整因子字典
        """
        factors = {
            'volatility_adjustment': 1.0,
            'liquidity_adjustment': 1.0,
            'sentiment_adjustment': 1.0,
            'regime_adjustment': 1.0
        }
        
        # 波动性调整
        if assessment.volatility_level == 'high':
            factors['volatility_adjustment'] = 0.7  # 收紧30%
        elif assessment.volatility_level == 'low':
            factors['volatility_adjustment'] = 1.2  # 放宽20%
        
        # 流动性调整
        if assessment.liquidity_condition == 'poor':
            factors['liquidity_adjustment'] = 0.6  # 收紧40%
        elif assessment.liquidity_condition == 'good':
            factors['liquidity_adjustment'] = 1.1  # 放宽10%
        
        # 情绪调整
        if assessment.sentiment_score < 0.2 or assessment.sentiment_score > 0.8:
            factors['sentiment_adjustment'] = 0.8  # 极端情绪时收紧20%
        
        # 市场状态调整
        if assessment.market_regime == 'bear':
            factors['regime_adjustment'] = 0.5  # 熊市收紧50%
        elif assessment.market_regime == 'bull':
            factors['regime_adjustment'] = 1.3  # 牛市放宽30%
        
        return factors
    
    def should_halt_trading(self, assessment: MarketEnvironmentAssessment) -> bool:
        """
        @brief 判断是否应该暂停交易
        @param assessment: 市场环境评估结果
        @return: 是否暂停交易
        """
        # 极端情况下暂停交易
        halt_conditions = [
            assessment.overall_risk_level == 'critical',
            assessment.volatility_level == 'high' and assessment.liquidity_condition == 'poor',
            assessment.market_regime == 'bear' and assessment.sentiment_score < 0.1
        ]
        
        return any(halt_conditions)
    
    def get_position_size_multiplier(self, assessment: MarketEnvironmentAssessment) -> float:
        """
        @brief 获取仓位规模调整倍数
        @param assessment: 市场环境评估结果
        @return: 仓位调整倍数
        """
        base_multiplier = 1.0
        
        # 基于整体风险水平调整
        risk_multipliers = {
            'low': 1.2,
            'medium': 1.0,
            'high': 0.7,
            'critical': 0.3
        }
        
        multiplier = risk_multipliers.get(assessment.overall_risk_level, 1.0)
        
        # 基于趋势强度微调
        if assessment.trend_strength > 0.8:
            multiplier *= 1.1  # 强趋势时略微增加
        elif assessment.trend_strength < 0.3:
            multiplier *= 0.9  # 弱趋势时略微减少
        
        return max(0.1, min(2.0, multiplier))  # 限制在0.1-2.0之间
    
    def clear_cache(self):
        """清空缓存"""
        self.assessment_cache.clear()
        self.logger.info("市场环境评估缓存已清空")
