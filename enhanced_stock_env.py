import gym
import random
from gym import spaces
import numpy as np
import pandas as pd
import talib
from typing import Dict, Any, Optional

# 扩展的归一化常数
MAX_ACCOUNT_BALANCE = 2147480
MAX_NUM_SHARES = 2147480
MAX_SHARE_PRICE = 5000
MAX_VOLUME = 1e9
MAX_AMOUNT = 1e10
INITIAL_ACCOUNT_BALANCE = 100000

# 技术指标归一化常数
MAX_RSI = 100
MAX_CCI = 200
MAX_WILLIAMS = 100
MAX_ATR_RATIO = 0.1  # ATR相对价格的最大比例

class EnhancedStockTradingEnv(gym.Env):
    """增强版股票交易环境 - 50维状态空间"""
    
    def __init__(self, df, fund_trading_system=None, lookback_window=20):
        super(EnhancedStockTradingEnv, self).__init__()
        
        self.df = df
        self.fund_trading_system = fund_trading_system
        self.lookback_window = lookback_window
        
        # 动作空间保持不变：[action_type, amount]
        self.action_space = spaces.Box(
            low=np.array([-1, -1]), high=np.array([1, 1]), dtype=np.float32)
        
        # 扩展状态空间到50维
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(50,), dtype=np.float32)
        
        self.current_step = 0
        
        # 预计算技术指标
        self._precompute_technical_indicators()
        
        # 初始化账户状态
        self._reset_account_state()
    
    def _precompute_technical_indicators(self):
        """预计算所有技术指标"""
        try:
            # 基础价格数据
            high = self.df['high'].values
            low = self.df['low'].values
            close = self.df['close'].values
            volume = self.df['volume'].values
            
            # 移动平均线
            self.df['ma5'] = talib.SMA(close, timeperiod=5)
            self.df['ma10'] = talib.SMA(close, timeperiod=10)
            self.df['ma20'] = talib.SMA(close, timeperiod=20)
            self.df['ma60'] = talib.SMA(close, timeperiod=60)
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(close)
            self.df['macd'] = macd
            self.df['macd_signal'] = macd_signal
            self.df['macd_hist'] = macd_hist
            
            # RSI
            self.df['rsi'] = talib.RSI(close, timeperiod=14)
            
            # CCI
            self.df['cci'] = talib.CCI(high, low, close, timeperiod=14)
            
            # Williams %R
            self.df['williams_r'] = talib.WILLR(high, low, close, timeperiod=14)
            
            # 布林带
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close)
            self.df['bb_upper'] = bb_upper
            self.df['bb_middle'] = bb_middle
            self.df['bb_lower'] = bb_lower
            self.df['bb_width'] = (bb_upper - bb_lower) / bb_middle
            
            # ATR
            self.df['atr'] = talib.ATR(high, low, close, timeperiod=14)
            
            # 价格动量
            self.df['price_momentum'] = talib.MOM(close, timeperiod=10)
            
            # 填充NaN值
            self.df = self.df.fillna(method='bfill').fillna(0)
            
        except Exception as e:
            print(f"技术指标计算失败: {e}")
            # 如果计算失败，用0填充
            indicators = ['ma5', 'ma10', 'ma20', 'ma60', 'macd', 'macd_signal', 
                         'macd_hist', 'rsi', 'cci', 'williams_r', 'bb_upper', 
                         'bb_middle', 'bb_lower', 'bb_width', 'atr', 'price_momentum']
            for indicator in indicators:
                if indicator not in self.df.columns:
                    self.df[indicator] = 0
    
    def _get_czsc_features(self, current_step: int) -> np.ndarray:
        """获取CZSC缠论特征"""
        if self.fund_trading_system is None:
            return np.zeros(10)
        
        try:
            fund_code = '518880'  # 默认基金代码，实际应该从环境传入
            
            # 获取CZSC结构数据
            czsc_data = self.fund_trading_system.get_czsc_structure_data(fund_code)
            
            if not czsc_data:
                return np.zeros(10)
            
            # 提取分型特征
            fx_list = czsc_data.get('fx_list', [])
            latest_fx_type = 0.5  # 默认中性
            fx_strength = 0.5
            fx_distance = 0.5
            
            if fx_list:
                latest_fx = fx_list[-1]
                latest_fx_type = 1.0 if latest_fx.get('fx_mark') == 'g' else 0.0
                fx_strength = min(1.0, abs(latest_fx.get('fx_price', 0) - 
                                         self.df.loc[current_step, 'close']) / 
                                 self.df.loc[current_step, 'close'])
                fx_distance = min(1.0, (current_step - 
                                       self._find_fx_step(latest_fx.get('dt', ''))) / 100)
            
            # 提取笔特征
            bi_list = czsc_data.get('bi_list', [])
            bi_direction = 0.5
            bi_strength = 0.5
            bi_length = 0.5
            
            if bi_list:
                latest_bi = bi_list[-1]
                bi_direction = 1.0 if latest_bi.get('direction') == 'up' else 0.0
                bi_strength = min(1.0, abs(latest_bi.get('bi_price', 0) - 
                                          self.df.loc[current_step, 'close']) / 
                                 self.df.loc[current_step, 'close'])
                bi_length = min(1.0, len(bi_list) / 50)  # 假设最大50个笔
            
            # 提取线段特征
            xd_list = czsc_data.get('xd_list', [])
            xd_direction = 0.5
            xd_strength = 0.5
            
            if xd_list:
                latest_xd = xd_list[-1]
                xd_direction = 1.0 if latest_xd.get('direction') == 'up' else 0.0
                xd_strength = min(1.0, abs(latest_xd.get('xd_price', 0) - 
                                          self.df.loc[current_step, 'close']) / 
                                 self.df.loc[current_step, 'close'])
            
            # 结构评分
            structure_analysis = czsc_data.get('structure_analysis', {})
            structure_score = min(1.0, structure_analysis.get('structure_strength', 0.5))
            trend_consistency = min(1.0, structure_analysis.get('trend_consistency', 0.5))
            
            return np.array([
                latest_fx_type, fx_strength, fx_distance,
                bi_direction, bi_strength, bi_length,
                xd_direction, xd_strength,
                structure_score, trend_consistency
            ])
            
        except Exception as e:
            print(f"CZSC特征提取失败: {e}")
            return np.zeros(10)
    
    def _find_fx_step(self, dt_str: str) -> int:
        """根据时间字符串找到对应的步数"""
        try:
            # 简化实现，实际需要根据时间匹配
            return max(0, self.current_step - 10)
        except:
            return self.current_step
    
    def _get_six_dimension_features(self, current_step: int) -> np.ndarray:
        """获取六大维度评估特征"""
        if self.fund_trading_system is None:
            return np.zeros(6)
        
        try:
            fund_code = '518880'
            evaluations = self.fund_trading_system.get_six_dimension_evaluation(fund_code)
            
            if not evaluations:
                return np.zeros(6)
            
            scores = []
            dimension_names = ['trend', 'volatility', 'liquidity', 
                             'sentiment', 'structural', 'transition']
            
            for dim_name in dimension_names:
                if dim_name in evaluations:
                    score = evaluations[dim_name].score
                    scores.append(min(1.0, max(0.0, score)))
                else:
                    scores.append(0.5)  # 默认中性评分
            
            return np.array(scores)
            
        except Exception as e:
            print(f"六大维度特征提取失败: {e}")
            return np.zeros(6)
    
    def _get_market_environment_features(self, current_step: int) -> np.ndarray:
        """获取市场环境特征"""
        try:
            # 市场状态分类 (简化版)
            ma5 = self.df.loc[current_step, 'ma5']
            ma20 = self.df.loc[current_step, 'ma20']
            close = self.df.loc[current_step, 'close']
            
            # 市场趋势状态
            if ma5 > ma20 and close > ma5:
                market_regime = 1.0  # 牛市
            elif ma5 < ma20 and close < ma5:
                market_regime = 0.0  # 熊市
            else:
                market_regime = 0.5  # 震荡市
            
            # 波动率状态
            atr = self.df.loc[current_step, 'atr']
            volatility_regime = min(1.0, atr / (close * MAX_ATR_RATIO))
            
            # 成交量分布特征
            volume = self.df.loc[current_step, 'volume']
            avg_volume = self.df['volume'].rolling(20).mean().iloc[current_step]
            volume_profile = min(1.0, volume / (avg_volume + 1e-8))
            
            # 价格动量
            momentum = self.df.loc[current_step, 'price_momentum']
            price_momentum = min(1.0, max(0.0, (momentum + close) / (2 * close)))
            
            # 市场广度 (简化)
            rsi = self.df.loc[current_step, 'rsi']
            market_breadth = rsi / MAX_RSI
            
            # 相关性因子 (简化)
            bb_width = self.df.loc[current_step, 'bb_width']
            correlation_factor = min(1.0, bb_width)
            
            return np.array([
                market_regime, volatility_regime, volume_profile,
                price_momentum, market_breadth, correlation_factor
            ])
            
        except Exception as e:
            print(f"市场环境特征提取失败: {e}")
            return np.zeros(6)
    
    def _next_observation(self):
        """生成50维观测状态"""
        try:
            # 原始价格和成交量数据 (6维)
            basic_features = np.array([
                self.df.loc[self.current_step, 'open'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'high'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'low'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'close'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'volume'] / MAX_VOLUME,
                self.df.loc[self.current_step, 'amount'] / MAX_AMOUNT,
            ])
            
            # 账户状态 (7维)
            account_features = np.array([
                self.balance / MAX_ACCOUNT_BALANCE,
                self.max_net_worth / MAX_ACCOUNT_BALANCE,
                self.net_worth / MAX_ACCOUNT_BALANCE,
                self.shares_held / MAX_NUM_SHARES,
                self.cost_basis / MAX_SHARE_PRICE,
                self.total_shares_sold / MAX_NUM_SHARES,
                self.total_sales_value / (MAX_NUM_SHARES * MAX_SHARE_PRICE),
            ])
            
            # 技术指标特征 (15维)
            close = self.df.loc[self.current_step, 'close']
            technical_features = np.array([
                self.df.loc[self.current_step, 'ma5'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'ma10'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'ma20'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'ma60'] / MAX_SHARE_PRICE,
                
                (self.df.loc[self.current_step, 'macd'] + close) / (2 * close),
                (self.df.loc[self.current_step, 'macd_signal'] + close) / (2 * close),
                (self.df.loc[self.current_step, 'macd_hist'] + close) / (2 * close),
                
                self.df.loc[self.current_step, 'rsi'] / MAX_RSI,
                (self.df.loc[self.current_step, 'cci'] + MAX_CCI) / (2 * MAX_CCI),
                (self.df.loc[self.current_step, 'williams_r'] + MAX_WILLIAMS) / MAX_WILLIAMS,
                
                self.df.loc[self.current_step, 'bb_upper'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'bb_middle'] / MAX_SHARE_PRICE,
                self.df.loc[self.current_step, 'bb_lower'] / MAX_SHARE_PRICE,
                min(1.0, self.df.loc[self.current_step, 'bb_width']),
                
                min(1.0, self.df.loc[self.current_step, 'atr'] / (close * MAX_ATR_RATIO))
            ])
            
            # CZSC缠论特征 (10维)
            czsc_features = self._get_czsc_features(self.current_step)
            
            # 六大维度评估特征 (6维)
            dimension_features = self._get_six_dimension_features(self.current_step)
            
            # 市场环境特征 (6维)
            market_features = self._get_market_environment_features(self.current_step)
            
            # 组合所有特征 (6+7+15+10+6+6=50维)
            obs = np.concatenate([
                basic_features,      # 6维
                account_features,    # 7维  
                technical_features,  # 15维
                czsc_features,       # 10维
                dimension_features,  # 6维
                market_features      # 6维
            ])
            
            # 确保所有值都在[0,1]范围内
            obs = np.clip(obs, 0, 1)
            
            # 处理NaN值
            obs = np.nan_to_num(obs, nan=0.5)
            
            return obs.astype(np.float32)
            
        except Exception as e:
            print(f"观测状态生成失败: {e}")
            # 返回默认的50维零向量
            return np.zeros(50, dtype=np.float32)
    
    def _reset_account_state(self):
        """重置账户状态"""
        self.balance = INITIAL_ACCOUNT_BALANCE
        self.net_worth = INITIAL_ACCOUNT_BALANCE
        self.max_net_worth = INITIAL_ACCOUNT_BALANCE
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0
    
    def _take_action(self, action):
        """执行交易动作 - 保持原有逻辑"""
        current_price = random.uniform(
            self.df.loc[self.current_step, "low"], 
            self.df.loc[self.current_step, "high"])
        
        action_type = action[0]
        amount = action[1]
        
        if action_type < 1/3 and self.balance >= current_price:  # 买入
            total_possible = int(self.balance / current_price)
            shares_bought = int(total_possible * amount)
            if shares_bought != 0:
                prev_cost = self.cost_basis * self.shares_held
                additional_cost = shares_bought * current_price
                
                self.balance -= additional_cost
                self.cost_basis = (prev_cost + additional_cost) / (self.shares_held + shares_bought)
                self.shares_held += shares_bought
                
        elif action_type > 2/3 and self.shares_held != 0:  # 卖出
            shares_sold = int(self.shares_held * amount)
            self.balance += shares_sold * current_price
            self.shares_held -= shares_sold
            self.total_shares_sold += shares_sold
            self.total_sales_value += shares_sold * current_price
        
        # 计算净值
        self.net_worth = self.balance + self.shares_held * current_price
        
        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth
            
        if self.shares_held == 0:
            self.cost_basis = 0
    
    def step(self, action):
        """环境步进 - 保持原有逻辑"""
        self._take_action(action)
        done = False
        status = None
        reward = 0
        
        self.current_step += 1
        
        # 奖励计算逻辑保持不变
        if self.net_worth >= INITIAL_ACCOUNT_BALANCE * 3:
            reward += 3
            status = f'[ENV] success at step {self.current_step}! Get 3 times worth.'
            done = True
        elif self.current_step > len(self.df) - 1:
            status = f'[ENV] Loop training. Max worth was {self.max_net_worth}, final worth is {self.net_worth}.'
            reward += self.net_worth / INITIAL_ACCOUNT_BALANCE
            self.current_step = 0
            done = True
        elif self.net_worth <= 0:
            status = f'[ENV] Failure at step {self.current_step}. Loss all worth.'
            reward += -1
            done = True
        else:
            profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
            profit_percent = profit / INITIAL_ACCOUNT_BALANCE
            if profit_percent > 0:
                reward += profit_percent / 3
            else:
                reward += -0.1
        
        obs = self._next_observation()
        
        return obs, reward, done, {
            'profit': self.net_worth,
            'current_step': self.current_step,
            'status': status
        }
    
    def reset(self, new_df=None):
        """重置环境"""
        self._reset_account_state()
        
        if new_df is not None:
            self.df = new_df
            self._precompute_technical_indicators()
            
        self.current_step = 0
        return self._next_observation()
    
    def render(self, mode='human'):
        """渲染环境状态"""
        profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
        print('-' * 50)
        print(f'Step: {self.current_step}')
        print(f'Balance: {self.balance:.2f}')
        print(f'Shares held: {self.shares_held}')
        print(f'Net worth: {self.net_worth:.2f} (Max: {self.max_net_worth:.2f})')
        print(f'Profit: {profit:.2f} ({profit/INITIAL_ACCOUNT_BALANCE*100:.2f}%)')
        return profit