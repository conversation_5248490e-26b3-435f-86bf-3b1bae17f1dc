"""
风控智能体 - 交易决策的最后防线
实现技术指标验证、市场环境评估和组合风险控制
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.data_structures import DimensionEvaluationResult
from core.enums import MarketRegime


class RiskControlAgent(BaseAgent):
    """
    @class RiskControlAgent
    @brief 风控智能体 - 交易决策验证和风险控制
    @details 在买入条件触发时进行技术指标验证和风险评估
    """
    
    def __init__(self):
        super().__init__("RiskControlAgent", "risk_control")
        self.logger = logging.getLogger(self.__class__.__name__)
        self.risk_config = self._load_risk_config()

        # 性能优化：缓存机制
        self._validation_cache = {}
        self._cache_max_size = 100
        self._cache_ttl = 300  # 5分钟缓存

        # 错误处理：重试机制
        self._max_retries = 3
        self._retry_delay = 0.1
        
    def _load_risk_config(self) -> Dict[str, Any]:
        """加载风控配置"""
        # 临时配置，后续将移到配置文件
        return {
            'technical_indicators': {
                'bollinger_bands': {
                    'buy_position_requirement': 'below_lower_band',
                    'tolerance': 0.02
                },
                'rsi': {
                    'buy_max_threshold': 65,
                    'sell_min_threshold': 35
                },
                'volume': {
                    'min_volume_ratio': 1.2
                }
            },
            'market_environment': {
                'volatility_threshold': 0.8,
                'trend_confirmation': True
            },
            'portfolio_risk': {
                'max_single_position': 0.2
            }
        }
    
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 处理风控验证请求
        @param data: 包含基金代码和分析结果的数据
        @return: 风控验证结果
        """
        fund_code = data.get('fund_code', 'unknown')

        try:
            analysis_result = data.get('analysis_result', {})
            proposed_decision = data.get('proposed_decision', 'hold')

            # 性能优化：检查缓存
            cache_key = self._generate_cache_key(fund_code, proposed_decision, analysis_result)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                self.logger.debug(f"使用缓存结果 - 基金: {fund_code}")
                return cached_result

            self.logger.info(f"开始风控验证 - 基金: {fund_code}, 建议决策: {proposed_decision}")

            # 执行三层风控验证（带重试机制）
            risk_result = self._perform_risk_validation_with_retry(
                fund_code, proposed_decision, analysis_result
            )

            result = {
                'fund_code': fund_code,
                'original_decision': proposed_decision,
                'risk_validation': risk_result,
                'final_decision': risk_result.get('recommended_action', 'hold'),
                'risk_level': risk_result.get('risk_level', 'unknown'),
                'validation_time': datetime.now().isoformat(),
                'cached': False
            }

            # 缓存结果
            self._cache_result(cache_key, result)

            return result

        except Exception as e:
            self.logger.error(f"风控验证失败 {fund_code}: {str(e)}", exc_info=True)
            return self._get_error_response(fund_code, str(e))

    def _perform_risk_validation_with_retry(self, fund_code: str, proposed_decision: str,
                                          analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """带重试机制的风控验证"""
        last_exception = None

        for attempt in range(self._max_retries):
            try:
                return self._perform_risk_validation(fund_code, proposed_decision, analysis_result)
            except Exception as e:
                last_exception = e
                self.logger.warning(f"风控验证第{attempt + 1}次尝试失败: {str(e)}")

                if attempt < self._max_retries - 1:
                    import time
                    time.sleep(self._retry_delay * (attempt + 1))  # 递增延迟

        # 所有重试都失败
        self.logger.error(f"风控验证重试{self._max_retries}次后仍失败: {str(last_exception)}")
        raise last_exception

    def _generate_cache_key(self, fund_code: str, proposed_decision: str,
                           analysis_result: Dict[str, Any]) -> str:
        """生成缓存键"""
        try:
            # 提取关键指标用于缓存键
            tech_indicators = analysis_result.get('technical_analysis', {}).get('indicators', {})
            key_indicators = {
                'bb_position': tech_indicators.get('bb_position', 0),
                'rsi': tech_indicators.get('rsi', 50),
                'volume_ratio': tech_indicators.get('volume_ratio', 1.0)
            }

            # 生成简化的缓存键
            import hashlib
            key_str = f"{fund_code}_{proposed_decision}_{str(sorted(key_indicators.items()))}"
            return hashlib.md5(key_str.encode()).hexdigest()[:16]

        except Exception:
            # 缓存键生成失败时返回基础键
            return f"{fund_code}_{proposed_decision}_{datetime.now().strftime('%Y%m%d%H%M')}"

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        try:
            if cache_key in self._validation_cache:
                cached_data = self._validation_cache[cache_key]
                cache_time = cached_data.get('timestamp', 0)

                # 检查缓存是否过期
                if datetime.now().timestamp() - cache_time < self._cache_ttl:
                    result = cached_data['result'].copy()
                    result['cached'] = True
                    result['validation_time'] = datetime.now().isoformat()
                    return result
                else:
                    # 清理过期缓存
                    del self._validation_cache[cache_key]

            return None

        except Exception as e:
            self.logger.warning(f"缓存获取失败: {str(e)}")
            return None

    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """缓存结果"""
        try:
            # 清理缓存大小
            if len(self._validation_cache) >= self._cache_max_size:
                self._cleanup_cache()

            # 存储缓存
            self._validation_cache[cache_key] = {
                'result': result.copy(),
                'timestamp': datetime.now().timestamp()
            }

        except Exception as e:
            self.logger.warning(f"缓存存储失败: {str(e)}")

    def _cleanup_cache(self):
        """清理过期缓存"""
        try:
            current_time = datetime.now().timestamp()
            expired_keys = []

            for key, data in self._validation_cache.items():
                if current_time - data.get('timestamp', 0) > self._cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._validation_cache[key]

            # 如果清理后仍然过多，删除最旧的条目
            if len(self._validation_cache) >= self._cache_max_size:
                sorted_items = sorted(
                    self._validation_cache.items(),
                    key=lambda x: x[1].get('timestamp', 0)
                )

                # 删除最旧的一半
                for key, _ in sorted_items[:len(sorted_items) // 2]:
                    del self._validation_cache[key]

        except Exception as e:
            self.logger.warning(f"缓存清理失败: {str(e)}")
            self._validation_cache.clear()  # 清空所有缓存

    def _get_error_response(self, fund_code: str, error_msg: str) -> Dict[str, Any]:
        """获取错误响应"""
        return {
            'fund_code': fund_code,
            'error': error_msg,
            'final_decision': 'hold',  # 出错时保守决策
            'risk_level': 'critical',
            'validation_time': datetime.now().isoformat(),
            'cached': False
        }
    
    def _perform_risk_validation(self, fund_code: str, proposed_decision: str, 
                                analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """执行三层风控验证"""
        validation_result = {
            'passed': True,
            'risk_level': 'low',
            'rejection_reasons': [],
            'technical_violations': {},
            'market_environment_score': 0.0,
            'portfolio_risk_score': 0.0,
            'recommended_action': proposed_decision,
            'confidence': 1.0,
            'details': {}
        }
        
        # 只对买入决策进行严格验证
        if proposed_decision != 'buy':
            validation_result['details']['note'] = '非买入决策，跳过风控验证'
            return validation_result
        
        # 第一层：技术指标验证
        tech_validation = self._validate_technical_indicators(fund_code, analysis_result)
        validation_result['technical_violations'] = tech_validation
        
        # 第二层：市场环境验证
        market_validation = self._validate_market_environment(analysis_result)
        validation_result['market_environment_score'] = market_validation.get('score', 0.0)
        
        # 第三层：组合风险验证
        portfolio_validation = self._validate_portfolio_risk(fund_code)
        validation_result['portfolio_risk_score'] = portfolio_validation.get('score', 0.0)
        
        # 综合评估
        final_assessment = self._assess_overall_risk(tech_validation, market_validation, portfolio_validation)
        validation_result.update(final_assessment)
        
        return validation_result
    
    def _validate_technical_indicators(self, fund_code: str, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """验证技术指标条件"""
        violations = {}

        try:
            # 获取技术指标数据
            tech_data = analysis_result.get('technical_analysis', {})
            indicators = tech_data.get('indicators', {})

            if not indicators:
                self.logger.warning(f"基金 {fund_code} 缺少技术指标数据")
                return {'error': '缺少技术指标数据'}

            # 布林线验证（带异常处理）
            try:
                bb_violation = self._check_bollinger_bands(indicators)
                if bb_violation:
                    violations['bollinger_bands'] = bb_violation
            except Exception as e:
                self.logger.warning(f"布林线验证异常 {fund_code}: {str(e)}")
                violations['bollinger_bands'] = f"验证异常: {str(e)}"

            # RSI验证（带异常处理）
            try:
                rsi_violation = self._check_rsi_conditions(indicators)
                if rsi_violation:
                    violations['rsi'] = rsi_violation
            except Exception as e:
                self.logger.warning(f"RSI验证异常 {fund_code}: {str(e)}")
                violations['rsi'] = f"验证异常: {str(e)}"

            # 成交量验证（带异常处理）
            try:
                volume_violation = self._check_volume_conditions(indicators)
                if volume_violation:
                    violations['volume'] = volume_violation
            except Exception as e:
                self.logger.warning(f"成交量验证异常 {fund_code}: {str(e)}")
                violations['volume'] = f"验证异常: {str(e)}"

        except Exception as e:
            self.logger.error(f"技术指标验证失败 {fund_code}: {str(e)}")
            violations['error'] = str(e)

        return violations
    
    def _check_bollinger_bands(self, indicators: Dict[str, Any]) -> Optional[str]:
        """检查布林线条件"""
        try:
            bb_position = indicators.get('bb_position', 0.5)
            requirement = self.risk_config['technical_indicators']['bollinger_bands']['buy_position_requirement']
            tolerance = self.risk_config['technical_indicators']['bollinger_bands']['tolerance']
            
            if requirement == 'below_lower_band':
                # 要求价格低于布林线下轨（bb_position < 0 + tolerance）
                if bb_position > tolerance:
                    return f"价格位置 {bb_position:.3f} 高于布林线下轨要求 (>{tolerance})"
            
            return None
            
        except Exception as e:
            return f"布林线验证错误: {str(e)}"
    
    def _check_rsi_conditions(self, indicators: Dict[str, Any]) -> Optional[str]:
        """检查RSI条件"""
        try:
            rsi = indicators.get('rsi', 50)
            max_threshold = self.risk_config['technical_indicators']['rsi']['buy_max_threshold']
            
            if rsi > max_threshold:
                return f"RSI {rsi:.1f} 超过买入阈值 {max_threshold}"
            
            return None
            
        except Exception as e:
            return f"RSI验证错误: {str(e)}"
    
    def _check_volume_conditions(self, indicators: Dict[str, Any]) -> Optional[str]:
        """检查成交量条件"""
        try:
            volume_ratio = indicators.get('volume_ratio', 1.0)
            min_ratio = self.risk_config['technical_indicators']['volume']['min_volume_ratio']
            
            if volume_ratio < min_ratio:
                return f"成交量比率 {volume_ratio:.2f} 低于最小要求 {min_ratio}"
            
            return None
            
        except Exception as e:
            return f"成交量验证错误: {str(e)}"
    
    def _validate_market_environment(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """验证市场环境条件"""
        try:
            # 获取六大维度评估结果
            dimension_evaluations = analysis_result.get('dimension_evaluations', {})
            
            # 波动性检查
            volatility_eval = dimension_evaluations.get('波动性')
            volatility_score = volatility_eval.score if volatility_eval else 0.5
            
            # 流动性检查
            liquidity_eval = dimension_evaluations.get('流动性')
            liquidity_score = liquidity_eval.score if liquidity_eval else 0.5
            
            # 情绪检查
            sentiment_eval = dimension_evaluations.get('情绪')
            sentiment_score = sentiment_eval.score if sentiment_eval else 0.5
            
            # 计算综合市场环境评分
            environment_score = (volatility_score + liquidity_score + sentiment_score) / 3
            
            return {
                'score': environment_score,
                'volatility_score': volatility_score,
                'liquidity_score': liquidity_score,
                'sentiment_score': sentiment_score,
                'assessment': 'favorable' if environment_score > 0.6 else 'unfavorable'
            }
            
        except Exception as e:
            self.logger.warning(f"市场环境验证异常: {str(e)}")
            return {'score': 0.5, 'error': str(e)}
    
    def _validate_portfolio_risk(self, fund_code: str) -> Dict[str, Any]:
        """验证组合风险条件"""
        try:
            # 这里应该检查实际持仓，暂时返回模拟结果
            # TODO: 集成实际的持仓管理系统

            self.logger.debug(f"验证基金 {fund_code} 的组合风险")

            # 模拟组合风险检查
            portfolio_result = {
                'score': 0.8,  # 模拟低风险评分
                'position_check': 'passed',
                'concentration_risk': 'low',
                'correlation_risk': 'acceptable',
                'fund_code': fund_code
            }

            return portfolio_result

        except Exception as e:
            self.logger.warning(f"组合风险验证异常 {fund_code}: {str(e)}")
            return {
                'score': 0.5,
                'error': str(e),
                'fund_code': fund_code,
                'position_check': 'error'
            }

    def clear_cache(self):
        """清空验证缓存"""
        try:
            self._validation_cache.clear()
            self.logger.info("风控验证缓存已清空")
        except Exception as e:
            self.logger.warning(f"清空缓存失败: {str(e)}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            current_time = datetime.now().timestamp()
            valid_count = 0
            expired_count = 0

            for data in self._validation_cache.values():
                if current_time - data.get('timestamp', 0) < self._cache_ttl:
                    valid_count += 1
                else:
                    expired_count += 1

            return {
                'total_entries': len(self._validation_cache),
                'valid_entries': valid_count,
                'expired_entries': expired_count,
                'cache_hit_rate': getattr(self, '_cache_hits', 0) / max(getattr(self, '_cache_requests', 1), 1),
                'max_size': self._cache_max_size,
                'ttl_seconds': self._cache_ttl
            }

        except Exception as e:
            self.logger.warning(f"获取缓存统计失败: {str(e)}")
            return {'error': str(e)}
    
    def _assess_overall_risk(self, tech_validation: Dict[str, Any],
                           market_validation: Dict[str, Any],
                           portfolio_validation: Dict[str, Any]) -> Dict[str, Any]:
        """综合评估整体风险"""
        # 检查是否有技术指标违规
        has_violations = bool(tech_validation)

        # 获取各层评分
        market_score = market_validation.get('score', 0.5)
        portfolio_score = portfolio_validation.get('score', 0.5)

        # 综合评估
        if has_violations:
            return {
                'passed': False,
                'risk_level': 'high',
                'recommended_action': 'hold',
                'confidence': 0.2,
                'rejection_reasons': list(tech_validation.values())
            }
        elif market_score < 0.4 or portfolio_score < 0.4:
            return {
                'passed': False,
                'risk_level': 'medium',
                'recommended_action': 'hold',
                'confidence': 0.5,
                'rejection_reasons': ['市场环境或组合风险评分过低']
            }
        else:
            return {
                'passed': True,
                'risk_level': 'low',
                'recommended_action': 'buy',
                'confidence': min(market_score, portfolio_score)
            }

    def validate_buy_conditions(self, fund_code: str, current_price: float,
                               technical_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        @brief 专门验证买入条件的方法
        @param fund_code: 基金代码
        @param current_price: 当前价格
        @param technical_data: 技术指标数据
        @return: 买入条件验证结果
        """
        try:
            validation_result = {
                'fund_code': fund_code,
                'current_price': current_price,
                'validation_time': datetime.now().isoformat(),
                'conditions_met': True,
                'failed_conditions': [],
                'technical_analysis': {},
                'risk_assessment': 'low'
            }

            # 详细的技术指标验证
            tech_result = self._detailed_technical_validation(technical_data)
            validation_result['technical_analysis'] = tech_result

            # 检查失败条件
            if tech_result.get('violations'):
                validation_result['conditions_met'] = False
                validation_result['failed_conditions'] = list(tech_result['violations'].keys())
                validation_result['risk_assessment'] = 'high'

            return validation_result

        except Exception as e:
            self.logger.error(f"买入条件验证失败: {str(e)}")
            return {
                'fund_code': fund_code,
                'conditions_met': False,
                'error': str(e),
                'risk_assessment': 'critical'
            }

    def _detailed_technical_validation(self, technical_data: Dict[str, Any]) -> Dict[str, Any]:
        """详细的技术指标验证"""
        result = {
            'bollinger_analysis': {},
            'rsi_analysis': {},
            'volume_analysis': {},
            'macd_analysis': {},
            'violations': {},
            'overall_score': 0.0
        }

        try:
            indicators = technical_data.get('indicators', {})

            # 布林线详细分析
            bb_result = self._analyze_bollinger_bands_detailed(indicators)
            result['bollinger_analysis'] = bb_result
            if not bb_result.get('condition_met', True):
                result['violations']['bollinger_bands'] = bb_result.get('violation_reason')

            # RSI详细分析
            rsi_result = self._analyze_rsi_detailed(indicators)
            result['rsi_analysis'] = rsi_result
            if not rsi_result.get('condition_met', True):
                result['violations']['rsi'] = rsi_result.get('violation_reason')

            # 成交量详细分析
            volume_result = self._analyze_volume_detailed(indicators)
            result['volume_analysis'] = volume_result
            if not volume_result.get('condition_met', True):
                result['violations']['volume'] = volume_result.get('violation_reason')

            # MACD分析（可选）
            macd_result = self._analyze_macd_detailed(indicators)
            result['macd_analysis'] = macd_result

            # 计算综合评分
            scores = [
                bb_result.get('score', 0.5),
                rsi_result.get('score', 0.5),
                volume_result.get('score', 0.5),
                macd_result.get('score', 0.5)
            ]
            result['overall_score'] = np.mean(scores)

        except Exception as e:
            self.logger.error(f"详细技术验证异常: {str(e)}")
            result['error'] = str(e)

        return result

    def _analyze_bollinger_bands_detailed(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """详细的布林线分析"""
        try:
            bb_position = indicators.get('bb_position', 0.5)
            bb_width = indicators.get('bb_width', 0.1)
            price = indicators.get('close', 0)
            bb_upper = indicators.get('bb_upper', price * 1.02)
            bb_middle = indicators.get('bb_middle', price)
            bb_lower = indicators.get('bb_lower', price * 0.98)

            # 计算相对位置
            if bb_upper != bb_lower:
                relative_position = (price - bb_lower) / (bb_upper - bb_lower)
            else:
                relative_position = 0.5

            # 买入条件：价格应该接近或低于下轨
            condition_met = relative_position <= 0.2  # 在下轨附近或以下

            result = {
                'bb_position': bb_position,
                'bb_width': bb_width,
                'relative_position': relative_position,
                'price': price,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'condition_met': condition_met,
                'score': 1.0 - relative_position if condition_met else 0.2,
                'analysis': f"价格位于布林线 {relative_position:.1%} 位置"
            }

            if not condition_met:
                result['violation_reason'] = f"价格位置 {relative_position:.1%} 高于买入要求（应≤20%）"

            return result

        except Exception as e:
            return {
                'condition_met': False,
                'error': str(e),
                'score': 0.0
            }

    def _analyze_rsi_detailed(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """详细的RSI分析"""
        try:
            rsi = indicators.get('rsi', 50)
            rsi_threshold = self.risk_config['technical_indicators']['rsi']['buy_max_threshold']

            # RSI条件：不能在超买区域
            condition_met = rsi <= rsi_threshold

            # 计算评分：RSI越低（但不要太低）评分越高
            if rsi < 20:  # 过度超卖
                score = 0.6
            elif rsi <= 40:  # 理想买入区域
                score = 1.0
            elif rsi <= rsi_threshold:  # 可接受区域
                score = 0.8
            else:  # 超买区域
                score = 0.2

            result = {
                'rsi_value': rsi,
                'rsi_threshold': rsi_threshold,
                'condition_met': condition_met,
                'score': score,
                'rsi_level': self._get_rsi_level(rsi),
                'analysis': f"RSI {rsi:.1f} - {self._get_rsi_level(rsi)}"
            }

            if not condition_met:
                result['violation_reason'] = f"RSI {rsi:.1f} 超过买入阈值 {rsi_threshold}"

            return result

        except Exception as e:
            return {
                'condition_met': False,
                'error': str(e),
                'score': 0.0
            }

    def _analyze_volume_detailed(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """详细的成交量分析"""
        try:
            volume_ratio = indicators.get('volume_ratio', 1.0)
            avg_volume = indicators.get('avg_volume', 0)
            current_volume = indicators.get('volume', 0)
            min_ratio = self.risk_config['technical_indicators']['volume']['min_volume_ratio']

            # 成交量条件：必须有足够的成交量支撑
            condition_met = volume_ratio >= min_ratio

            # 计算评分
            if volume_ratio >= min_ratio * 2:  # 成交量非常活跃
                score = 1.0
            elif volume_ratio >= min_ratio:  # 成交量充足
                score = 0.8
            else:  # 成交量不足
                score = 0.3

            result = {
                'volume_ratio': volume_ratio,
                'min_required_ratio': min_ratio,
                'current_volume': current_volume,
                'avg_volume': avg_volume,
                'condition_met': condition_met,
                'score': score,
                'volume_level': self._get_volume_level(volume_ratio),
                'analysis': f"成交量比率 {volume_ratio:.2f} - {self._get_volume_level(volume_ratio)}"
            }

            if not condition_met:
                result['violation_reason'] = f"成交量比率 {volume_ratio:.2f} 低于最小要求 {min_ratio}"

            return result

        except Exception as e:
            return {
                'condition_met': False,
                'error': str(e),
                'score': 0.0
            }

    def _analyze_macd_detailed(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """详细的MACD分析"""
        try:
            macd_dif = indicators.get('macd_dif', 0)
            macd_dea = indicators.get('macd_dea', 0)
            macd_histogram = indicators.get('macd_histogram', 0)
            macd_bullish = indicators.get('macd_bullish', False)

            # MACD评分逻辑
            score = 0.5  # 默认中性
            if macd_bullish and macd_dif > macd_dea:
                score = 0.9  # 强烈看涨
            elif macd_dif > macd_dea:
                score = 0.7  # 看涨
            elif macd_dif < macd_dea and macd_histogram > 0:
                score = 0.6  # 可能转涨

            return {
                'macd_dif': macd_dif,
                'macd_dea': macd_dea,
                'macd_histogram': macd_histogram,
                'macd_bullish': macd_bullish,
                'score': score,
                'signal': 'bullish' if macd_dif > macd_dea else 'bearish',
                'analysis': f"MACD {'看涨' if macd_dif > macd_dea else '看跌'}"
            }

        except Exception as e:
            return {
                'score': 0.5,
                'error': str(e)
            }

    def _get_rsi_level(self, rsi: float) -> str:
        """获取RSI水平描述"""
        if rsi < 20:
            return "严重超卖"
        elif rsi < 30:
            return "超卖"
        elif rsi < 40:
            return "偏弱"
        elif rsi < 60:
            return "中性"
        elif rsi < 70:
            return "偏强"
        elif rsi < 80:
            return "超买"
        else:
            return "严重超买"

    def _get_volume_level(self, volume_ratio: float) -> str:
        """获取成交量水平描述"""
        if volume_ratio < 0.5:
            return "极低"
        elif volume_ratio < 0.8:
            return "偏低"
        elif volume_ratio < 1.2:
            return "正常"
        elif volume_ratio < 2.0:
            return "活跃"
        else:
            return "极度活跃"
