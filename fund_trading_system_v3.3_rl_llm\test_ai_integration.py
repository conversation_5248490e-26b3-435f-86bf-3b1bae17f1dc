"""
AI集成功能测试脚本
用于验证LLM分析器和自然语言接口的基本功能
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_ai_config():
    """测试AI配置管理"""
    print("🔧 测试AI配置管理...")
    
    try:
        from config.ai_config import AIConfig
        
        config = AIConfig()
        print(f"✅ 配置文件加载成功")
        
        # 验证配置
        validation = config.validate_config()
        print(f"配置验证结果: {'✅ 有效' if validation['valid'] else '❌ 无效'}")
        
        if validation['errors']:
            print("错误:")
            for error in validation['errors']:
                print(f"  - {error}")
        
        if validation['warnings']:
            print("警告:")
            for warning in validation['warnings']:
                print(f"  - {warning}")
        
        active_provider = config.get_active_provider()
        print(f"活跃的LLM提供商: {active_provider or '无'}")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {str(e)}")
        return None

def test_llm_analyzer(config=None):
    """测试LLM市场分析器"""
    print("\n🤖 测试LLM市场分析器...")
    
    try:
        from analyzers import LLMMarketAnalyzer
        from core.data_structures import DimensionEvaluationResult
        
        analyzer = LLMMarketAnalyzer(config)
        
        if analyzer.client:
            print("✅ LLM客户端初始化成功")
        else:
            print("⚠️  LLM客户端未初始化，将使用兜底功能")
        
        # 创建测试数据
        test_data = {
            'evaluations': {
                '趋势': DimensionEvaluationResult(
                    dimension_name='趋势',
                    state='上升',
                    score=0.65,
                    confidence=0.8,
                    signals=['买入'],
                    data_quality='good',
                    details={'trend': '上升'}
                ),
                '波动性': DimensionEvaluationResult(
                    dimension_name='波动性',
                    state='中等',
                    score=0.45,
                    confidence=0.7,
                    signals=['中性'],
                    data_quality='good',
                    details={'volatility': '中等'}
                )
            },
            'price_data': {
                'current_price': 1.2345,
                'change_pct': 2.15,
                'volume': 1500000
            }
        }
        
        # 测试市场分析
        result = analyzer.analyze_market_narrative(test_data, fund_code='000001')
        
        print(f"分析类型: {result.get('analysis_type', 'N/A')}")
        print(f"市场情绪: {result.get('market_sentiment', 'N/A')}")
        print(f"置信度: {result.get('confidence_level', 0):.2f}")
        
        if result.get('error'):
            print(f"⚠️  分析过程中出现错误: {result['error']}")
        else:
            print("✅ 市场分析完成")
        
        return analyzer
        
    except Exception as e:
        print(f"❌ LLM分析器测试失败: {str(e)}")
        return None

def test_natural_language_interface(analyzer=None):
    """测试自然语言接口"""
    print("\n💬 测试自然语言接口...")
    
    try:
        from analyzers import NaturalLanguageInterface
        
        nl_interface = NaturalLanguageInterface(analyzer)
        
        # 测试查询
        test_queries = [
            "当前市场状态如何？",
            "基金000001怎么样？",
            "现在适合买入吗？"
        ]
        
        context_data = {
            'fund_data': {
                '000001': {
                    'nav': 1.2345,
                    'change_pct': 2.15,
                    'name': '测试基金'
                }
            },
            'market_analysis': {
                'market_sentiment': '积极',
                'market_drivers': ['技术面改善'],
                'risk_points': ['波动性增加']
            }
        }
        
        for query in test_queries:
            print(f"\n查询: {query}")
            result = nl_interface.process_query(query, context_data)
            
            if result['success']:
                print(f"✅ 查询类型: {result['query_type']}")
                print(f"回答: {result['response'][:100]}...")
            else:
                print(f"❌ 查询失败: {result.get('error', '未知错误')}")
        
        print("✅ 自然语言接口测试完成")
        return nl_interface
        
    except Exception as e:
        print(f"❌ 自然语言接口测试失败: {str(e)}")
        return None

def test_market_classifier():
    """测试市场分类器"""
    print("\n🏷️  测试市场分类器...")
    
    try:
        from analyzers import MultiDimensionalMarketClassifier
        from core.data_structures import DimensionEvaluationResult
        
        classifier = MultiDimensionalMarketClassifier()
        
        # 创建测试数据
        evaluations = {
            '趋势': DimensionEvaluationResult(
                dimension_name='趋势',
                state='上升',
                score=0.65,
                confidence=0.8,
                signals=['买入'],
                data_quality='good',
                details={'trend': '上升'}
            ),
            '波动性': DimensionEvaluationResult(
                dimension_name='波动性',
                state='中等',
                score=0.45,
                confidence=0.7,
                signals=['中性'],
                data_quality='good',
                details={'volatility': '中等'}
            ),
            '流动性': DimensionEvaluationResult(
                dimension_name='流动性',
                state='正常',
                score=0.55,
                confidence=0.75,
                signals=['正常'],
                data_quality='good',
                details={'liquidity': '正常'}
            )
        }
        
        # 测试分类
        result = classifier.classify_market(evaluations, fund_code='000001', debug=True)
        
        print(f"✅ 主要分类: {result['primary_classification']}")
        print(f"分类置信度: {result['classification_confidence']:.3f}")
        print(f"市场特征: {', '.join(result['market_characteristics'])}")
        
        return classifier
        
    except Exception as e:
        print(f"❌ 市场分类器测试失败: {str(e)}")
        return None

def run_integration_test():
    """运行完整的集成测试"""
    print("🚀 开始AI集成功能测试")
    print("=" * 50)
    
    # 测试配置管理
    config = test_ai_config()
    
    # 测试LLM分析器
    analyzer = test_llm_analyzer(config)
    
    # 测试自然语言接口
    nl_interface = test_natural_language_interface(analyzer)
    
    # 测试市场分类器
    classifier = test_market_classifier()
    
    print("\n" + "=" * 50)
    print("🎉 AI集成功能测试完成！")
    
    # 总结测试结果
    components = {
        'AI配置管理': config is not None,
        'LLM分析器': analyzer is not None,
        '自然语言接口': nl_interface is not None,
        '市场分类器': classifier is not None
    }
    
    print("\n📊 测试结果总结:")
    for component, status in components.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {component}: {'正常' if status else '失败'}")
    
    # 给出建议
    print("\n💡 建议:")
    if not config or not config.get_active_provider():
        print("- 配置API密钥以启用完整的LLM功能")
        print("- 设置环境变量 MOONSHOT_API_KEY 或编辑配置文件")
    
    if analyzer and analyzer.client:
        print("- LLM功能已就绪，可以开始使用AI分析")
    else:
        print("- LLM服务不可用，系统将使用兜底分析功能")
    
    print("- 可以运行 examples/ai_integration_demo.py 查看完整演示")

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    run_integration_test()

if __name__ == "__main__":
    main()