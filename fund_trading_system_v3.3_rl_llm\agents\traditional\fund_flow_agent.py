"""
资金流向分析智能体
负责分析基金的资金流向和实时价格
"""

import sys
import os
from typing import Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.utils import *


class FundFlowAgent(BaseAgent):
    """
    @class FundFlowAgent
    @brief 资金流向分析智能体
    @details 负责分析基金的资金流向和实时价格
    """
    
    def __init__(self, name: str = "FundFlowAgent"):
        super().__init__(name, "fund_flow_analysis")
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理资金流向分析请求"""
        fund_code = data.get('fund_code')
        if not fund_code:
            return {'error': 'No fund_code provided'}
            
        try:
            # 获取资金流向数据
            flow_data = self.analyze_fund_flow(fund_code)
            return flow_data
        except Exception as e:
            self.logger.error(f"Fund flow analysis failed for {fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def get_fund_change_rate(self, fund_code: str) -> float:
        """
        @brief 获取基金涨跌幅
        @param fund_code: 基金代码
        @return: 涨跌幅（百分比）
        """
        try:
            # 实际涨跌幅数据获取需要在这里实现
            raise NotImplementedError("Real change rate data source required")
        except Exception as e:
            self.logger.error(f"Failed to get change rate for {fund_code}: {str(e)}")
            return 0.0
    
    def get_real_time_price(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 获取实时价格数据 - 仅使用真实数据
        @param fund_code: 基金代码
        @return: 价格数据字典
        """
        try:
            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")
                
            # 优先获取实时行情数据
            try:
                quote_data = get_realtime_quote(fund_code)
                if 'error' not in quote_data:
                    return {
                        'fund_code': fund_code,
                        'price': quote_data['price'],
                        'change_rate': quote_data['change_rate'],
                        'volume': quote_data['volume'],
                        'amount': quote_data.get('amount', 0),
                        'bid1': quote_data.get('bid1', quote_data['price']),
                        'ask1': quote_data.get('ask1', quote_data['price']),
                        'timestamp': quote_data['timestamp'],
                        'data_source': 'real_time_quote'
                    }
            except Exception as quote_error:
                self.logger.warning(f"Real-time quote failed for {fund_code}: {quote_error}")
            
            # 备用：获取K线数据
            df = get_kline(fund_code, freq='D')
            if df.empty or len(df) < 1:
                raise ValueError(f"No historical data available for {fund_code}")
                
            latest_data = df.iloc[-1]
            current_price = float(latest_data['close'])
            
            # 计算涨跌幅
            if len(df) >= 2:
                prev_price = float(df.iloc[-2]['close'])
                change_rate = ((current_price - prev_price) / prev_price) * 100
            else:
                change_rate = 0.0
            
            # 估算买卖盘（基于高低价）
            day_range = float(latest_data['high']) - float(latest_data['low'])
            bid_estimate = current_price - (day_range * 0.001)  # 估算买一价
            ask_estimate = current_price + (day_range * 0.001)  # 估算卖一价
            
            return {
                'fund_code': fund_code,
                'price': current_price,
                'change_rate': change_rate,
                'volume': float(latest_data.get('vol', 0)),
                'amount': float(latest_data.get('amount', 0)),
                'high': float(latest_data['high']),
                'low': float(latest_data['low']),
                'open': float(latest_data['open']),
                'bid1': bid_estimate,
                'ask1': ask_estimate,
                'timestamp': datetime.now().isoformat(),
                'data_source': 'historical_kline'
            }

        except Exception as e:
            self.logger.error(f"Failed to get real-time price for {fund_code}: {str(e)}")
            # 不再提供模拟数据，直接返回错误
            return {
                'fund_code': fund_code,
                'price': 0.0,
                'error': str(e),
                'data_source': 'error'
            }

    def analyze_fund_flow(self, fund_code: str) -> Dict[str, Any]:
        """
        @brief 分析基金资金流向 - 完全基于真实数据
        @param fund_code: 基金代码
        @return: 资金流向分析结果
        """
        try:
            # 获取真实价格数据
            price_data = self.get_real_time_price(fund_code)

            if 'error' in price_data:
                raise ValueError(f"Failed to get price data: {price_data['error']}")

            if not CZSC_FUNC_AVAILABLE:
                raise ImportError("CZSC functions not available")

            # 计算真实资金流向强度
            flow_strength = calculate_real_fund_flow_strength(fund_code)

            # 获取成交量分析
            volume_analysis = get_volume_profile_analysis(fund_code)

            # 数据质量验证
            quality_result = validate_data_quality(fund_code, {
                'symbol': fund_code,
                'timestamp': price_data['timestamp'],
                'price_data': price_data,
                'flow_data': flow_strength
            })

            if not quality_result['overall_quality']:
                self.logger.warning(f"Data quality issues for {fund_code}: {quality_result['issues']}")

            # 综合流动性评估
            current_volume = price_data.get('volume', 0)
            current_amount = price_data.get('amount', 0)
            change_rate = price_data.get('change_rate', 0.0)

            # 基于真实指标判断流动性
            high_liquidity = (
                flow_strength['liquidity_score'] > 0.6 and
                volume_analysis['relative_volume'] > 1.2 and
                abs(change_rate) < 5.0  # 价格变动不过于剧烈
            )

            # 资金流向方向判断
            if flow_strength['net_flow_strength'] > 0.3:
                capital_flow_direction = '净流入'
            elif flow_strength['net_flow_strength'] < -0.3:
                capital_flow_direction = '净流出'
            else:
                capital_flow_direction = '平衡'

            # 成交量水平分类（基于真实数据）
            volume_level = volume_analysis['volume_quality']

            # 买卖压力分析
            buy_pressure = flow_strength['buy_pressure']
            sell_pressure = 1.0 - buy_pressure

            return {
                'fund_code': fund_code,
                'price_data': price_data,
                'flow_strength': flow_strength,
                'volume_analysis': volume_analysis,
                'current_volume': current_volume,
                'current_amount': current_amount,
                'volume_ratio': volume_analysis['relative_volume'],
                'volume_level': volume_level,
                'capital_flow': capital_flow_direction,
                'net_flow_strength': flow_strength['net_flow_strength'],
                'buy_pressure': buy_pressure,
                'sell_pressure': sell_pressure,
                'high_liquidity': high_liquidity,
                'liquidity_score': flow_strength['liquidity_score'],
                'flow_ratio': flow_strength['flow_ratio'],
                'volume_activity': flow_strength['volume_activity'],
                'change_rate': change_rate,
                'data_quality': quality_result['quality_score'],
                'analysis_time': datetime.now().isoformat(),
                'data_source': 'real_flow_analysis'
            }

        except Exception as e:
            self.logger.error(f"Failed to analyze fund flow for {fund_code}: {str(e)}")
            # 不再提供模拟数据，直接返回错误
            return {
                'fund_code': fund_code,
                'error': str(e),
                'high_liquidity': False,
                'liquidity_score': 0.0,
                'data_source': 'error'
            }
