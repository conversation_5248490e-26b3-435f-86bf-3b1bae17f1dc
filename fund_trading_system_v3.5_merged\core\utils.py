"""
Core Utilities for Trading System v3.5

Provides essential utility functions and classes for logging, data validation,
feature processing, and other common operations throughout the system.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
import json
import pickle
import hashlib
from functools import wraps
import time


class Logger:
    """
    Enhanced logging utility with structured logging capabilities
    and performance monitoring.
    """
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, name: str, log_level: str = "INFO", 
                   log_file: Optional[str] = None) -> logging.Logger:
        """
        Get or create a logger with specified configuration.
        
        Args:
            name: Logger name
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_file: Optional log file path
            
        Returns:
            Configured logger instance
        """
        if name in cls._loggers:
            return cls._loggers[name]
        
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_level.upper()))
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(getattr(logging, log_level.upper()))
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        cls._loggers[name] = logger
        return logger
    
    @classmethod
    def setup_system_logging(cls, log_level: str = "INFO", 
                           log_file: Optional[str] = None):
        """Setup system-wide logging configuration"""
        # Main system logger
        cls.get_logger("TradingSystem", log_level, log_file)
        
        # Component loggers
        component_names = [
            "RLAgent", "LLMAgent", "CZSCAgent",
            "MultiAgentCoordinator", "TradingAgent",
            "TradingConfig", "DataValidator", "FeatureProcessor"
        ]
        
        for component in component_names:
            cls.get_logger(component, log_level, log_file)
    
    @staticmethod
    def log_performance(func):
        """Decorator to log function performance"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger = Logger.get_logger(func.__module__ or "Performance")
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.debug(f"{func.__name__} executed in {execution_time:.4f}s")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func.__name__} failed after {execution_time:.4f}s: {e}")
                raise
        
        return wrapper


class DataValidator:
    """
    Data validation utility for market data and trading signals.
    Ensures data quality and consistency throughout the system.
    """
    
    def __init__(self):
        self.logger = Logger.get_logger("DataValidator")
        
        # Validation thresholds
        self.price_change_threshold = 0.3  # 30% max price change
        self.volume_multiplier_threshold = 10.0  # 10x volume increase
        self.confidence_range = (0.0, 1.0)
        
    def validate_market_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate market data for consistency and quality.
        
        Args:
            data: Market data dictionary
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Required fields
        required_fields = ['symbol', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
        for field in required_fields:
            if field not in data:
                errors.append(f"Missing required field: {field}")
        
        if errors:
            return False, errors
        
        # Price validation
        prices = [data['open'], data['high'], data['low'], data['close']]
        if any(p <= 0 for p in prices):
            errors.append("All prices must be positive")
        
        if data['high'] < max(data['open'], data['close']):
            errors.append("High price must be >= max(open, close)")
        
        if data['low'] > min(data['open'], data['close']):
            errors.append("Low price must be <= min(open, close)")
        
        # Volume validation
        if data['volume'] < 0:
            errors.append("Volume must be non-negative")
        
        # Price change validation
        if data['open'] > 0:
            price_change = abs(data['close'] - data['open']) / data['open']
            if price_change > self.price_change_threshold:
                errors.append(f"Price change {price_change:.1%} exceeds threshold {self.price_change_threshold:.1%}")
        
        # Symbol validation
        if not isinstance(data['symbol'], str) or len(data['symbol']) == 0:
            errors.append("Symbol must be a non-empty string")
        
        return len(errors) == 0, errors
    
    def validate_trading_signal(self, signal: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate trading signal for consistency.
        
        Args:
            signal: Trading signal dictionary
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Required fields
        required_fields = ['symbol', 'timestamp', 'action', 'confidence', 'agent_name']
        for field in required_fields:
            if field not in signal:
                errors.append(f"Missing required field: {field}")
        
        if errors:
            return False, errors
        
        # Action validation
        valid_actions = ['BUY', 'SELL', 'HOLD']
        if signal['action'] not in valid_actions:
            errors.append(f"Action must be one of {valid_actions}")
        
        # Confidence validation
        confidence = signal['confidence']
        if not (self.confidence_range[0] <= confidence <= self.confidence_range[1]):
            errors.append(f"Confidence must be between {self.confidence_range[0]} and {self.confidence_range[1]}")
        
        # Agent name validation
        if not isinstance(signal['agent_name'], str) or len(signal['agent_name']) == 0:
            errors.append("Agent name must be a non-empty string")
        
        return len(errors) == 0, errors
    
    def detect_outliers(self, data: List[float], method: str = "iqr") -> List[int]:
        """
        Detect outliers in numerical data.
        
        Args:
            data: List of numerical values
            method: Outlier detection method ('iqr', 'zscore')
            
        Returns:
            List of outlier indices
        """
        if len(data) < 4:
            return []
        
        data_array = np.array(data)
        outlier_indices = []
        
        if method == "iqr":
            q1 = np.percentile(data_array, 25)
            q3 = np.percentile(data_array, 75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outlier_indices = [
                i for i, value in enumerate(data) 
                if value < lower_bound or value > upper_bound
            ]
        
        elif method == "zscore":
            mean = np.mean(data_array)
            std = np.std(data_array)
            z_scores = np.abs((data_array - mean) / std) if std > 0 else np.zeros_like(data_array)
            
            outlier_indices = [
                i for i, z_score in enumerate(z_scores) 
                if z_score > 3.0  # 3-sigma rule
            ]
        
        return outlier_indices
    
    def validate_feature_vector(self, features: np.ndarray, 
                              expected_shape: Tuple[int, ...] = (50,)) -> Tuple[bool, List[str]]:
        """
        Validate feature vector dimensions and values.
        
        Args:
            features: Feature vector array
            expected_shape: Expected shape of feature vector
            
        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []
        
        # Shape validation
        if features.shape != expected_shape:
            errors.append(f"Feature vector shape {features.shape} != expected {expected_shape}")
        
        # Value validation
        if np.any(np.isnan(features)):
            errors.append("Feature vector contains NaN values")
        
        if np.any(np.isinf(features)):
            errors.append("Feature vector contains infinite values")
        
        # Range validation (features should be reasonably bounded)
        if np.any(np.abs(features) > 1000):
            errors.append("Feature vector contains extremely large values")
        
        return len(errors) == 0, errors


class FeatureProcessor:
    """
    Feature processing utility for technical indicators and data transformations.
    Handles feature engineering, normalization, and aggregation.
    """
    
    def __init__(self):
        self.logger = Logger.get_logger("FeatureProcessor")
        self.scaler_cache = {}
    
    def calculate_technical_indicators(self, price_data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate 20-dimensional technical indicators from price data.
        
        Args:
            price_data: DataFrame with OHLCV columns
            
        Returns:
            Dictionary of technical indicators
        """
        indicators = {}
        
        try:
            # Trend indicators (5 dimensions)
            indicators['sma_5'] = price_data['close'].rolling(5).mean().iloc[-1] if len(price_data) >= 5 else price_data['close'].iloc[-1]
            indicators['sma_20'] = price_data['close'].rolling(20).mean().iloc[-1] if len(price_data) >= 20 else price_data['close'].iloc[-1]
            indicators['ema_12'] = price_data['close'].ewm(span=12).mean().iloc[-1]
            indicators['ema_26'] = price_data['close'].ewm(span=26).mean().iloc[-1]
            indicators['macd'] = indicators['ema_12'] - indicators['ema_26']
            
            # Momentum indicators (5 dimensions)
            if len(price_data) >= 14:
                indicators['rsi'] = self._calculate_rsi(price_data['close'], 14)
            else:
                indicators['rsi'] = 50.0  # Neutral RSI
            
            indicators['roc_5'] = self._calculate_roc(price_data['close'], 5)
            indicators['roc_20'] = self._calculate_roc(price_data['close'], 20)
            
            if len(price_data) >= 14:
                stoch_k, stoch_d = self._calculate_stochastic(price_data, 14)
                indicators['stoch_k'] = stoch_k
                indicators['stoch_d'] = stoch_d
            else:
                indicators['stoch_k'] = 50.0
                indicators['stoch_d'] = 50.0
            
            # Volatility indicators (5 dimensions)
            if len(price_data) >= 20:
                bb_upper, bb_middle, bb_lower = self._calculate_bollinger_bands(price_data['close'], 20)
                indicators['bb_position'] = (price_data['close'].iloc[-1] - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
                indicators['bb_width'] = (bb_upper - bb_lower) / bb_middle if bb_middle > 0 else 0.0
            else:
                indicators['bb_position'] = 0.5
                indicators['bb_width'] = 0.0
            
            indicators['atr'] = self._calculate_atr(price_data, 14)
            indicators['volatility_ratio'] = self._calculate_volatility_ratio(price_data['close'])
            indicators['price_efficiency'] = self._calculate_price_efficiency(price_data['close'])
            
            # Volume indicators (3 dimensions)
            indicators['volume_sma'] = price_data['volume'].rolling(20).mean().iloc[-1] if len(price_data) >= 20 else price_data['volume'].iloc[-1]
            indicators['volume_ratio'] = price_data['volume'].iloc[-1] / indicators['volume_sma'] if indicators['volume_sma'] > 0 else 1.0
            indicators['obv'] = self._calculate_obv(price_data)
            
            # Support/Resistance indicators (2 dimensions)
            indicators['support_level'] = self._calculate_support_level(price_data)
            indicators['resistance_level'] = self._calculate_resistance_level(price_data)
            
            # Normalize indicators to reasonable ranges
            indicators = self._normalize_indicators(indicators)
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            # Return default values
            indicators = {f'tech_indicator_{i}': 0.0 for i in range(1, 21)}
        
        return indicators
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def _calculate_roc(self, prices: pd.Series, period: int) -> float:
        """Calculate Rate of Change"""
        if len(prices) >= period + 1:
            current_price = prices.iloc[-1]
            past_price = prices.iloc[-period-1]
            return (current_price - past_price) / past_price if past_price > 0 else 0.0
        return 0.0
    
    def _calculate_stochastic(self, data: pd.DataFrame, period: int) -> Tuple[float, float]:
        """Calculate Stochastic K and D"""
        if len(data) >= period:
            lowest_low = data['low'].rolling(window=period).min()
            highest_high = data['high'].rolling(window=period).max()
            
            k = 100 * ((data['close'] - lowest_low) / (highest_high - lowest_low))
            d = k.rolling(window=3).mean()
            
            return k.iloc[-1] if not pd.isna(k.iloc[-1]) else 50.0, d.iloc[-1] if not pd.isna(d.iloc[-1]) else 50.0
        return 50.0, 50.0
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int) -> Tuple[float, float, float]:
        """Calculate Bollinger Bands"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        
        upper = sma + (std * 2)
        lower = sma - (std * 2)
        
        return upper.iloc[-1], sma.iloc[-1], lower.iloc[-1]
    
    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        if len(data) < 2:
            return 0.0
        
        high_low = data['high'] - data['low']
        high_close = np.abs(data['high'] - data['close'].shift())
        low_close = np.abs(data['low'] - data['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(window=period).mean()
        
        return atr.iloc[-1] if not pd.isna(atr.iloc[-1]) else 0.0
    
    def _calculate_volatility_ratio(self, prices: pd.Series, short_period: int = 5, long_period: int = 20) -> float:
        """Calculate volatility ratio between short and long periods"""
        if len(prices) >= long_period:
            short_vol = prices.rolling(window=short_period).std().iloc[-1]
            long_vol = prices.rolling(window=long_period).std().iloc[-1]
            return short_vol / long_vol if long_vol > 0 else 1.0
        return 1.0
    
    def _calculate_price_efficiency(self, prices: pd.Series, period: int = 20) -> float:
        """Calculate price efficiency (directional movement efficiency)"""
        if len(prices) >= period:
            net_change = abs(prices.iloc[-1] - prices.iloc[-period])
            total_movement = sum(abs(prices.diff().iloc[-period:]))
            return net_change / total_movement if total_movement > 0 else 0.0
        return 0.0
    
    def _calculate_obv(self, data: pd.DataFrame) -> float:
        """Calculate On-Balance Volume"""
        if len(data) < 2:
            return 0.0
        
        price_change = data['close'].diff()
        obv = 0.0
        
        for i in range(1, len(data)):
            if price_change.iloc[i] > 0:
                obv += data['volume'].iloc[i]
            elif price_change.iloc[i] < 0:
                obv -= data['volume'].iloc[i]
        
        return obv
    
    def _calculate_support_level(self, data: pd.DataFrame) -> float:
        """Calculate support level based on recent lows"""
        if len(data) >= 10:
            recent_lows = data['low'].rolling(window=10).min()
            return recent_lows.iloc[-1]
        return data['low'].iloc[-1]
    
    def _calculate_resistance_level(self, data: pd.DataFrame) -> float:
        """Calculate resistance level based on recent highs"""
        if len(data) >= 10:
            recent_highs = data['high'].rolling(window=10).max()
            return recent_highs.iloc[-1]
        return data['high'].iloc[-1]
    
    def _normalize_indicators(self, indicators: Dict[str, float]) -> Dict[str, float]:
        """Normalize indicators to reasonable ranges"""
        normalized = {}
        
        for key, value in indicators.items():
            if 'rsi' in key or 'stoch' in key:
                # RSI and Stochastic are already 0-100, normalize to 0-1
                normalized[key] = value / 100.0
            elif 'bb_position' in key:
                # Bollinger Band position is already 0-1
                normalized[key] = np.clip(value, 0, 1)
            elif 'ratio' in key:
                # Ratios - cap at reasonable values
                normalized[key] = np.clip(value, 0, 5.0)
            else:
                # Other indicators - use z-score normalization with reasonable bounds
                normalized[key] = np.tanh(value)  # Maps to (-1, 1)
        
        return normalized
    
    def aggregate_features(self, feature_dict: Dict[str, float], 
                         target_dimensions: int = 20) -> np.ndarray:
        """
        Aggregate features to target dimensions.
        
        Args:
            feature_dict: Dictionary of features
            target_dimensions: Target number of dimensions
            
        Returns:
            Aggregated feature array
        """
        features = np.zeros(target_dimensions)
        
        # Sort keys for consistent ordering
        sorted_keys = sorted(feature_dict.keys())
        
        for i, key in enumerate(sorted_keys[:target_dimensions]):
            features[i] = feature_dict[key]
        
        return features
    
    def create_feature_cache_key(self, symbol: str, timeframe: str, 
                                indicators: List[str]) -> str:
        """Create cache key for feature calculations"""
        key_data = f"{symbol}_{timeframe}_{','.join(sorted(indicators))}"
        return hashlib.md5(key_data.encode()).hexdigest()


# Utility functions for common operations

def ensure_directory(directory: Union[str, Path]) -> Path:
    """Ensure directory exists, create if it doesn't"""
    path = Path(directory)
    path.mkdir(parents=True, exist_ok=True)
    return path


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, return default if denominator is zero"""
    return numerator / denominator if denominator != 0 else default


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values"""
    return safe_divide(new_value - old_value, old_value) * 100


def format_currency(amount: float, symbol: str = "¥") -> str:
    """Format amount as currency"""
    return f"{symbol}{amount:,.2f}"


def format_percentage(value: float, decimals: int = 2) -> str:
    """Format value as percentage"""
    return f"{value:.{decimals}%}"


def load_json_file(file_path: Union[str, Path]) -> Dict[str, Any]:
    """Load JSON file safely"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        Logger.get_logger("Utils").error(f"Error loading JSON file {file_path}: {e}")
        return {}


def save_json_file(data: Dict[str, Any], file_path: Union[str, Path]):
    """Save data to JSON file safely"""
    try:
        ensure_directory(Path(file_path).parent)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    except Exception as e:
        Logger.get_logger("Utils").error(f"Error saving JSON file {file_path}: {e}")


def load_pickle_file(file_path: Union[str, Path]) -> Any:
    """Load pickle file safely"""
    try:
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    except Exception as e:
        Logger.get_logger("Utils").error(f"Error loading pickle file {file_path}: {e}")
        return None


def save_pickle_file(data: Any, file_path: Union[str, Path]):
    """Save data to pickle file safely"""
    try:
        ensure_directory(Path(file_path).parent)
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
    except Exception as e:
        Logger.get_logger("Utils").error(f"Error saving pickle file {file_path}: {e}") 