"""
Backtest Engine Implementation

Core backtesting engine that runs trading strategies over historical data
using the multi-agent coordination system.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from coordinators.multi_agent_coordinator import MultiAgentCoordinator, AgentWeight, CoordinatedDecision
from agents.base_agent import MarketData, TradingSignal
from core.config import TradingConfig
from core.data_structures import Position, Trade, PerformanceMetrics


@dataclass
class BacktestConfig:
    """Backtest specific configuration"""
    start_date: str = "2020-01-01"
    end_date: str = "2023-12-31"
    initial_capital: float = 100000.0
    commission: float = 0.0003  # 0.03%
    slippage: float = 0.0001    # 0.01%
    max_position_size: float = 0.2  # 20% max position
    benchmark: str = "000300.SH"
    
    
@dataclass
class BacktestResult:
    """Backtest execution results"""
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    total_return: float
    annualized_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    total_trades: int
    
    # Detailed records
    trades: List[Trade] = field(default_factory=list)
    daily_returns: List[float] = field(default_factory=list)
    equity_curve: List[float] = field(default_factory=list)
    positions_history: List[Position] = field(default_factory=list)
    
    # Agent performance breakdown
    agent_performance: Dict[str, Dict[str, float]] = field(default_factory=dict)


class BacktestEngine:
    """
    Backtesting Engine for Trading System v3.5
    
    Executes trading strategies over historical data using the multi-agent
    coordination system to generate realistic performance metrics.
    """
    
    def __init__(self, 
                 config: TradingConfig,
                 backtest_config: Optional[BacktestConfig] = None):
        """
        Initialize the backtest engine.
        
        Args:
            config: Main trading system configuration
            backtest_config: Backtest specific configuration
        """
        self.logger = logging.getLogger("BacktestEngine")
        self.config = config
        self.backtest_config = backtest_config or BacktestConfig()
        
        # Initialize multi-agent coordinator
        agent_weights = AgentWeight(
            rl_weight=config.coordination_config.rl_weight,
            llm_weight=config.coordination_config.llm_weight,
            czsc_weight=config.coordination_config.czsc_weight
        )
        self.coordinator = MultiAgentCoordinator(agent_weights)
        
        # Backtest state
        self.current_capital = self.backtest_config.initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.equity_curve: List[float] = []
        self.daily_returns: List[float] = []
        
        # Performance tracking
        self.agent_decisions = {
            'rl': {'correct': 0, 'total': 0},
            'llm': {'correct': 0, 'total': 0},
            'czsc': {'correct': 0, 'total': 0}
        }
        
        self.logger.info(f"Backtest engine initialized for period {self.backtest_config.start_date} to {self.backtest_config.end_date}")
    
    def run_backtest(self, data: pd.DataFrame) -> BacktestResult:
        """
        Run backtest on historical data.
        
        Args:
            data: Historical market data DataFrame with OHLCV columns
            
        Returns:
            BacktestResult with comprehensive performance metrics
        """
        try:
            self.logger.info("Starting backtest execution...")
            
            # Validate and prepare data
            data = self._prepare_data(data)
            
            # Reset backtest state
            self._reset_state()
            
            # Execute backtest day by day
            for idx, row in data.iterrows():
                market_data = self._create_market_data(row)
                self._process_trading_day(market_data)
                
                # Record daily performance
                daily_equity = self._calculate_portfolio_value(market_data)
                self.equity_curve.append(daily_equity)
                
                if len(self.equity_curve) > 1:
                    daily_return = (daily_equity - self.equity_curve[-2]) / self.equity_curve[-2]
                    self.daily_returns.append(daily_return)
            
            # Generate final results
            result = self._generate_results()
            
            self.logger.info(f"Backtest completed. Total return: {result.total_return:.2%}")
            return result
            
        except Exception as e:
            self.logger.error(f"Backtest execution failed: {e}")
            raise
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Validate and prepare historical data for backtesting"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        # Check required columns
        missing_cols = [col for col in required_columns if col not in data.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Ensure data is sorted by date
        if 'date' in data.columns:
            data = data.sort_values('date')
        
        # Add technical indicators if not present
        data = self._add_technical_indicators(data)
        
        return data
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add basic technical indicators for agent analysis"""
        df = data.copy()
        
        # Moving averages
        df['ma_5'] = df['close'].rolling(5).mean()
        df['ma_20'] = df['close'].rolling(20).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # Volume indicators
        df['volume_ma'] = df['volume'].rolling(10).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        return df
    
    def _reset_state(self):
        """Reset backtest state for new run"""
        self.current_capital = self.backtest_config.initial_capital
        self.positions.clear()
        self.trades.clear()
        self.equity_curve.clear()
        self.daily_returns.clear()
        
        # Reset agent performance tracking
        for agent in self.agent_decisions:
            self.agent_decisions[agent] = {'correct': 0, 'total': 0}
    
    def _create_market_data(self, row: pd.Series) -> MarketData:
        """Create MarketData object from DataFrame row"""
        # Extract technical indicators
        technical_indicators = {
            'ma_5': row.get('ma_5', 0),
            'ma_20': row.get('ma_20', 0),
            'rsi': row.get('rsi', 50),
            'macd': row.get('macd', 0),
            'macd_signal': row.get('macd_signal', 0),
            'bb_upper': row.get('bb_upper', 0),
            'bb_lower': row.get('bb_lower', 0),
            'volume_ratio': row.get('volume_ratio', 1),
            # Additional indicators with defaults
            'kdj_k': 50,  # Default neutral values
            'atr': row.get('close', 0) * 0.02,  # 2% of price as default ATR
            'price_position': 0.5  # Neutral position
        }
        
        # CZSC structure (simplified for backtest)
        czsc_structure = {
            'bi_direction': 0,
            'bi_strength': 0.5,
            'duan_level': 1,
            'zhongshu_status': 0,
            'trend_direction': 1 if row['close'] > row.get('ma_20', row['close']) else -1
        }
        
        return MarketData(
            symbol=row.get('symbol', 'BACKTEST'),
            timestamp=row.get('date', datetime.now()),
            open=row['open'],
            high=row['high'],
            low=row['low'],
            close=row['close'],
            volume=row['volume'],
            technical_indicators=technical_indicators,
            czsc_structure=czsc_structure
        )
    
    def _process_trading_day(self, market_data: MarketData):
        """Process a single trading day"""
        # Get coordinated decision from multi-agent system
        decision = self.coordinator.generate_coordinated_decision(market_data)
        
        # Execute trading based on decision
        if decision.final_action in ['buy', 'sell']:
            self._execute_trade(market_data, decision)
        
        # Track agent performance
        self._track_agent_performance(market_data, decision)
    
    def _execute_trade(self, market_data: MarketData, decision: CoordinatedDecision):
        """Execute trade based on coordinated decision"""
        symbol = market_data.symbol
        price = market_data.close
        action = decision.final_action
        confidence = decision.final_confidence
        
        # Calculate position size based on confidence and risk management
        position_size = self._calculate_position_size(confidence, price)
        
        if action == 'buy' and symbol not in self.positions:
            # Open long position
            shares = int(position_size / price)
            if shares > 0:
                cost = shares * price * (1 + self.backtest_config.commission + self.backtest_config.slippage)
                
                if cost <= self.current_capital:
                    position = Position(
                        symbol=symbol,
                        quantity=shares,
                        entry_price=price,
                        entry_time=market_data.timestamp,
                        current_price=price
                    )
                    
                    self.positions[symbol] = position
                    self.current_capital -= cost
                    
                    trade = Trade(
                        symbol=symbol,
                        action='buy',
                        quantity=shares,
                        price=price,
                        timestamp=market_data.timestamp,
                        commission=shares * price * self.backtest_config.commission
                    )
                    self.trades.append(trade)
                    
                    self.logger.debug(f"Opened position: {shares} shares of {symbol} at {price}")
        
        elif action == 'sell' and symbol in self.positions:
            # Close long position
            position = self.positions[symbol]
            revenue = position.quantity * price * (1 - self.backtest_config.commission - self.backtest_config.slippage)
            
            self.current_capital += revenue
            
            trade = Trade(
                symbol=symbol,
                action='sell',
                quantity=position.quantity,
                price=price,
                timestamp=market_data.timestamp,
                commission=position.quantity * price * self.backtest_config.commission
            )
            self.trades.append(trade)
            
            self.logger.debug(f"Closed position: {position.quantity} shares of {symbol} at {price}")
            del self.positions[symbol]
    
    def _calculate_position_size(self, confidence: float, price: float) -> float:
        """Calculate position size based on confidence and risk management"""
        base_position = self.current_capital * self.backtest_config.max_position_size
        confidence_adjusted = base_position * confidence
        return min(confidence_adjusted, base_position)
    
    def _apply_risk_management(self, market_data, position, action) -> Dict[str, Any]:
        """风险管理钩子：支持基础VaR风控"""
        risk_info = {
            'stop_loss_triggered': False,
            'take_profit_triggered': False,
            'var_exceeded': False,
            'cvar_exceeded': False,
            'dynamic_stop_loss': False
        }
        # 动态止损逻辑占位
        if getattr(self.config.risk_config, 'dynamic_stop_loss', False):
            risk_info['dynamic_stop_loss'] = True
        # 基础VaR实现（历史法）
        if hasattr(self, 'equity_curve') and len(self.equity_curve) > 20:
            returns = np.diff(self.equity_curve[-21:]) / self.equity_curve[-21:-1]
            var_level = self.config.risk_config.var_confidence
            var = np.percentile(returns, 100 * (1 - var_level))
            # 若今日预期损失超过VaR，则触发风控
            if (market_data.close - market_data.open) / market_data.open < var:
                risk_info['var_exceeded'] = True
        # CVaR占位
        # if ...: risk_info['cvar_exceeded'] = True
        return risk_info
    
    def _calculate_portfolio_value(self, market_data: MarketData) -> float:
        """Calculate total portfolio value"""
        total_value = self.current_capital
        
        # Add value of open positions
        for position in self.positions.values():
            if position.symbol == market_data.symbol:
                position.current_price = market_data.close
                total_value += position.quantity * market_data.close
        
        return total_value
    
    def _track_agent_performance(self, market_data: MarketData, decision: CoordinatedDecision):
        """Track individual agent performance for analysis"""
        # Simplified performance tracking - can be enhanced
        for agent_type in ['rl', 'llm', 'czsc']:
            if agent_type in decision.individual_signals:
                signal = decision.individual_signals[agent_type]
                self.agent_decisions[agent_type]['total'] += 1
                
                # Simple correctness check (can be improved)
                if signal.action == decision.final_action:
                    self.agent_decisions[agent_type]['correct'] += 1
    
    def _generate_results(self) -> BacktestResult:
        """Generate comprehensive backtest results"""
        final_capital = self.equity_curve[-1] if self.equity_curve else self.backtest_config.initial_capital
        total_return = (final_capital - self.backtest_config.initial_capital) / self.backtest_config.initial_capital
        
        # Calculate performance metrics
        if self.daily_returns:
            daily_returns_array = np.array(self.daily_returns)
            annualized_return = np.mean(daily_returns_array) * 252
            volatility = np.std(daily_returns_array) * np.sqrt(252)
            sharpe_ratio = annualized_return / volatility if volatility > 0 else 0
            
            # Calculate max drawdown
            equity_array = np.array(self.equity_curve)
            peak = np.maximum.accumulate(equity_array)
            drawdown = (equity_array - peak) / peak
            max_drawdown = np.min(drawdown)
        else:
            annualized_return = 0
            sharpe_ratio = 0
            max_drawdown = 0
        
        # Calculate win rate
        profitable_trades = sum(1 for trade in self.trades[::2] if len(self.trades) > 0)  # Simplified
        win_rate = profitable_trades / len(self.trades) * 2 if self.trades else 0
        
        # Agent performance breakdown
        agent_performance = {}
        for agent, stats in self.agent_decisions.items():
            accuracy = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            agent_performance[agent] = {
                'accuracy': accuracy,
                'total_decisions': stats['total']
            }
        
        return BacktestResult(
            start_date=self.backtest_config.start_date,
            end_date=self.backtest_config.end_date,
            initial_capital=self.backtest_config.initial_capital,
            final_capital=final_capital,
            total_return=total_return,
            annualized_return=annualized_return,
            max_drawdown=max_drawdown,
            sharpe_ratio=sharpe_ratio,
            win_rate=win_rate,
            total_trades=len(self.trades),
            trades=self.trades.copy(),
            daily_returns=self.daily_returns.copy(),
            equity_curve=self.equity_curve.copy(),
            agent_performance=agent_performance
        ) 