"""
RL Infrastructure Module

This module provides comprehensive reinforcement learning infrastructure including:
- Enhanced training systems with hyperparameter optimization
- Online learning capabilities
- A/B testing framework for model comparison
- Advanced evaluation metrics and reporting
- Model management and deployment tools
"""

# Core components
from .trading_environment import TradingEnvironment
from .model_manager import RLModelManager
from .enhanced_model_manager import HotSwapModelManager
from .enhanced_training_system import EnhancedTrainingSystem

# New enhanced components
try:
    from .online_learning_manager import (
        OnlineLearningManager, 
        OnlineLearningConfig, 
        OnlineLearningMode,
        LearningTrigger,
        LearningMetrics,
        OnlineLearningResult
    )
    ONLINE_LEARNING_AVAILABLE = True
except ImportError:
    ONLINE_LEARNING_AVAILABLE = False

try:
    from .ab_testing_framework import (
        ABTestingFramework,
        ABTestConfig,
        ModelVariant,
        TestStatus,
        TrafficAllocationMethod,
        TestResult,
        ABTestReport,
        create_ab_test
    )
    AB_TESTING_AVAILABLE = True
except ImportError:
    AB_TESTING_AVAILABLE = False

try:
    from .advanced_evaluator import (
        AdvancedEvaluator,
        EvaluationResult,
        RiskMetric,
        PerformanceMetric
    )
    ADVANCED_EVALUATOR_AVAILABLE = True
except ImportError:
    ADVANCED_EVALUATOR_AVAILABLE = False

try:
    from .evaluation_report_generator import (
        EvaluationReportGenerator,
        ReportConfig
    )
    REPORT_GENERATOR_AVAILABLE = True
except ImportError:
    REPORT_GENERATOR_AVAILABLE = False

# Version info
__version__ = "3.5.0"
__author__ = "Fund Trading System Team"

# Feature availability flags
ENHANCED_FEATURES = {
    'online_learning': ONLINE_LEARNING_AVAILABLE,
    'ab_testing': AB_TESTING_AVAILABLE,
    'advanced_evaluation': ADVANCED_EVALUATOR_AVAILABLE,
    'report_generation': REPORT_GENERATOR_AVAILABLE
}

# Export all available components
__all__ = [
    # Core components
    'TradingEnvironment',
    'RLModelManager',
    'HotSwapModelManager',
    'EnhancedTrainingSystem',

    # Feature availability
    'ENHANCED_FEATURES',
]

# Add enhanced components if available
if ONLINE_LEARNING_AVAILABLE:
    __all__.extend([
        'OnlineLearningManager',
        'OnlineLearningConfig',
        'OnlineLearningMode',
        'LearningTrigger',
        'LearningMetrics',
        'OnlineLearningResult'
    ])

if AB_TESTING_AVAILABLE:
    __all__.extend([
        'ABTestingFramework',
        'ABTestConfig',
        'ModelVariant',
        'TestStatus',
        'TrafficAllocationMethod',
        'TestResult',
        'ABTestReport',
        'create_ab_test'
    ])

if ADVANCED_EVALUATOR_AVAILABLE:
    __all__.extend([
        'AdvancedEvaluator',
        'EvaluationResult',
        'RiskMetric',
        'PerformanceMetric'
    ])

if REPORT_GENERATOR_AVAILABLE:
    __all__.extend([
        'EvaluationReportGenerator',
        'ReportConfig'
    ])


def get_feature_status() -> dict:
    """Get the status of all enhanced features"""
    return ENHANCED_FEATURES.copy()


def check_dependencies() -> dict:
    """Check the availability of optional dependencies"""
    dependencies = {}
    
    try:
        import torch
        dependencies['torch'] = True
    except ImportError:
        dependencies['torch'] = False
    
    try:
        import numpy
        dependencies['numpy'] = True
    except ImportError:
        dependencies['numpy'] = False
    
    try:
        import pandas
        dependencies['pandas'] = True
    except ImportError:
        dependencies['pandas'] = False
    
    try:
        import scipy
        dependencies['scipy'] = True
    except ImportError:
        dependencies['scipy'] = False
    
    try:
        import matplotlib
        dependencies['matplotlib'] = True
    except ImportError:
        dependencies['matplotlib'] = False
    
    try:
        import seaborn
        dependencies['seaborn'] = True
    except ImportError:
        dependencies['seaborn'] = False
    
    try:
        import sklearn
        dependencies['sklearn'] = True
    except ImportError:
        dependencies['sklearn'] = False
    
    return dependencies


def print_feature_summary():
    """Print a summary of available features"""
    print("=== RL Infrastructure Feature Summary ===")
    print(f"Version: {__version__}")
    print()
    
    print("Enhanced Features:")
    for feature, available in ENHANCED_FEATURES.items():
        status = "✅ Available" if available else "❌ Not Available"
        print(f"  {feature}: {status}")
    
    print()
    print("Dependencies:")
    deps = check_dependencies()
    for dep, available in deps.items():
        status = "✅ Installed" if available else "❌ Missing"
        print(f"  {dep}: {status}")
    
    print()
    if all(ENHANCED_FEATURES.values()):
        print("🎉 All enhanced features are available!")
    else:
        missing = [f for f, a in ENHANCED_FEATURES.items() if not a]
        print(f"⚠️  Missing features: {', '.join(missing)}")
        print("   Install missing dependencies to enable all features.")


# Initialize logging for the module
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())
