#!/usr/bin/env python3
"""
全面修复NumPy和statsmodels兼容性问题
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout.strip():
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        return False

def comprehensive_fix():
    """全面修复"""
    print("=== 全面修复NumPy/statsmodels兼容性 ===")
    
    # 方案1: 升级statsmodels到兼容版本
    print("\n方案1: 升级statsmodels...")
    commands = [
        f"{sys.executable} -m pip install 'statsmodels>=0.14.0' --force-reinstall",
        f"{sys.executable} -m pip install 'scipy>=1.9.0' --force-reinstall"
    ]
    
    for cmd in commands:
        run_command(cmd)
    
    # 验证修复
    try:
        print("\n验证修复...")
        import numpy
        print(f"NumPy: {numpy.__version__}")
        
        import statsmodels
        print(f"statsmodels: {statsmodels.__version__}")
        
        # 测试问题代码
        from statsmodels.tools.numdiff import approx_fprime
        print("✓ statsmodels导入成功")
        
        return True
        
    except Exception as e:
        print(f"方案1失败: {e}")
        
        # 方案2: 降级numpy
        print("\n方案2: 降级numpy...")
        commands2 = [
            f"{sys.executable} -m pip install 'numpy==1.21.6' --force-reinstall",
            f"{sys.executable} -m pip install pandas --force-reinstall",
            f"{sys.executable} -m pip install stable-baselines3 --force-reinstall"
        ]
        
        for cmd in commands2:
            run_command(cmd)
            
        try:
            import numpy
            print(f"NumPy: {numpy.__version__}")
            from statsmodels.tools.numdiff import approx_fprime
            print("✓ 方案2成功")
            return True
        except Exception as e2:
            print(f"方案2也失败: {e2}")
            return False

def create_no_czsc_version():
    """创建不依赖CZSC的版本"""
    print("\n=== 创建无CZSC依赖版本 ===")
    
    # 读取原始文件
    try:
        with open('enhanced_rl_stock_base_sb3.py', 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        print("无法读取原始文件")
        return
    
    # 修改内容，移除CZSC依赖
    modified_content = content.replace(
        """# 尝试导入czsc_func，如果不存在则使用模拟数据
try:
    from czsc_func import get_kline
    CZSC_AVAILABLE = True
except ImportError:
    CZSC_AVAILABLE = False
    print("Warning: czsc_func not available, using simulated data")""",
        """# 直接使用模拟数据，避免CZSC依赖问题
CZSC_AVAILABLE = False
print("使用模拟数据，避免依赖问题")"""
    )
    
    # 保存修改后的文件
    with open('enhanced_rl_stock_base_sb3_no_czsc.py', 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("已创建无CZSC依赖版本: enhanced_rl_stock_base_sb3_no_czsc.py")

def main():
    """主函数"""
    print("全面修复工具")
    print("=" * 40)
    
    # 尝试修复
    success = comprehensive_fix()
    
    # 创建无依赖版本
    create_no_czsc_version()
    
    if success:
        print("\n✅ 修复成功！可以运行:")
        print("python enhanced_rl_stock_base_sb3.py")
    else:
        print("\n⚠️ 如果仍有问题，请运行无依赖版本:")
        print("python enhanced_rl_stock_base_sb3_no_czsc.py")

if __name__ == "__main__":
    main()