#!/usr/bin/env python3
"""
RL-LLM协作系统完整测试运行器
整合所有测试套件，提供全面的测试报告和系统验证
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import argparse

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 导入测试套件
try:
    from tests.integration_tests import TestRunner as IntegrationTestRunner
    from tests.performance_tests import PerformanceTestRunner
    from tests.end_to_end_tests import EndToEndTestRunner
except ImportError as e:
    print(f"警告: 无法导入测试模块: {e}")
    print("请确保所有测试模块都已正确创建")


class ComprehensiveTestRunner:
    """
    @class ComprehensiveTestRunner
    @brief 综合测试运行器
    @details 整合所有测试套件，提供完整的测试执行和报告功能
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """初始化测试运行器"""
        self.config = config or {}
        self.logger = self._setup_logging()
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # 测试套件配置
        self.test_suites = {
            'integration': {
                'runner': IntegrationTestRunner,
                'enabled': self.config.get('run_integration', True),
                'timeout': self.config.get('integration_timeout', 300),  # 5分钟
                'description': '集成测试 - 验证组件间协作'
            },
            'performance': {
                'runner': PerformanceTestRunner,
                'enabled': self.config.get('run_performance', True),
                'timeout': self.config.get('performance_timeout', 600),  # 10分钟
                'description': '性能测试 - 验证系统性能指标'
            },
            'end_to_end': {
                'runner': EndToEndTestRunner,
                'enabled': self.config.get('run_end_to_end', True),
                'timeout': self.config.get('end_to_end_timeout', 900),   # 15分钟
                'description': '端到端测试 - 验证完整业务流程'
            }
        }
        
        self.logger.info("综合测试运行器初始化完成")
    
    def run_all_tests(self, test_suites: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        运行所有测试套件
        
        Args:
            test_suites: 指定要运行的测试套件列表，如果为None则运行所有启用的套件
            
        Returns:
            包含所有测试结果的字典
        """
        self.logger.info("开始运行综合测试套件...")
        self.start_time = datetime.now()
        
        # 确定要运行的测试套件
        suites_to_run = test_suites or [name for name, config in self.test_suites.items() if config['enabled']]
        
        # 运行前检查
        self._pre_test_checks()
        
        # 运行测试套件
        for suite_name in suites_to_run:
            if suite_name not in self.test_suites:
                self.logger.warning(f"未知的测试套件: {suite_name}")
                continue
            
            suite_config = self.test_suites[suite_name]
            self.logger.info(f"开始运行 {suite_name} 测试套件: {suite_config['description']}")
            
            try:
                suite_result = self._run_test_suite(suite_name, suite_config)
                self.test_results[suite_name] = suite_result
                
                # 实时报告进度
                self._report_suite_progress(suite_name, suite_result)
                
            except Exception as e:
                self.logger.error(f"{suite_name} 测试套件执行失败: {str(e)}")
                self.test_results[suite_name] = {
                    'status': 'failed',
                    'error': str(e),
                    'tests_run': 0,
                    'failures': 0,
                    'errors': 1,
                    'success_rate': 0.0
                }
        
        self.end_time = datetime.now()
        
        # 生成综合报告
        comprehensive_report = self._generate_comprehensive_report()
        
        # 保存测试结果
        self._save_test_results(comprehensive_report)
        
        # 输出摘要
        self._print_test_summary(comprehensive_report)
        
        return comprehensive_report
    
    def run_quick_smoke_tests(self) -> Dict[str, Any]:
        """
        运行快速冒烟测试
        只运行关键功能的基本验证，用于快速验证系统状态
        """
        self.logger.info("开始运行快速冒烟测试...")
        
        smoke_config = {
            'run_integration': True,
            'run_performance': False,  # 跳过性能测试以节省时间
            'run_end_to_end': False,   # 跳过端到端测试以节省时间
            'integration_timeout': 60   # 缩短超时时间
        }
        
        # 临时更新配置
        original_config = self.config.copy()
        self.config.update(smoke_config)
        
        try:
            result = self.run_all_tests(['integration'])
            return result
        finally:
            # 恢复原始配置
            self.config = original_config
    
    def run_regression_tests(self) -> Dict[str, Any]:
        """
        运行回归测试
        全面测试所有功能，确保新的更改没有破坏现有功能
        """
        self.logger.info("开始运行回归测试...")
        
        # 回归测试配置（更严格的超时和完整的测试覆盖）
        regression_config = {
            'run_integration': True,
            'run_performance': True,
            'run_end_to_end': True,
            'integration_timeout': 600,
            'performance_timeout': 900,
            'end_to_end_timeout': 1200
        }
        
        # 临时更新配置
        original_config = self.config.copy()
        self.config.update(regression_config)
        
        try:
            result = self.run_all_tests()
            return result
        finally:
            # 恢复原始配置
            self.config = original_config
    
    def _run_test_suite(self, suite_name: str, suite_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行单个测试套件"""
        runner_class = suite_config['runner']
        timeout = suite_config['timeout']
        
        start_time = time.time()
        
        try:
            # 执行测试套件
            if hasattr(runner_class, 'run_integration_tests'):
                result = runner_class.run_integration_tests()
            elif hasattr(runner_class, 'run_performance_tests'):
                result = runner_class.run_performance_tests()
            elif hasattr(runner_class, 'run_end_to_end_tests'):
                result = runner_class.run_end_to_end_tests()
            else:
                # 尝试实例化并调用run方法
                runner = runner_class()
                result = runner.run()
            
            execution_time = time.time() - start_time
            
            # 检查超时
            if execution_time > timeout:
                self.logger.warning(f"{suite_name} 测试套件执行时间超过预期 ({execution_time:.1f}s > {timeout}s)")
            
            # 标准化结果格式
            standardized_result = self._standardize_test_result(result, suite_name, execution_time)
            
            return standardized_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"{suite_name} 测试套件执行异常: {str(e)}")
            
            return {
                'status': 'error',
                'suite_name': suite_name,
                'execution_time': execution_time,
                'error': str(e),
                'tests_run': 0,
                'failures': 0,
                'errors': 1,
                'success_rate': 0.0
            }
    
    def _standardize_test_result(self, result: Dict[str, Any], suite_name: str, 
                                 execution_time: float) -> Dict[str, Any]:
        """标准化测试结果格式"""
        # 基本字段
        standardized = {
            'suite_name': suite_name,
            'execution_time': execution_time,
            'timestamp': datetime.now().isoformat(),
            'tests_run': result.get('tests_run', 0),
            'failures': result.get('failures', 0),
            'errors': result.get('errors', 0),
            'success_rate': result.get('success_rate', 0.0),
        }
        
        # 确定状态
        if result.get('errors', 0) > 0:
            standardized['status'] = 'error'
        elif result.get('failures', 0) > 0:
            standardized['status'] = 'failed'
        elif result.get('success_rate', 0) >= 0.95:  # 95%以上成功率认为通过
            standardized['status'] = 'passed'
        else:
            standardized['status'] = 'partial'
        
        # 保留原始详细信息
        standardized['details'] = result.get('details', {})
        
        # 性能相关指标（如果有）
        if 'performance_metrics' in result:
            standardized['performance_metrics'] = result['performance_metrics']
        
        return standardized
    
    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成综合测试报告"""
        total_duration = (self.end_time - self.start_time).total_seconds() if self.end_time and self.start_time else 0
        
        # 汇总统计
        total_tests = sum(result.get('tests_run', 0) for result in self.test_results.values())
        total_failures = sum(result.get('failures', 0) for result in self.test_results.values())
        total_errors = sum(result.get('errors', 0) for result in self.test_results.values())
        overall_success_rate = (total_tests - total_failures - total_errors) / total_tests if total_tests > 0 else 0
        
        # 套件状态汇总
        suite_statuses = {}
        for suite_name, result in self.test_results.items():
            suite_statuses[suite_name] = result.get('status', 'unknown')
        
        # 确定整体状态
        if all(status == 'passed' for status in suite_statuses.values()):
            overall_status = 'passed'
        elif any(status == 'error' for status in suite_statuses.values()):
            overall_status = 'error'
        elif any(status == 'failed' for status in suite_statuses.values()):
            overall_status = 'failed'
        else:
            overall_status = 'partial'
        
        # 生成建议
        recommendations = self._generate_recommendations()
        
        return {
            'summary': {
                'overall_status': overall_status,
                'overall_success_rate': overall_success_rate,
                'total_duration': total_duration,
                'total_tests': total_tests,
                'total_failures': total_failures,
                'total_errors': total_errors,
                'suite_count': len(self.test_results),
                'timestamp': datetime.now().isoformat()
            },
            'suite_results': self.test_results,
            'suite_statuses': suite_statuses,
            'recommendations': recommendations,
            'system_info': self._get_system_info(),
            'config': self.config
        }
    
    def _generate_recommendations(self) -> List[str]:
        """基于测试结果生成改进建议"""
        recommendations = []
        
        # 检查成功率
        for suite_name, result in self.test_results.items():
            success_rate = result.get('success_rate', 0)
            if success_rate < 0.8:
                recommendations.append(f"{suite_name}测试套件成功率过低({success_rate:.1%})，需要关注失败用例")
        
        # 检查执行时间
        for suite_name, result in self.test_results.items():
            execution_time = result.get('execution_time', 0)
            expected_time = self.test_suites.get(suite_name, {}).get('timeout', 300)
            if execution_time > expected_time * 0.8:
                recommendations.append(f"{suite_name}测试套件执行时间过长({execution_time:.1f}s)，可能需要性能优化")
        
        # 检查错误模式
        total_errors = sum(result.get('errors', 0) for result in self.test_results.values())
        if total_errors > 0:
            recommendations.append("存在系统错误，建议检查系统环境和依赖配置")
        
        # 通用建议
        if not recommendations:
            recommendations.append("所有测试正常通过，系统状态良好")
        else:
            recommendations.append("建议优先修复失败的测试用例，确保系统稳定性")
        
        return recommendations
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        import platform
        import psutil
        
        return {
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'cpu_count': psutil.cpu_count(),
            'memory_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'disk_usage_gb': round(psutil.disk_usage('/').total / (1024**3), 2)
        }
    
    def _pre_test_checks(self) -> None:
        """测试前检查"""
        self.logger.info("执行测试前检查...")
        
        # 检查必要的模块导入
        required_modules = ['numpy', 'torch']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            self.logger.warning(f"缺少依赖模块: {missing_modules}")
        
        # 检查可用内存
        import psutil
        available_memory_gb = psutil.virtual_memory().available / (1024**3)
        if available_memory_gb < 1.0:  # 至少需要1GB可用内存
            self.logger.warning(f"可用内存不足: {available_memory_gb:.2f}GB")
        
        self.logger.info("测试前检查完成")
    
    def _report_suite_progress(self, suite_name: str, result: Dict[str, Any]) -> None:
        """报告测试套件进度"""
        status = result.get('status', 'unknown')
        success_rate = result.get('success_rate', 0)
        execution_time = result.get('execution_time', 0)
        
        status_emoji = {
            'passed': '✅',
            'failed': '❌',
            'error': '💥',
            'partial': '⚠️'
        }.get(status, '❓')
        
        self.logger.info(f"{status_emoji} {suite_name} 完成 - 状态: {status}, 成功率: {success_rate:.1%}, 耗时: {execution_time:.1f}s")
    
    def _save_test_results(self, report: Dict[str, Any]) -> None:
        """保存测试结果到文件"""
        try:
            # 创建结果目录
            results_dir = os.path.join(current_dir, 'test_results')
            os.makedirs(results_dir, exist_ok=True)
            
            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.json"
            filepath = os.path.join(results_dir, filename)
            
            # 保存报告
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"测试报告已保存到: {filepath}")
            
            # 同时保存最新的报告（覆盖）
            latest_filepath = os.path.join(results_dir, 'latest_test_report.json')
            with open(latest_filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"保存测试结果失败: {str(e)}")
    
    def _print_test_summary(self, report: Dict[str, Any]) -> None:
        """打印测试摘要"""
        summary = report['summary']
        
        print(f"\n{'='*80}")
        print(f"RL-LLM协作系统 - 综合测试报告")
        print(f"{'='*80}")
        print(f"整体状态: {summary['overall_status'].upper()}")
        print(f"整体成功率: {summary['overall_success_rate']:.1%}")
        print(f"总执行时间: {summary['total_duration']:.1f}秒")
        print(f"总测试数: {summary['total_tests']}")
        print(f"失败数: {summary['total_failures']}")
        print(f"错误数: {summary['total_errors']}")
        print(f"测试时间: {summary['timestamp']}")
        
        print(f"\n{'='*40} 套件结果 {'='*40}")
        for suite_name, status in report['suite_statuses'].items():
            suite_result = report['suite_results'][suite_name]
            status_emoji = {
                'passed': '✅',
                'failed': '❌', 
                'error': '💥',
                'partial': '⚠️'
            }.get(status, '❓')
            
            print(f"{status_emoji} {suite_name:15} | 状态: {status:8} | "
                  f"成功率: {suite_result.get('success_rate', 0):.1%} | "
                  f"耗时: {suite_result.get('execution_time', 0):6.1f}s")
        
        print(f"\n{'='*40} 改进建议 {'='*40}")
        for i, recommendation in enumerate(report['recommendations'], 1):
            print(f"{i}. {recommendation}")
        
        print(f"\n{'='*80}")
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(self.__class__.__name__)
        
        if not logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 文件处理器
            log_dir = os.path.join(current_dir, 'logs')
            os.makedirs(log_dir, exist_ok=True)
            
            log_file = os.path.join(log_dir, 'comprehensive_tests.log')
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 格式化
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(formatter)
            file_handler.setFormatter(formatter)
            
            logger.addHandler(console_handler)
            logger.addHandler(file_handler)
            logger.setLevel(logging.DEBUG)
        
        return logger


def main():
    """主函数 - 命令行入口"""
    parser = argparse.ArgumentParser(description='RL-LLM协作系统综合测试运行器')
    
    parser.add_argument('--mode', choices=['all', 'smoke', 'regression'], 
                        default='all', help='测试模式')
    parser.add_argument('--suites', nargs='+', 
                        choices=['integration', 'performance', 'end_to_end'],
                        help='指定要运行的测试套件')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--output', type=str, help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 加载配置
    config = {}
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建测试运行器
    runner = ComprehensiveTestRunner(config)
    
    try:
        # 根据模式运行测试
        if args.mode == 'smoke':
            result = runner.run_quick_smoke_tests()
        elif args.mode == 'regression':
            result = runner.run_regression_tests()
        else:  # all
            result = runner.run_all_tests(args.suites)
        
        # 返回适当的退出码
        overall_status = result['summary']['overall_status']
        if overall_status == 'passed':
            sys.exit(0)
        elif overall_status in ['failed', 'partial']:
            sys.exit(1)
        else:  # error
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"测试运行异常: {str(e)}")
        sys.exit(3)


if __name__ == '__main__':
    main() 