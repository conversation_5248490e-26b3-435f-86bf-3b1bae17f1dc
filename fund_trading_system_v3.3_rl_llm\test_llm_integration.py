#!/usr/bin/env python3
"""
测试LLM市场分析器集成
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3

def test_llm_integration():
    """测试LLM分析器集成"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🧪 测试LLM市场分析器集成")
    print("-" * 50)
    
    try:
        # 初始化协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 检查LLM分析器是否正确初始化
        if hasattr(coordinator, 'llm_analyzer'):
            print("✅ LLM分析器已成功集成到协调器中")
            
            # 测试基金代码
            test_fund_code = '513500'
            
            print(f"🔍 测试基金 {test_fund_code} 的完整分析流程...")
            
            # 执行完整分析
            result = coordinator.coordinate_analysis(test_fund_code)
            
            # 检查结果中是否包含LLM分析
            if 'llm_analysis' in result:
                llm_analysis = result['llm_analysis']
                print("✅ LLM分析结果已包含在协调器输出中")
                
                # 显示LLM分析摘要
                if 'error' not in llm_analysis:
                    market_sentiment = llm_analysis.get('market_sentiment', '未知')
                    confidence_level = llm_analysis.get('confidence_level', 0)
                    strategy_suggestion = llm_analysis.get('strategy_suggestion', '')
                    
                    print(f"📊 LLM分析摘要:")
                    print(f"   - 市场情绪: {market_sentiment}")
                    print(f"   - 置信度: {confidence_level:.2f}")
                    print(f"   - 策略建议: {strategy_suggestion[:100]}...")
                    
                    if llm_analysis.get('market_drivers'):
                        print(f"   - 市场驱动因素: {', '.join(llm_analysis['market_drivers'][:3])}")
                    
                    print("✅ LLM分析功能正常工作")
                else:
                    print(f"⚠️ LLM分析遇到错误: {llm_analysis.get('error', '未知错误')}")
                    print("💡 这可能是由于API配置问题，但集成本身是成功的")
            else:
                print("❌ 协调器输出中未找到LLM分析结果")
                
            # 检查协调器版本
            coordinator_version = result.get('coordinator_version', 'unknown')
            print(f"🔧 协调器版本: {coordinator_version}")
            
            if coordinator_version == 'V3.2':
                print("✅ 协调器已更新到包含LLM分析的版本")
            
        else:
            print("❌ LLM分析器未正确集成到协调器中")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n🏁 LLM集成测试完成")

if __name__ == '__main__':
    test_llm_integration()