"""
Advanced VaR/CVaR Calculator

This module implements sophisticated Value at Risk and Conditional Value at Risk
calculations using multiple methodologies:
- Historical Simulation VaR
- Parametric VaR (Normal and t-distribution)
- Monte Carlo VaR
- Dynamic VaR with time-varying volatility
- Extreme Value Theory VaR
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import warnings

# Try to import optional dependencies
try:
    import scipy.stats as stats
    from scipy.optimize import minimize
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    stats = None

try:
    from arch import arch_model
    ARCH_AVAILABLE = True
except ImportError:
    ARCH_AVAILABLE = False


class VaRMethod(Enum):
    """VaR calculation methods"""
    HISTORICAL = "historical"
    PARAMETRIC_NORMAL = "parametric_normal"
    PARAMETRIC_T = "parametric_t"
    MONTE_CARLO = "monte_carlo"
    GARCH = "garch"
    EXTREME_VALUE = "extreme_value"


@dataclass
class VaRResult:
    """VaR calculation result"""
    var: float
    cvar: float
    method: VaRMethod
    confidence_level: float
    time_horizon: int
    volatility: float
    skewness: float
    kurtosis: float
    model_parameters: Dict[str, Any]
    backtesting_stats: Optional[Dict[str, Any]] = None


class AdvancedVaRCalculator:
    """Advanced VaR calculator with multiple methodologies"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._get_default_config()
        self.logger = logging.getLogger("AdvancedVaRCalculator")
        
        # Calculation cache
        self.calculation_cache = {}
        self.model_cache = {}
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'default_confidence_levels': [0.90, 0.95, 0.99],
            'default_time_horizon': 1,  # days
            'monte_carlo_simulations': 10000,
            'garch_model': 'GARCH',  # GARCH, EGARCH, GJR-GARCH
            'garch_p': 1,
            'garch_q': 1,
            'extreme_value_threshold': 0.90,  # 90th percentile for EVT
            'min_observations': 100,
            'max_observations': 1000,
            'enable_backtesting': True,
            'backtesting_window': 250  # days
        }
    
    def calculate_var(self, returns: Union[List[float], np.ndarray], 
                     method: VaRMethod = VaRMethod.HISTORICAL,
                     confidence_level: float = 0.95,
                     time_horizon: int = 1) -> VaRResult:
        """Calculate VaR using specified method"""
        try:
            returns_array = np.array(returns)
            
            # Validate inputs
            if len(returns_array) < self.config['min_observations']:
                self.logger.warning(f"Insufficient data: {len(returns_array)} observations")
                return self._create_fallback_var_result(confidence_level, time_horizon)
            
            # Limit observations for performance
            if len(returns_array) > self.config['max_observations']:
                returns_array = returns_array[-self.config['max_observations']:]
            
            # Calculate VaR based on method
            if method == VaRMethod.HISTORICAL:
                return self._calculate_historical_var(returns_array, confidence_level, time_horizon)
            elif method == VaRMethod.PARAMETRIC_NORMAL:
                return self._calculate_parametric_var(returns_array, confidence_level, time_horizon, 'normal')
            elif method == VaRMethod.PARAMETRIC_T:
                return self._calculate_parametric_var(returns_array, confidence_level, time_horizon, 't')
            elif method == VaRMethod.MONTE_CARLO:
                return self._calculate_monte_carlo_var(returns_array, confidence_level, time_horizon)
            elif method == VaRMethod.GARCH:
                return self._calculate_garch_var(returns_array, confidence_level, time_horizon)
            elif method == VaRMethod.EXTREME_VALUE:
                return self._calculate_extreme_value_var(returns_array, confidence_level, time_horizon)
            else:
                self.logger.warning(f"Unknown VaR method: {method}")
                return self._calculate_historical_var(returns_array, confidence_level, time_horizon)
                
        except Exception as e:
            self.logger.error(f"VaR calculation failed: {e}")
            return self._create_fallback_var_result(confidence_level, time_horizon)
    
    def _calculate_historical_var(self, returns: np.ndarray, confidence_level: float, 
                                time_horizon: int) -> VaRResult:
        """Calculate Historical Simulation VaR"""
        # Scale returns for time horizon
        scaled_returns = returns * np.sqrt(time_horizon)
        
        # Calculate VaR
        var = np.percentile(scaled_returns, (1 - confidence_level) * 100)
        
        # Calculate CVaR (Expected Shortfall)
        cvar = np.mean(scaled_returns[scaled_returns <= var])
        
        # Calculate statistics
        volatility = np.std(scaled_returns)
        skewness = stats.skew(scaled_returns) if SCIPY_AVAILABLE else 0.0
        kurtosis = stats.kurtosis(scaled_returns) if SCIPY_AVAILABLE else 0.0
        
        return VaRResult(
            var=var,
            cvar=cvar,
            method=VaRMethod.HISTORICAL,
            confidence_level=confidence_level,
            time_horizon=time_horizon,
            volatility=volatility,
            skewness=skewness,
            kurtosis=kurtosis,
            model_parameters={
                'observations': len(returns),
                'method': 'historical_simulation'
            }
        )
    
    def _calculate_parametric_var(self, returns: np.ndarray, confidence_level: float,
                                time_horizon: int, distribution: str) -> VaRResult:
        """Calculate Parametric VaR (Normal or t-distribution)"""
        # Calculate basic statistics
        mean_return = np.mean(returns)
        volatility = np.std(returns)
        
        # Scale for time horizon
        scaled_mean = mean_return * time_horizon
        scaled_volatility = volatility * np.sqrt(time_horizon)
        
        if distribution == 'normal':
            # Normal distribution VaR
            z_score = stats.norm.ppf(1 - confidence_level) if SCIPY_AVAILABLE else -1.645
            var = scaled_mean + z_score * scaled_volatility
            
            # CVaR for normal distribution
            phi = stats.norm.pdf(z_score) if SCIPY_AVAILABLE else 0.1
            cvar = scaled_mean - scaled_volatility * phi / (1 - confidence_level)
            
            model_params = {
                'distribution': 'normal',
                'mean': mean_return,
                'volatility': volatility,
                'z_score': z_score
            }
            
        else:  # t-distribution
            if not SCIPY_AVAILABLE:
                # Fallback to normal if scipy not available
                return self._calculate_parametric_var(returns, confidence_level, time_horizon, 'normal')
            
            # Fit t-distribution
            df, loc, scale = stats.t.fit(returns)
            
            # t-distribution VaR
            t_score = stats.t.ppf(1 - confidence_level, df)
            var = scaled_mean + t_score * scaled_volatility
            
            # CVaR for t-distribution (approximation)
            pdf_value = stats.t.pdf(t_score, df)
            cvar = scaled_mean - scaled_volatility * pdf_value / (1 - confidence_level)
            
            model_params = {
                'distribution': 't',
                'degrees_of_freedom': df,
                'location': loc,
                'scale': scale,
                't_score': t_score
            }
        
        # Calculate distribution statistics
        skewness = stats.skew(returns) if SCIPY_AVAILABLE else 0.0
        kurtosis = stats.kurtosis(returns) if SCIPY_AVAILABLE else 0.0
        
        return VaRResult(
            var=var,
            cvar=cvar,
            method=VaRMethod.PARAMETRIC_T if distribution == 't' else VaRMethod.PARAMETRIC_NORMAL,
            confidence_level=confidence_level,
            time_horizon=time_horizon,
            volatility=scaled_volatility,
            skewness=skewness,
            kurtosis=kurtosis,
            model_parameters=model_params
        )
    
    def _calculate_monte_carlo_var(self, returns: np.ndarray, confidence_level: float,
                                 time_horizon: int) -> VaRResult:
        """Calculate Monte Carlo VaR"""
        # Estimate parameters
        mean_return = np.mean(returns)
        volatility = np.std(returns)
        
        # Generate random scenarios
        num_simulations = self.config['monte_carlo_simulations']
        
        # Use normal distribution for simplicity (can be enhanced with other distributions)
        simulated_returns = np.random.normal(
            mean_return * time_horizon,
            volatility * np.sqrt(time_horizon),
            num_simulations
        )
        
        # Calculate VaR and CVaR
        var = np.percentile(simulated_returns, (1 - confidence_level) * 100)
        cvar = np.mean(simulated_returns[simulated_returns <= var])
        
        # Calculate statistics
        sim_volatility = np.std(simulated_returns)
        skewness = stats.skew(simulated_returns) if SCIPY_AVAILABLE else 0.0
        kurtosis = stats.kurtosis(simulated_returns) if SCIPY_AVAILABLE else 0.0
        
        return VaRResult(
            var=var,
            cvar=cvar,
            method=VaRMethod.MONTE_CARLO,
            confidence_level=confidence_level,
            time_horizon=time_horizon,
            volatility=sim_volatility,
            skewness=skewness,
            kurtosis=kurtosis,
            model_parameters={
                'simulations': num_simulations,
                'mean_return': mean_return,
                'volatility': volatility,
                'distribution': 'normal'
            }
        )
    
    def _calculate_garch_var(self, returns: np.ndarray, confidence_level: float,
                           time_horizon: int) -> VaRResult:
        """Calculate GARCH-based VaR"""
        if not ARCH_AVAILABLE:
            self.logger.warning("ARCH package not available, falling back to parametric VaR")
            return self._calculate_parametric_var(returns, confidence_level, time_horizon, 'normal')
        
        try:
            # Fit GARCH model
            model = arch_model(returns * 100, vol='Garch', p=self.config['garch_p'], q=self.config['garch_q'])
            
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                fitted_model = model.fit(disp='off')
            
            # Forecast volatility
            forecast = fitted_model.forecast(horizon=time_horizon)
            forecasted_variance = forecast.variance.iloc[-1, :].sum()
            forecasted_volatility = np.sqrt(forecasted_variance) / 100  # Convert back from percentage
            
            # Calculate VaR using forecasted volatility
            mean_return = np.mean(returns)
            z_score = stats.norm.ppf(1 - confidence_level) if SCIPY_AVAILABLE else -1.645
            
            var = mean_return * time_horizon + z_score * forecasted_volatility
            
            # CVaR calculation
            phi = stats.norm.pdf(z_score) if SCIPY_AVAILABLE else 0.1
            cvar = mean_return * time_horizon - forecasted_volatility * phi / (1 - confidence_level)
            
            # Model parameters
            model_params = {
                'model_type': 'GARCH',
                'p': self.config['garch_p'],
                'q': self.config['garch_q'],
                'forecasted_volatility': forecasted_volatility,
                'aic': fitted_model.aic,
                'bic': fitted_model.bic
            }
            
            return VaRResult(
                var=var,
                cvar=cvar,
                method=VaRMethod.GARCH,
                confidence_level=confidence_level,
                time_horizon=time_horizon,
                volatility=forecasted_volatility,
                skewness=stats.skew(returns) if SCIPY_AVAILABLE else 0.0,
                kurtosis=stats.kurtosis(returns) if SCIPY_AVAILABLE else 0.0,
                model_parameters=model_params
            )
            
        except Exception as e:
            self.logger.warning(f"GARCH VaR calculation failed: {e}")
            return self._calculate_parametric_var(returns, confidence_level, time_horizon, 'normal')

    def _calculate_extreme_value_var(self, returns: np.ndarray, confidence_level: float,
                                   time_horizon: int) -> VaRResult:
        """Calculate Extreme Value Theory VaR"""
        if not SCIPY_AVAILABLE:
            self.logger.warning("SciPy not available, falling back to historical VaR")
            return self._calculate_historical_var(returns, confidence_level, time_horizon)

        try:
            # Define threshold (e.g., 90th percentile)
            threshold_percentile = self.config['extreme_value_threshold']
            threshold = np.percentile(returns, threshold_percentile * 100)

            # Extract exceedances
            exceedances = returns[returns > threshold] - threshold

            if len(exceedances) < 10:  # Need sufficient exceedances
                self.logger.warning("Insufficient exceedances for EVT, falling back to historical VaR")
                return self._calculate_historical_var(returns, confidence_level, time_horizon)

            # Fit Generalized Pareto Distribution
            shape, loc, scale = stats.genpareto.fit(exceedances, floc=0)

            # Calculate VaR using EVT
            n = len(returns)
            n_exceedances = len(exceedances)

            # VaR calculation
            if confidence_level > threshold_percentile:
                # Use EVT for extreme quantiles
                p = (confidence_level - threshold_percentile) / (1 - threshold_percentile)
                var_exceedance = stats.genpareto.ppf(p, shape, loc=0, scale=scale)
                var = threshold + var_exceedance
            else:
                # Use empirical quantile for non-extreme levels
                var = np.percentile(returns, confidence_level * 100)

            # Scale for time horizon
            var = var * np.sqrt(time_horizon)

            # CVaR calculation (approximation)
            if shape < 1:
                cvar = var + scale / (1 - shape) * (1 - confidence_level) ** (-shape)
            else:
                cvar = var * 1.2  # Simple approximation

            model_params = {
                'threshold': threshold,
                'threshold_percentile': threshold_percentile,
                'shape_parameter': shape,
                'scale_parameter': scale,
                'n_exceedances': n_exceedances
            }

            return VaRResult(
                var=var,
                cvar=cvar,
                method=VaRMethod.EXTREME_VALUE,
                confidence_level=confidence_level,
                time_horizon=time_horizon,
                volatility=np.std(returns) * np.sqrt(time_horizon),
                skewness=stats.skew(returns),
                kurtosis=stats.kurtosis(returns),
                model_parameters=model_params
            )

        except Exception as e:
            self.logger.warning(f"EVT VaR calculation failed: {e}")
            return self._calculate_historical_var(returns, confidence_level, time_horizon)

    def calculate_multiple_vars(self, returns: Union[List[float], np.ndarray],
                              methods: Optional[List[VaRMethod]] = None,
                              confidence_levels: Optional[List[float]] = None,
                              time_horizon: int = 1) -> Dict[str, VaRResult]:
        """Calculate VaR using multiple methods and confidence levels"""
        if methods is None:
            methods = [VaRMethod.HISTORICAL, VaRMethod.PARAMETRIC_NORMAL, VaRMethod.MONTE_CARLO]

        if confidence_levels is None:
            confidence_levels = self.config['default_confidence_levels']

        results = {}

        for method in methods:
            for confidence_level in confidence_levels:
                key = f"{method.value}_{confidence_level:.0%}"
                results[key] = self.calculate_var(returns, method, confidence_level, time_horizon)

        return results

    def _create_fallback_var_result(self, confidence_level: float, time_horizon: int) -> VaRResult:
        """Create fallback VaR result when calculation fails"""
        # Conservative estimates
        fallback_var = -0.05 * np.sqrt(time_horizon)  # 5% daily VaR
        fallback_cvar = fallback_var * 1.3  # CVaR typically 30% worse than VaR

        return VaRResult(
            var=fallback_var,
            cvar=fallback_cvar,
            method=VaRMethod.HISTORICAL,
            confidence_level=confidence_level,
            time_horizon=time_horizon,
            volatility=0.20,  # 20% annual volatility
            skewness=0.0,
            kurtosis=0.0,
            model_parameters={'fallback': True}
        )
