import argparse
import os
try:
    import gymnasium as gym
    from gymnasium import spaces
    GYM_VERSION = "gymnasium"
except ImportError:
    try:
        import gym
        from gym import spaces
        GYM_VERSION = "gym"
    except ImportError:
        raise ImportError("Neither gymnasium nor gym is available. Please install one of them.")
import random
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from stable_baselines3 import SAC, PPO, A2C
from stable_baselines3.common.env_checker import check_env
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import EvalCallback, StopTrainingOnRewardThreshold
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.noise import NormalActionNoise
from stable_baselines3.common.policies import ActorCriticPolicy
from stable_baselines3.sac.policies import SACPolicy
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# 直接使用模拟数据，避免CZSC依赖问题
CZSC_AVAILABLE = False
print("使用模拟数据，避免依赖问题")

# =================== 配置参数 ===================
# 归一化参数
MAX_ACCOUNT_BALANCE = 2147480
MAX_NUM_SHARES = 2147480
MAX_SHARE_PRICE = 5000
MAX_VOLUME = 1e9
MAX_AMOUNT = 1e10
MAX_OPEN_POSITIONS = 5
MAX_STEPS = 1000
MAX_DAY_CHANGE = 1
INITIAL_ACCOUNT_BALANCE = 100000

# 训练参数
SEED = 0
WARMUP_STEPS = 1000
EVAL_EPISODES = 5
MEMORY_SIZE = int(1e5)
BATCH_SIZE = 64
GAMMA = 0.995
TAU = 0.005
ACTOR_LR = 1e-4
CRITIC_LR = 1e-4
ALPHA = 0.2
# =================== 状态空间管理器 ===================
class StateSpaceManager:
    """增强的状态空间管理器 - 50维复合指标"""
    
    def __init__(self):
        self.state_dim = 50
        self.feature_names = self._define_feature_names()
        
    def _define_feature_names(self) -> List[str]:
        """定义50维特征名称"""
        features = []
        
        # 基础价格特征 (6维)
        features.extend(['open_norm', 'high_norm', 'low_norm', 'close_norm', 'volume_norm', 'amount_norm'])
        
        # 流动性指标 (5维)
        features.extend(['volume_ratio', 'turnover_rate', 'price_impact', 'bid_ask_spread', 'market_depth'])
        
        # 趋势指标 (8维)
        features.extend(['sma_5', 'sma_20', 'ema_12', 'ema_26', 'macd', 'macd_signal', 'macd_hist', 'adx'])
        
        # 结构指标 (5维)
        features.extend(['support_strength', 'resistance_strength', 'fibonacci_level', 'pivot_point', 'channel_position'])
        
        # 情绪指标 (6维)
        features.extend(['rsi', 'stoch_k', 'stoch_d', 'williams_r', 'cci', 'sentiment_score'])
        
        # 波动性指标 (5维)
        features.extend(['atr', 'bollinger_upper', 'bollinger_lower', 'volatility', 'garch_vol'])
        
        # 转换性指标 (5维)
        features.extend(['momentum', 'roc', 'acceleration', 'regime_change', 'cycle_position'])
        
        # 账户状态 (7维)
        features.extend(['balance_norm', 'net_worth_norm', 'shares_held_norm', 'cost_basis_norm', 
                        'total_sold_norm', 'total_sales_norm', 'unrealized_pnl'])
        
        # 风险指标 (3维)
        features.extend(['var_estimate', 'sharpe_ratio', 'max_drawdown'])
        
        return features
    
    def extract_features(self, df: pd.DataFrame, current_step: int, account_state: Dict) -> np.ndarray:
        """提取50维特征向量"""
        features = np.zeros(self.state_dim)
        
        try:
            # 基础价格特征 (6维)
            features[0] = df.loc[current_step, 'open'] / MAX_SHARE_PRICE
            features[1] = df.loc[current_step, 'high'] / MAX_SHARE_PRICE  
            features[2] = df.loc[current_step, 'low'] / MAX_SHARE_PRICE
            features[3] = df.loc[current_step, 'close'] / MAX_SHARE_PRICE
            features[4] = df.loc[current_step, 'volume'] / MAX_VOLUME
            features[5] = df.loc[current_step, 'amount'] / MAX_AMOUNT
            
            # 计算技术指标
            features[6:] = self._calculate_technical_indicators(df, current_step, account_state)
            
        except (KeyError, IndexError) as e:
            # 如果数据不完整，填充默认值
            print(f"Warning: Feature extraction error: {e}")
            features = np.random.random(self.state_dim) * 0.1
            
        return np.clip(features, 0, 1).astype(np.float32)    
 
    def _calculate_technical_indicators(self, df: pd.DataFrame, current_step: int, account_state: Dict) -> np.ndarray:
        """计算技术指标 (44维)"""
        indicators = np.zeros(44)
        
        if current_step < 26:  # 不足计算周期时使用默认值
            return indicators
            
        try:
            close_prices = df['close'].iloc[max(0, current_step-26):current_step+1].values
            high_prices = df['high'].iloc[max(0, current_step-26):current_step+1].values
            low_prices = df['low'].iloc[max(0, current_step-26):current_step+1].values
            volumes = df['volume'].iloc[max(0, current_step-26):current_step+1].values
            
            # 流动性指标 (5维)
            indicators[0] = self._safe_calc(lambda: volumes[-1] / np.mean(volumes[-5:]) if len(volumes) >= 5 else 0.5)
            indicators[1] = self._safe_calc(lambda: min(volumes[-1] / 1e6, 1.0))
            indicators[2] = self._safe_calc(lambda: abs(close_prices[-1] - close_prices[-2]) / close_prices[-2] if len(close_prices) >= 2 else 0)
            indicators[3] = self._safe_calc(lambda: (high_prices[-1] - low_prices[-1]) / close_prices[-1])
            indicators[4] = 0.5  # 市场深度（简化处理）
            
            # 趋势指标 (8维)
            if len(close_prices) >= 20:
                sma_5 = np.mean(close_prices[-5:])
                sma_20 = np.mean(close_prices[-20:])
                ema_12 = self._calculate_ema(close_prices, 12)
                ema_26 = self._calculate_ema(close_prices, 26)
                
                indicators[5] = sma_5 / close_prices[-1]
                indicators[6] = sma_20 / close_prices[-1]
                indicators[7] = ema_12 / close_prices[-1]
                indicators[8] = ema_26 / close_prices[-1]
                
                # MACD
                macd = ema_12 - ema_26
                signal = self._calculate_ema([macd], 9)
                indicators[9] = (macd / close_prices[-1]) + 0.5
                indicators[10] = (signal / close_prices[-1]) + 0.5
                indicators[11] = ((macd - signal) / close_prices[-1]) + 0.5
                
                # ADX (简化版)
                indicators[12] = 0.5
                
            # 结构指标 (5维) - 简化实现
            indicators[13:18] = 0.5
            
            # 情绪指标 (6维)
            if len(close_prices) >= 14:
                rsi = self._calculate_rsi(close_prices, 14)
                indicators[18] = rsi / 100
                
                # 随机指标 (简化)
                indicators[19] = 0.5
                indicators[20] = 0.5
                indicators[21] = 0.5
                indicators[22] = 0.5
                indicators[23] = 0.5  # 情绪分数
                
            # 波动性指标 (5维)
            if len(high_prices) >= 14:
                atr = self._calculate_atr(high_prices, low_prices, close_prices, 14)
                indicators[24] = min(atr / close_prices[-1], 1.0)
                
                # 布林带
                bb_std = np.std(close_prices[-20:]) if len(close_prices) >= 20 else 0
                bb_middle = np.mean(close_prices[-20:]) if len(close_prices) >= 20 else close_prices[-1]
                indicators[25] = min((bb_middle + 2*bb_std) / close_prices[-1], 2.0) - 1.0
                indicators[26] = max((bb_middle - 2*bb_std) / close_prices[-1], 0.0)
                
                indicators[27] = min(bb_std / close_prices[-1], 1.0)
                indicators[28] = 0.5  # GARCH波动率
                
            # 转换性指标 (5维)
            if len(close_prices) >= 10:
                momentum = (close_prices[-1] / close_prices[-10] - 1) + 0.5
                indicators[29] = np.clip(momentum, 0, 1)
                
                roc = (close_prices[-1] / close_prices[-5] - 1) + 0.5 if len(close_prices) >= 5 else 0.5
                indicators[30] = np.clip(roc, 0, 1)
                
                indicators[31] = 0.5  # 加速度
                indicators[32] = 0.5  # 制度变化
                indicators[33] = 0.5  # 周期位置
                
            # 账户状态 (7维)
            indicators[34] = account_state['balance'] / MAX_ACCOUNT_BALANCE
            indicators[35] = account_state['net_worth'] / MAX_ACCOUNT_BALANCE
            indicators[36] = account_state['shares_held'] / MAX_NUM_SHARES
            indicators[37] = account_state['cost_basis'] / MAX_SHARE_PRICE
            indicators[38] = account_state['total_shares_sold'] / MAX_NUM_SHARES
            indicators[39] = account_state['total_sales_value'] / (MAX_NUM_SHARES * MAX_SHARE_PRICE)
            
            # 未实现盈亏
            if account_state['shares_held'] > 0:
                unrealized_pnl = (close_prices[-1] - account_state['cost_basis']) * account_state['shares_held']
                indicators[40] = (unrealized_pnl / INITIAL_ACCOUNT_BALANCE) + 0.5
            else:
                indicators[40] = 0.5
                
            # 风险指标 (3维) - 简化实现
            indicators[41] = 0.5  # VaR估计
            indicators[42] = 0.5  # 夏普比率
            indicators[43] = 0.5  # 最大回撤
            
        except Exception as e:
            print(f"Technical indicator calculation error: {e}")
            
        return np.clip(indicators, 0, 1)
    
    def _safe_calc(self, func) -> float:
        """安全计算，避免除零等错误"""
        try:
            result = func()
            return np.clip(result, 0, 1) if not np.isnan(result) else 0.5
        except:
            return 0.5
            
    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算EMA"""
        if len(prices) < period:
            return prices[-1]
        alpha = 2.0 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        return ema
        
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0
            
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
        
    def _calculate_atr(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> float:
        """计算ATR"""
        if len(high) < period + 1:
            return (high[-1] - low[-1])
            
        tr_list = []
        for i in range(1, len(high)):
            tr = max(high[i] - low[i], 
                    abs(high[i] - close[i-1]),
                    abs(low[i] - close[i-1]))
            tr_list.append(tr)
            
        return np.mean(tr_list[-period:])
# =================== 智能动作空间 ===================
class SmartActionSpace:
    """智能动作空间 - 多维决策系统"""
    
    def __init__(self):
        # 动作维度：[交易信号, 仓位比例, 止损位, 止盈位]
        self.action_space = spaces.Box(
            low=np.array([-1.0, 0.0, -0.1, 0.0]), 
            high=np.array([1.0, 1.0, 0.0, 0.2]), 
            dtype=np.float32
        )
        self.action_dim = 4
        
    def parse_action(self, raw_action: np.ndarray) -> Dict[str, float]:
        """解析原始动作为具体交易指令"""
        action = np.clip(raw_action, self.action_space.low, self.action_space.high)
        
        # 交易信号：-1(卖出), 0(持有), 1(买入)
        signal_raw = action[0]
        if signal_raw < -0.33:
            trade_signal = 'sell'
        elif signal_raw > 0.33:
            trade_signal = 'buy'
        else:
            trade_signal = 'hold'
            
        return {
            'trade_signal': trade_signal,
            'position_ratio': float(action[1]),      # 仓位比例 0-1
            'stop_loss': float(action[2]),           # 止损位 -10% to 0%
            'take_profit': float(action[3]),         # 止盈位 0% to 20%
            'signal_strength': abs(float(action[0])) # 信号强度
        }

# =================== 异常检测系统 ===================
class AnomalyDetector:
    """市场异常检测器"""
    
    def __init__(self, state_dim: int = 50):
        self.state_dim = state_dim
        self.threshold = 0.1
        self.history_buffer = []
        self.max_history = 100
        
    def detect_anomaly(self, state_vector: np.ndarray) -> Dict[str, Any]:
        """检测状态异常"""
        # 更新历史缓冲
        self.history_buffer.append(state_vector)
        if len(self.history_buffer) > self.max_history:
            self.history_buffer.pop(0)
            
        if len(self.history_buffer) < 10:
            return {'is_anomaly': False, 'anomaly_score': 0.0, 'risk_level': 'low'}
            
        # 简化的异常检测 - 基于统计距离
        recent_states = np.array(self.history_buffer[-10:])
        mean_state = np.mean(recent_states, axis=0)
        std_state = np.std(recent_states, axis=0) + 1e-6
        
        # 计算标准化距离
        normalized_distance = np.mean(np.abs((state_vector - mean_state) / std_state))
        
        is_anomaly = normalized_distance > self.threshold
        risk_level = self._assess_risk_level(normalized_distance)
        
        return {
            'is_anomaly': is_anomaly,
            'anomaly_score': float(normalized_distance),
            'risk_level': risk_level,
            'confidence': min(len(self.history_buffer) / self.max_history, 1.0)
        }
        
    def _assess_risk_level(self, score: float) -> str:
        """评估风险等级"""
        if score < 0.05:
            return 'low'
        elif score < 0.1:
            return 'medium'
        elif score < 0.2:
            return 'high'
        else:
            return 'critical'

# =================== 决策解释器 ===================
class DecisionExplainer:
    """决策解释器 - 提供透明的AI决策reasoning"""
    
    def __init__(self, feature_names: List[str]):
        self.feature_names = feature_names
        
    def explain_decision(self, state: np.ndarray, action: Dict, confidence: float) -> Dict[str, Any]:
        """生成决策解释"""
        
        # 识别关键特征（简化版本）
        key_features = self._identify_key_features(state)
        
        # 生成决策reasoning
        reasoning = self._generate_reasoning(action, key_features, confidence)
        
        # 风险评估
        risk_assessment = self._assess_decision_risk(state, action)
        
        return {
            'decision_reasoning': reasoning,
            'confidence_level': confidence,
            'key_features': key_features,
            'risk_assessment': risk_assessment,
            'action_summary': self._summarize_action(action)
        }
        
    def _identify_key_features(self, state: np.ndarray, top_k: int = 5) -> List[Dict]:
        """识别对决策最重要的特征"""
        # 简化实现：选择数值最极端的特征
        importance_scores = np.abs(state - 0.5)  # 距离中性值的距离
        top_indices = np.argsort(importance_scores)[-top_k:]
        
        key_features = []
        for idx in reversed(top_indices):
            key_features.append({
                'feature': self.feature_names[idx],
                'value': float(state[idx]),
                'importance': float(importance_scores[idx]),
                'interpretation': self._interpret_feature(self.feature_names[idx], state[idx])
            })
            
        return key_features
        
    def _interpret_feature(self, feature_name: str, value: float) -> str:
        """解释单个特征的含义"""
        if value > 0.7:
            return f"{feature_name} 处于高位"
        elif value < 0.3:
            return f"{feature_name} 处于低位"
        else:
            return f"{feature_name} 处于中性区间"
            
    def _generate_reasoning(self, action: Dict, key_features: List, confidence: float) -> str:
        """生成自然语言解释"""
        signal = action['trade_signal']
        strength = action['signal_strength']
        
        reasoning = f"基于当前市场状态分析，系统建议执行 {signal} 操作，"
        reasoning += f"信号强度为 {strength:.2f}，置信度为 {confidence:.2f}。\n"
        
        reasoning += "主要决策依据包括：\n"
        for feature in key_features[:3]:
            reasoning += f"- {feature['interpretation']} (重要性: {feature['importance']:.2f})\n"
            
        if action['trade_signal'] != 'hold':
            reasoning += f"建议仓位比例: {action['position_ratio']:.1%}"
            
        return reasoning
        
    def _assess_decision_risk(self, state: np.ndarray, action: Dict) -> Dict[str, Any]:
        """评估决策风险"""
        # 简化的风险评估
        volatility_features = state[24:29]  # 波动性指标
        avg_volatility = np.mean(volatility_features)
        
        risk_score = avg_volatility * action['signal_strength']
        
        if risk_score < 0.3:
            risk_level = 'low'
        elif risk_score < 0.6:
            risk_level = 'medium'
        else:
            risk_level = 'high'
            
        return {
            'risk_level': risk_level,
            'risk_score': float(risk_score),
            'volatility_indicator': float(avg_volatility),
            'position_risk': action['position_ratio'] * risk_score
        }
        
    def _summarize_action(self, action: Dict) -> str:
        """总结动作"""
        signal = action['trade_signal']
        if signal == 'buy':
            return f"买入信号，建议仓位 {action['position_ratio']:.1%}"
        elif signal == 'sell':
            return f"卖出信号，建议仓位调整 {action['position_ratio']:.1%}"
        else:
            return "持有信号，维持当前仓位"

# =================== 风险管理器 ===================
class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        self.max_position = 0.95  # 最大仓位
        self.max_single_loss = 0.05  # 单次最大损失
        self.max_daily_trades = 10
        self.daily_trade_count = 0
        
    def validate_action(self, action: Dict, current_state: Dict, anomaly_info: Dict) -> Dict[str, Any]:
        """验证动作是否符合风险管理规则"""
        
        risks = []
        is_valid = True
        adjusted_action = action.copy()
        
        # 检查异常情况
        if anomaly_info['is_anomaly'] and anomaly_info['risk_level'] in ['high', 'critical']:
            risks.append("检测到市场异常，建议降低仓位")
            adjusted_action['position_ratio'] *= 0.5
            
        # 仓位限制
        if action['position_ratio'] > self.max_position:
            risks.append(f"仓位超限，调整至 {self.max_position:.1%}")
            adjusted_action['position_ratio'] = self.max_position
            
        # 交易频率限制
        if self.daily_trade_count >= self.max_daily_trades:
            risks.append("日交易次数达到上限")
            adjusted_action['trade_signal'] = 'hold'
            is_valid = False
            
        # 止损检查
        current_price = current_state.get('current_price', 0)
        if current_state.get('shares_held', 0) > 0 and current_price > 0:
            unrealized_loss = (current_state['cost_basis'] - current_price) / current_state['cost_basis']
            if unrealized_loss > self.max_single_loss:
                risks.append(f"未实现损失超过 {self.max_single_loss:.1%}，建议止损")
                adjusted_action['trade_signal'] = 'sell'
                adjusted_action['position_ratio'] = 1.0
                
        return {
            'is_valid': is_valid,
            'adjusted_action': adjusted_action,
            'risk_warnings': risks,
            'risk_score': len(risks) / 5.0  # 归一化风险分数
        }
        
    def reset_daily_counter(self):
        """重置日交易计数器"""
        self.daily_trade_count = 0
        
    def record_trade(self):
        """记录交易"""
        self.daily_trade_count += 1

# =================== 增强交易环境 ===================
class EnhancedStockTradingEnv(gym.Env):
    """增强版股票交易环境 - 兼容Stable-Baselines3"""
    
    def __init__(self, df: pd.DataFrame):
        super(EnhancedStockTradingEnv, self).__init__()
        
        self.df = df
        self.current_step = 0
        
        # 初始化各个组件
        self.state_manager = StateSpaceManager()
        self.action_parser = SmartActionSpace()
        self.anomaly_detector = AnomalyDetector()
        self.explainer = DecisionExplainer(self.state_manager.feature_names)
        self.risk_manager = RiskManager()
        
        # 环境空间定义
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(self.state_manager.state_dim,), dtype=np.float32
        )
        self.action_space = self.action_parser.action_space
        
        # 账户状态
        self.reset()
        
    def reset(self, seed=None, options=None) -> np.ndarray:
        """重置环境 - SB3兼容版本"""
        super().reset(seed=seed)
        
        # 重置账户状态
        self.balance = INITIAL_ACCOUNT_BALANCE
        self.net_worth = INITIAL_ACCOUNT_BALANCE
        self.max_net_worth = INITIAL_ACCOUNT_BALANCE
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0
        self.current_step = 0
        
        # 重置组件
        self.anomaly_detector = AnomalyDetector()
        self.risk_manager.reset_daily_counter()
        
        observation = self._get_observation()
        info = self._get_info()
        
        return observation, info
        
    def step(self, action: np.ndarray) -> Tuple[np.ndarray, float, bool, bool, Dict]:
        """执行动作 - SB3兼容版本"""
        # 解析动作
        parsed_action = self.action_parser.parse_action(action)
        
        # 异常检测
        current_obs = self._get_observation()
        anomaly_info = self.anomaly_detector.detect_anomaly(current_obs)
        
        # 风险管理验证
        current_state = self._get_current_state()
        risk_validation = self.risk_manager.validate_action(
            parsed_action, current_state, anomaly_info
        )
        
        # 使用调整后的动作
        final_action = risk_validation['adjusted_action']
        
        # 执行交易
        reward = self._execute_trade(final_action)
        
        # 更新状态
        self.current_step += 1
        done = self._check_done()
        truncated = False  # SB3新增的截断标志
        
        # 生成决策解释
        explanation = self.explainer.explain_decision(
            current_obs, final_action, 
            1.0 - risk_validation['risk_score']
        )
        
        # 构建info
        info = {
            'profit': self.net_worth - INITIAL_ACCOUNT_BALANCE,
            'current_step': self.current_step,
            'parsed_action': parsed_action,
            'final_action': final_action,
            'anomaly_info': anomaly_info,
            'risk_validation': risk_validation,
            'explanation': explanation,
            'account_state': self._get_current_state()
        }
        
        next_obs = self._get_observation()
        return next_obs, reward, done, truncated, info 
       
    def _get_observation(self) -> np.ndarray:
        """获取当前观测"""
        if self.current_step >= len(self.df):
            self.current_step = len(self.df) - 1
            
        account_state = {
            'balance': self.balance,
            'net_worth': self.net_worth,
            'shares_held': self.shares_held,
            'cost_basis': self.cost_basis,
            'total_shares_sold': self.total_shares_sold,
            'total_sales_value': self.total_sales_value
        }
        
        return self.state_manager.extract_features(self.df, self.current_step, account_state)
        
    def _get_info(self) -> Dict:
        """获取环境信息"""
        return {
            'profit': self.net_worth - INITIAL_ACCOUNT_BALANCE,
            'current_step': self.current_step,
            'account_state': self._get_current_state()
        }
        
    def _get_current_state(self) -> Dict:
        """获取当前完整状态"""
        current_price = self.df.loc[self.current_step, 'close'] if self.current_step < len(self.df) else 0
        
        return {
            'balance': self.balance,
            'net_worth': self.net_worth,
            'shares_held': self.shares_held,
            'cost_basis': self.cost_basis,
            'total_shares_sold': self.total_shares_sold,
            'total_sales_value': self.total_sales_value,
            'current_price': current_price,
            'current_step': self.current_step
        }
        
    def _execute_trade(self, action: Dict) -> float:
        """执行交易并计算奖励"""
        if self.current_step >= len(self.df):
            return 0
            
        current_price = random.uniform(
            self.df.loc[self.current_step, "low"], 
            self.df.loc[self.current_step, "high"]
        )
        
        initial_net_worth = self.net_worth
        
        if action['trade_signal'] == 'buy' and self.balance >= current_price:
            # 买入逻辑
            max_shares = int(self.balance / current_price)
            shares_to_buy = int(max_shares * action['position_ratio'])
            
            if shares_to_buy > 0:
                cost = shares_to_buy * current_price
                self.balance -= cost
                
                # 更新成本基础
                if self.shares_held > 0:
                    total_cost = self.cost_basis * self.shares_held + cost
                    self.shares_held += shares_to_buy
                    self.cost_basis = total_cost / self.shares_held
                else:
                    self.shares_held = shares_to_buy
                    self.cost_basis = current_price
                    
                self.risk_manager.record_trade()
                
        elif action['trade_signal'] == 'sell' and self.shares_held > 0:
            # 卖出逻辑
            shares_to_sell = int(self.shares_held * action['position_ratio'])
            
            if shares_to_sell > 0:
                revenue = shares_to_sell * current_price
                self.balance += revenue
                self.shares_held -= shares_to_sell
                self.total_shares_sold += shares_to_sell
                self.total_sales_value += revenue
                
                if self.shares_held == 0:
                    self.cost_basis = 0
                    
                self.risk_manager.record_trade()
        
        # 更新净资产
        self.net_worth = self.balance + self.shares_held * current_price
        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth
            
        # 计算奖励
        return self._calculate_reward(initial_net_worth, action)
        
    def _calculate_reward(self, initial_net_worth: float, action: Dict) -> float:
        """计算增强奖励函数"""
        # 基础收益奖励
        profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
        profit_rate = profit / INITIAL_ACCOUNT_BALANCE
        
        # 收益奖励 (主要部分)
        if profit_rate > 0:
            reward = profit_rate * 10  # 放大正收益
        else:
            reward = profit_rate * 5   # 减少负收益惩罚
            
        # 风险调整
        if self.shares_held > 0:
            current_price = self.df.loc[self.current_step, 'close']
            unrealized_loss_rate = (self.cost_basis - current_price) / self.cost_basis
            if unrealized_loss_rate > 0.1:  # 超过10%损失
                reward -= unrealized_loss_rate * 2
                
        # 交易频率惩罚
        if action['trade_signal'] != 'hold':
            reward -= 0.001  # 小幅交易成本
            
        # 成功奖励
        if profit_rate > 0.5:  # 超过50%收益
            reward += 5
            
        return reward
        
    def _check_done(self) -> bool:
        """检查是否结束"""
        if self.current_step >= len(self.df) - 1:
            return True
        if self.net_worth <= INITIAL_ACCOUNT_BALANCE * 0.1:  # 损失90%
            return True
        if self.net_worth >= INITIAL_ACCOUNT_BALANCE * 3:  # 收益300%
            return True
        return False
        
    def render(self, mode: str = 'human') -> None:
        """渲染环境信息"""
        profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
        profit_rate = profit / INITIAL_ACCOUNT_BALANCE
        
        print('-' * 50)
        print(f'Step: {self.current_step}')
        print(f'Balance: ${self.balance:,.2f}')
        print(f'Shares held: {self.shares_held}')
        print(f'Cost basis: ${self.cost_basis:.2f}')
        print(f'Net worth: ${self.net_worth:,.2f}')
        print(f'Profit: ${profit:,.2f} ({profit_rate:.2%})')
        print(f'Max net worth: ${self.max_net_worth:,.2f}')
        print('-' * 50)

# =================== SB3训练函数 ===================
def run_evaluate_episodes(model, env: EnhancedStockTradingEnv, 
                         eval_episodes: int = 5) -> Dict[str, float]:
    """运行评估回合 - SB3版本"""
    total_reward = 0.0
    total_profit = 0.0
    successful_episodes = 0
    
    for episode in range(eval_episodes):
        obs, _ = env.reset()
        done = False
        episode_reward = 0.0
        
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, done, truncated, info = env.step(action)
            episode_reward += reward
            done = done or truncated
            
        total_reward += episode_reward
        profit = info['profit']
        total_profit += profit
        
        if profit > 0:
            successful_episodes += 1
            
        print(f"Episode {episode + 1}: Reward={episode_reward:.3f}, "
              f"Profit=${profit:,.2f}, Final Worth=${info['account_state']['net_worth']:,.2f}")
              
    avg_reward = total_reward / eval_episodes
    avg_profit = total_profit / eval_episodes
    success_rate = successful_episodes / eval_episodes
    
    print(f"\n=== Evaluation Results ===")
    print(f"Average Reward: {avg_reward:.3f}")
    print(f"Average Profit: ${avg_profit:,.2f}")
    print(f"Success Rate: {success_rate:.1%}")
    print(f"=========================\n")
    
    return {
        'avg_reward': avg_reward,
        'avg_profit': avg_profit,
        'success_rate': success_rate
    }

def train_enhanced_agent_sb3(train_df: pd.DataFrame, eval_df: pd.DataFrame, 
                            algorithm_name: str = 'SAC', total_timesteps: int = 100000):
    """训练增强代理 - SB3版本"""
    print(f"开始训练增强版{algorithm_name}代理...")
    
    # 创建环境
    train_env = EnhancedStockTradingEnv(train_df)
    eval_env = EnhancedStockTradingEnv(eval_df)
    
    # 检查环境
    check_env(train_env)
    print("环境检查通过!")
    
    # 包装环境
    train_env = Monitor(train_env)
    eval_env = Monitor(eval_env)
    
    # 创建向量化环境
    train_env = DummyVecEnv([lambda: train_env])
    eval_env = DummyVecEnv([lambda: eval_env])
    
    # 创建模型
    if algorithm_name == 'SAC':
        model = SAC(
            'MlpPolicy',
            train_env,
            learning_rate=ACTOR_LR,
            buffer_size=MEMORY_SIZE,
            batch_size=BATCH_SIZE,
            gamma=GAMMA,
            tau=TAU,
            ent_coef=ALPHA,
            verbose=1,
            tensorboard_log="./tensorboard_logs/",
            seed=SEED
        )
    elif algorithm_name == 'PPO':
        model = PPO(
            'MlpPolicy',
            train_env,
            learning_rate=ACTOR_LR,
            gamma=GAMMA,
            verbose=1,
            tensorboard_log="./tensorboard_logs/",
            seed=SEED
        )
    elif algorithm_name == 'A2C':
        model = A2C(
            'MlpPolicy',
            train_env,
            learning_rate=ACTOR_LR,
            gamma=GAMMA,
            verbose=1,
            tensorboard_log="./tensorboard_logs/",
            seed=SEED
        )
    else:
        raise ValueError(f"Unsupported algorithm: {algorithm_name}")
    
    # 创建评估回调
    eval_callback = EvalCallback(
        eval_env,
        best_model_save_path=f"./models/best_{algorithm_name.lower()}_model",
        log_path=f"./logs/{algorithm_name.lower()}_eval",
        eval_freq=5000,
        deterministic=True,
        render=False,
        n_eval_episodes=EVAL_EPISODES
    )
    
    # 训练模型
    print(f"开始训练，总时间步数: {total_timesteps}")
    model.learn(
        total_timesteps=total_timesteps,
        callback=eval_callback,
        progress_bar=True
    )
    
    # 保存最终模型
    model_path = f"./models/final_{algorithm_name.lower()}_model"
    os.makedirs("./models", exist_ok=True)
    model.save(model_path)
    print(f"保存最终模型: {model_path}")
    
    return model

# =================== 数据生成函数 ===================
def generate_sample_data(length: int = 2000) -> pd.DataFrame:
    """生成示例股票数据"""
    np.random.seed(42)
    
    # 生成基础价格序列
    base_price = 100
    returns = np.random.normal(0.001, 0.02, length)  # 日收益率
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1))  # 价格不能为负
        
    prices = np.array(prices[1:])
    
    # 生成OHLC数据
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.normal(0, 0.005, length)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, length))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, length))),
        'volume': np.random.lognormal(10, 1, length),
    })
    
    # 确保OHLC逻辑正确
    df['high'] = np.maximum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['low'] = np.minimum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['amount'] = df['close'] * df['volume']
    
    # 添加日期
    df['dt'] = pd.date_range(start='2020-01-01', periods=length, freq='D')
    
    return df

# =================== 主函数 ===================
def main():
    """主函数"""
    print("=== 增强版强化学习股票交易系统 (Stable-Baselines3) ===")
    
    # 设置随机种子
    np.random.seed(SEED)
    torch.manual_seed(SEED)
    
    # 获取数据
    if CZSC_AVAILABLE:
        try:
            print("使用CZSC数据源...")
            df = get_kline('518880', freq='15min')
            df['volume'] = df['vol']
            df['amount'] = df['close'] * df['volume']
            print(f"加载数据: {len(df)} 条记录")
        except Exception as e:
            print(f"CZSC数据加载失败: {e}")
            print("使用模拟数据...")
            df = generate_sample_data(2000)
    else:
        print("使用模拟数据...")
        df = generate_sample_data(2000)
    
    # 数据分割
    split_point = int(len(df) * 0.8)
    train_df = df[:split_point].reset_index(drop=True)
    eval_df = df[split_point:].reset_index(drop=True)
    
    print(f"训练数据: {len(train_df)} 条")
    print(f"评估数据: {len(eval_df)} 条")
    
    # 创建测试环境
    print("\n=== 环境测试 ===")
    test_env = EnhancedStockTradingEnv(train_df[:100])
    obs, info = test_env.reset()
    print(f"状态空间维度: {obs.shape}")
    print(f"动作空间: {test_env.action_space}")
    
    # 测试一步交互
    action = test_env.action_space.sample()
    next_obs, reward, done, truncated, info = test_env.step(action)
    print(f"测试动作: {action}")
    print(f"奖励: {reward:.4f}")
    print(f"决策解释: {info['explanation']['decision_reasoning'][:100]}...")
    
    # 训练代理
    print("\n=== 开始训练 ===")
    trained_model = train_enhanced_agent_sb3(
        train_df, eval_df, 
        algorithm_name='SAC', 
        total_timesteps=50000  # 减少训练步数用于演示
    )
    
    print("\n=== 最终评估 ===")
    final_env = EnhancedStockTradingEnv(eval_df)
    final_results = run_evaluate_episodes(trained_model, final_env, 10)
    
    print(f"\n最终结果:")
    print(f"平均奖励: {final_results['avg_reward']:.3f}")
    print(f"平均收益: ${final_results['avg_profit']:,.2f}")
    print(f"成功率: {final_results['success_rate']:.1%}")

if __name__ == '__main__':
    main()