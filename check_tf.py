#!/usr/bin/env python3
"""
检查TensorFlow和相关包的兼容性
"""

def check_tensorflow():
    """检查TensorFlow"""
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow: {tf.__version__}")
        
        # 测试基本功能
        x = tf.constant([1, 2, 3])
        print(f"✓ TensorFlow基本功能正常: {x}")
        return True
    except ImportError:
        print("✗ TensorFlow未安装")
        return False
    except Exception as e:
        print(f"✗ TensorFlow错误: {e}")
        return False

def check_packages():
    """检查关键包"""
    packages = {
        'numpy': 'numpy',
        'pandas': 'pandas', 
        'gymnasium': 'gymnasium',
        'stable_baselines3': 'stable_baselines3',
        'torch': 'torch'
    }
    
    results = {}
    
    for name, module in packages.items():
        try:
            mod = __import__(module)
            version = getattr(mod, '__version__', 'unknown')
            print(f"✓ {name}: {version}")
            results[name] = True
        except ImportError:
            print(f"✗ {name}: 未安装")
            results[name] = False
        except Exception as e:
            print(f"✗ {name}: 错误 - {e}")
            results[name] = False
    
    return results

def test_simple_model():
    """测试简单模型"""
    try:
        import numpy as np
        import gymnasium as gym
        from stable_baselines3 import SAC
        
        # 创建简单环境
        env = gym.make('CartPole-v1')
        
        # 创建模型
        model = SAC('MlpPolicy', env, verbose=0)
        
        # 测试预测
        obs, _ = env.reset()
        action, _ = model.predict(obs)
        
        print("✓ 简单模型测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 简单模型测试失败: {e}")
        return False

def main():
    """主函数"""
    print("系统兼容性检查")
    print("=" * 30)
    
    # 检查包
    results = check_packages()
    
    # 检查TensorFlow
    tf_ok = check_tensorflow()
    
    # 测试模型
    model_ok = test_simple_model()
    
    print("\n" + "=" * 30)
    print("检查结果:")
    
    success_count = sum(results.values()) + tf_ok + model_ok
    total_count = len(results) + 2
    
    print(f"成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有检查通过！系统准备就绪")
    else:
        print("⚠️ 部分检查失败，但核心功能可用")
        
    print("\n可以运行的脚本:")
    print("- python simple_rl_trading.py (推荐)")
    if results.get('stable_baselines3', False):
        print("- python enhanced_rl_stock_base_sb3_no_czsc.py")

if __name__ == "__main__":
    main()