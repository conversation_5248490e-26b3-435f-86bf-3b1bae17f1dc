"""
Data Manager for Backtesting

Handles loading, preprocessing, and validation of historical market data
for the backtesting engine.
"""

import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import os
import sys

# Add core modules to path
sys.path.append(str(Path(__file__).parent.parent.parent / "core"))

try:
    from enhanced_cache import DataCacheManager
    from data_quality_monitor import DataQualityMonitor
    from realtime_data_stream import RealTimeDataStream, StreamData, DataStreamSubscriber
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Enhanced data features not available: {e}")
    ENHANCED_FEATURES_AVAILABLE = False

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False

try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False


class DataManager:
    """
    Data Manager for Backtesting Engine
    
    Handles loading, validation, and preprocessing of historical market data
    from various sources (CSV files, databases, APIs).
    """
    
    def __init__(self, data_dir: str = "data", enable_enhanced_features: bool = True):
        """
        Initialize the data manager.

        Args:
            data_dir: Directory containing historical data files
            enable_enhanced_features: Enable enhanced caching and monitoring
        """
        self.logger = logging.getLogger("DataManager")
        self.data_dir = Path(data_dir)
        self.enable_enhanced_features = enable_enhanced_features and ENHANCED_FEATURES_AVAILABLE

        # Legacy cache for backward compatibility
        self.data_cache = {}

        # Enhanced features
        if self.enable_enhanced_features:
            self.cache_manager = DataCacheManager(str(self.data_dir / "cache"))
            self.quality_monitor = DataQualityMonitor()
            self.realtime_stream = RealTimeDataStream()
            self.logger.info("Enhanced data features enabled")
        else:
            self.cache_manager = None
            self.quality_monitor = None
            self.realtime_stream = None
            self.logger.info("Using basic data features only")

        # Create data directory if it doesn't exist
        self.data_dir.mkdir(exist_ok=True)

        # Standard column mapping for different data sources
        self.column_mapping = {
            'Open': 'open',
            'High': 'high',
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume',
            'Date': 'date',
            'Symbol': 'symbol',
            'Adj Close': 'adj_close'
        }

        self.logger.info(f"Data manager initialized with data directory: {self.data_dir}")
    
    def load_data(self, 
                  symbol: str,
                  start_date: Optional[str] = None,
                  end_date: Optional[str] = None,
                  source: str = "csv",
                  tushare_token: Optional[str] = None) -> pd.DataFrame:
        """
        Load historical market data for a symbol.
        
        Args:
            symbol: Trading symbol (e.g., "000001.SZ")
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            source: Data source ("csv", "database", "api")
            tushare_token: tushare数据接口token（可选，若source为tushare时必需）
            
        Returns:
            DataFrame with standardized OHLCV data
        """
        try:
            cache_key = f"{symbol}_{start_date}_{end_date}_{source}"
            date_range_key = f"{start_date}_{end_date}"

            # Check enhanced cache first
            if self.cache_manager:
                cached_data = self.cache_manager.get_market_data(symbol, date_range_key)
                if cached_data is not None:
                    self.logger.debug(f"Loading {symbol} from enhanced cache")
                    return cached_data.copy()

            # Fallback to legacy cache
            if cache_key in self.data_cache:
                self.logger.debug(f"Loading {symbol} from legacy cache")
                return self.data_cache[cache_key].copy()

            # Load data based on source with fallback mechanism
            if source == "csv":
                data = self._load_csv_data(symbol, start_date, end_date)
            elif source == "akshare":
                data = self._load_akshare_data(symbol, start_date, end_date)
            elif source == "tushare":
                data = self._load_tushare_data(symbol, start_date, end_date, tushare_token)
            elif source == "database":
                data = self._load_database_data(symbol, start_date, end_date)
            elif source == "api":
                data = self._load_api_data(symbol, start_date, end_date)
            elif source == "auto":
                # 智能数据源选择，按优先级尝试
                data = self._load_data_with_fallback(symbol, start_date, end_date, tushare_token)
            else:
                raise ValueError(f"Unsupported data source: {source}")

            # Validate and standardize data
            data = self._validate_and_standardize(data, symbol)

            # Enhanced quality monitoring
            if self.quality_monitor and not data.empty:
                self._perform_quality_check(data, symbol, source)

            # Filter by date range if specified
            if start_date or end_date:
                data = self._filter_by_date(data, start_date, end_date)

            # Cache the result
            if self.cache_manager:
                # Use enhanced cache with appropriate TTL
                ttl = 300 if source in ['akshare', 'tushare'] else 3600  # 5 min for real-time, 1 hour for others
                self.cache_manager.set_market_data(symbol, date_range_key, data.copy(), ttl)
            else:
                # Fallback to legacy cache
                self.data_cache[cache_key] = data.copy()

            self.logger.info(f"Loaded {len(data)} records for {symbol} from {start_date} to {end_date}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load data for {symbol}: {e}")
            raise
    
    def _load_csv_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load data from CSV files"""
        # Try different common file naming patterns
        possible_files = [
            f"{symbol}.csv",
            f"{symbol}_daily.csv",
            f"{symbol.replace('.', '_')}.csv",
            f"data_{symbol}.csv"
        ]
        
        data_file = None
        for filename in possible_files:
            file_path = self.data_dir / filename
            if file_path.exists():
                data_file = file_path
                break
        
        if data_file is None:
            # Create sample data if no file exists
            self.logger.warning(f"No data file found for {symbol}, creating sample data")
            return self._create_sample_data(symbol, start_date, end_date)
        
        # Load CSV with flexible parsing
        try:
            data = pd.read_csv(data_file)
            self.logger.debug(f"Loaded CSV data from {data_file}")
            return data
        except Exception as e:
            self.logger.error(f"Failed to load CSV file {data_file}: {e}")
            raise
    
    def _load_akshare_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """
        通过akshare获取A股/ETF历史行情，支持容错和重试机制

        Args:
            symbol: 股票代码，支持格式如 000001.SZ, 600519.SH
            start_date: 开始日期，格式 YYYY-MM-DD
            end_date: 结束日期，格式 YYYY-MM-DD

        Returns:
            标准化的DataFrame，包含OHLCV数据

        Raises:
            ImportError: akshare未安装
            ValueError: 不支持的股票代码格式
            RuntimeError: 数据获取失败
        """
        if not AKSHARE_AVAILABLE:
            raise ImportError("akshare is not installed. Please install akshare to use this feature.")

        # 支持股票和ETF，自动判断symbol格式
        if symbol.endswith('.SZ') or symbol.endswith('.SH'):
            code = symbol.replace('.SZ', '').replace('.SH', '')

            # 重试机制
            max_retries = 3
            retry_delay = 1  # 秒

            for attempt in range(max_retries):
                try:
                    self.logger.info(f"Attempting to fetch akshare data for {symbol} (attempt {attempt + 1}/{max_retries})")

                    # 获取数据，使用前复权
                    df = ak.stock_zh_a_hist(
                        symbol=code,
                        period="daily",
                        start_date=start_date,
                        end_date=end_date,
                        adjust="qfq"
                    )

                    if df is None or df.empty:
                        raise ValueError(f"No data returned for {symbol}")

                    # akshare返回列：日期,开盘,收盘,最高,最低,成交量,...
                    df = df.rename(columns={
                        '日期': 'date', '开盘': 'open', '收盘': 'close',
                        '最高': 'high', '最低': 'low', '成交量': 'volume'
                    })
                    df['symbol'] = symbol

                    # 数据质量检查
                    if len(df) == 0:
                        raise ValueError(f"Empty dataset returned for {symbol}")

                    self.logger.info(f"Successfully fetched {len(df)} records from akshare for {symbol}")
                    return df[['date', 'symbol', 'open', 'high', 'low', 'close', 'volume']]

                except Exception as e:
                    self.logger.warning(f"akshare获取A股数据失败 (attempt {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        # 最后一次尝试失败，抛出异常
                        raise RuntimeError(f"Failed to fetch data from akshare after {max_retries} attempts: {e}")
                    else:
                        # 等待后重试
                        import time
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
        else:
            # ETF等其他品种可扩展
            raise ValueError(f"暂不支持该symbol的akshare行情: {symbol}")
    
    def _load_tushare_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str], tushare_token: Optional[str]) -> pd.DataFrame:
        """
        通过tushare获取A股/ETF历史行情，支持容错和重试机制

        Args:
            symbol: 股票代码，支持格式如 000001.SZ, 600519.SH
            start_date: 开始日期，格式 YYYY-MM-DD
            end_date: 结束日期，格式 YYYY-MM-DD
            tushare_token: tushare API token

        Returns:
            标准化的DataFrame，包含OHLCV数据

        Raises:
            ImportError: tushare未安装
            ValueError: token未配置或不支持的股票代码格式
            RuntimeError: 数据获取失败
        """
        if not TUSHARE_AVAILABLE:
            raise ImportError("tushare is not installed. Please install tushare to use this feature.")

        token = tushare_token or os.environ.get('TUSHARE_TOKEN')
        if not token:
            raise ValueError("tushare token未配置，请通过参数或环境变量TUSHARE_TOKEN传入")

        ts.set_token(token)
        pro = ts.pro_api()

        if symbol.endswith('.SZ') or symbol.endswith('.SH'):
            ts_code = symbol  # tushare使用完整代码格式

            # 重试机制
            max_retries = 3
            retry_delay = 1  # 秒

            for attempt in range(max_retries):
                try:
                    self.logger.info(f"Attempting to fetch tushare data for {symbol} (attempt {attempt + 1}/{max_retries})")

                    # 格式化日期为tushare要求的格式 (YYYYMMDD)
                    start_date_formatted = start_date.replace('-', '') if start_date else None
                    end_date_formatted = end_date.replace('-', '') if end_date else None

                    df = pro.daily(
                        ts_code=ts_code,
                        start_date=start_date_formatted,
                        end_date=end_date_formatted
                    )

                    if df is None or df.empty:
                        raise ValueError(f"No data returned for {symbol}")

                    # tushare返回列：ts_code,trade_date,open,high,low,close,vol,...
                    df = df.rename(columns={
                        'trade_date': 'date', 'vol': 'volume', 'ts_code': 'symbol'
                    })
                    df['date'] = pd.to_datetime(df['date'])
                    df = df.sort_values('date').reset_index(drop=True)

                    # 数据质量检查
                    if len(df) == 0:
                        raise ValueError(f"Empty dataset returned for {symbol}")

                    self.logger.info(f"Successfully fetched {len(df)} records from tushare for {symbol}")
                    return df[['date', 'symbol', 'open', 'high', 'low', 'close', 'volume']]

                except Exception as e:
                    self.logger.warning(f"tushare获取A股数据失败 (attempt {attempt + 1}): {e}")
                    if attempt == max_retries - 1:
                        # 最后一次尝试失败，抛出异常
                        raise RuntimeError(f"Failed to fetch data from tushare after {max_retries} attempts: {e}")
                    else:
                        # 等待后重试
                        import time
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
        else:
            raise ValueError(f"暂不支持该symbol的tushare行情: {symbol}")

    def _load_data_with_fallback(self, symbol: str, start_date: Optional[str], end_date: Optional[str], tushare_token: Optional[str]) -> pd.DataFrame:
        """
        智能数据源选择，按优先级尝试多个数据源

        优先级顺序：
        1. CSV文件 (最快，本地数据)
        2. tushare (专业数据源，需要token)
        3. akshare (免费数据源)
        4. 样本数据 (最后备选)

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            tushare_token: tushare API token

        Returns:
            成功获取的数据DataFrame

        Raises:
            RuntimeError: 所有数据源都失败时
        """
        data_sources = []

        # 构建数据源尝试列表
        # 1. 优先尝试CSV文件
        data_sources.append(("csv", self._load_csv_data))

        # 2. 如果有tushare token，优先使用tushare
        if tushare_token or os.environ.get('TUSHARE_TOKEN'):
            data_sources.append(("tushare", lambda s, sd, ed: self._load_tushare_data(s, sd, ed, tushare_token)))

        # 3. 尝试akshare
        if AKSHARE_AVAILABLE:
            data_sources.append(("akshare", self._load_akshare_data))

        # 4. 最后使用样本数据
        data_sources.append(("sample", self._create_sample_data))

        # 按顺序尝试每个数据源
        last_error = None
        for source_name, load_func in data_sources:
            try:
                self.logger.info(f"Trying to load data from {source_name} for {symbol}")

                if source_name == "tushare":
                    data = load_func(symbol, start_date, end_date)
                else:
                    data = load_func(symbol, start_date, end_date)

                if data is not None and not data.empty:
                    self.logger.info(f"Successfully loaded data from {source_name} for {symbol}: {len(data)} records")
                    return data
                else:
                    self.logger.warning(f"No data returned from {source_name} for {symbol}")

            except Exception as e:
                self.logger.warning(f"Failed to load data from {source_name} for {symbol}: {e}")
                last_error = e
                continue

        # 如果所有数据源都失败
        raise RuntimeError(f"Failed to load data for {symbol} from all available sources. Last error: {last_error}")

    def _load_database_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load data from database (placeholder implementation)"""
        self.logger.warning("Database data loading not implemented, using sample data")
        return self._create_sample_data(symbol, start_date, end_date)
    
    def _load_api_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Load data from API (placeholder implementation)"""
        self.logger.warning("API data loading not implemented, using sample data")
        return self._create_sample_data(symbol, start_date, end_date)
    
    def _validate_and_standardize(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Validate and standardize data format"""
        df = data.copy()
        
        # Standardize column names
        df.columns = df.columns.str.strip()
        for old_name, new_name in self.column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})
        
        # Ensure required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns for {symbol}: {missing_columns}")
        
        # Add symbol column if not present
        if 'symbol' not in df.columns:
            df['symbol'] = symbol
        
        # Parse date column
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        elif df.index.name == 'Date' or 'date' in str(df.index.name).lower():
            df['date'] = pd.to_datetime(df.index)
            df = df.reset_index(drop=True)
        else:
            # Create synthetic date column if missing
            df['date'] = pd.date_range(start='2020-01-01', periods=len(df), freq='D')
        
        # Validate data quality
        df = self._validate_data_quality(df, symbol)
        
        # Sort by date
        df = df.sort_values('date').reset_index(drop=True)
        
        return df
    
    def _validate_data_quality(self, data: pd.DataFrame, symbol: str) -> pd.DataFrame:
        """Validate and clean data quality issues"""
        df = data.copy()
        original_len = len(df)
        
        # Remove rows with missing OHLC data
        required_cols = ['open', 'high', 'low', 'close']
        df = df.dropna(subset=required_cols)
        
        # Remove rows with invalid prices (negative or zero)
        for col in required_cols:
            df = df[df[col] > 0]
        
        # Check price consistency (high >= low, etc.)
        df = df[df['high'] >= df['low']]
        df = df[df['high'] >= df['open']]
        df = df[df['high'] >= df['close']]
        df = df[df['low'] <= df['open']]
        df = df[df['low'] <= df['close']]
        
        # Handle missing volume (set to 0 if missing)
        if 'volume' in df.columns:
            df['volume'] = df['volume'].fillna(0)
            df['volume'] = df['volume'].clip(lower=0)
        
        # Remove extreme outliers (price changes > 50% in one day)
        df['price_change'] = df['close'].pct_change()
        df = df[abs(df['price_change']) <= 0.5]
        df = df.drop('price_change', axis=1)
        
        removed_count = original_len - len(df)
        if removed_count > 0:
            self.logger.warning(f"Removed {removed_count} invalid records for {symbol}")
        
        return df
    
    def _filter_by_date(self, data: pd.DataFrame, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Filter data by date range"""
        df = data.copy()
        
        if 'date' not in df.columns:
            return df
        
        if start_date:
            start_dt = pd.to_datetime(start_date)
            df = df[df['date'] >= start_dt]
        
        if end_date:
            end_dt = pd.to_datetime(end_date)
            df = df[df['date'] <= end_dt]
        
        return df
    
    def _create_sample_data(self, symbol: str, start_date: Optional[str], end_date: Optional[str]) -> pd.DataFrame:
        """Create sample data for testing when real data is not available"""
        # Determine date range
        if start_date:
            start_dt = pd.to_datetime(start_date)
        else:
            start_dt = pd.to_datetime('2020-01-01')
        
        if end_date:
            end_dt = pd.to_datetime(end_date)
        else:
            end_dt = pd.to_datetime('2023-12-31')
        
        # Generate date range
        dates = pd.date_range(start=start_dt, end=end_dt, freq='D')
        dates = dates[dates.weekday < 5]  # Remove weekends
        
        # Generate synthetic price data with realistic patterns
        np.random.seed(42)  # For reproducible results
        
        n_days = len(dates)
        initial_price = 100.0
        
        # Generate returns with some trending and mean reversion
        trend = np.linspace(0, 0.2, n_days)  # 20% upward trend over period
        noise = np.random.normal(0, 0.02, n_days)  # 2% daily volatility
        returns = trend / n_days + noise
        
        # Generate price series
        prices = [initial_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Generate OHLC data
        data_records = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # Generate intraday range
            daily_range = abs(np.random.normal(0, 0.01))
            
            open_price = close * (1 + np.random.uniform(-0.005, 0.005))
            high = max(open_price, close) * (1 + daily_range)
            low = min(open_price, close) * (1 - daily_range)
            
            # Generate volume (log-normal distribution)
            volume = int(np.random.lognormal(15, 1))  # Around 3M average volume
            
            data_records.append({
                'date': date,
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': volume
            })
        
        df = pd.DataFrame(data_records)
        
        self.logger.info(f"Created {len(df)} days of sample data for {symbol}")
        return df
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols in the data directory"""
        symbols = []
        
        for file_path in self.data_dir.glob("*.csv"):
            # Extract symbol from filename
            filename = file_path.stem
            
            # Try different naming patterns
            if filename.endswith('_daily'):
                symbol = filename.replace('_daily', '')
            elif filename.startswith('data_'):
                symbol = filename.replace('data_', '')
            else:
                symbol = filename
            
            symbols.append(symbol)
        
        return sorted(symbols)
    
    def clear_cache(self):
        """Clear the data cache"""
        self.data_cache.clear()
        self.logger.info("Data cache cleared")
    
    def save_sample_data(self, symbol: str, start_date: str = "2020-01-01", end_date: str = "2023-12-31"):
        """Save sample data to CSV file for testing"""
        data = self._create_sample_data(symbol, start_date, end_date)
        
        filename = f"{symbol}.csv"
        file_path = self.data_dir / filename
        
        data.to_csv(file_path, index=False)
        self.logger.info(f"Saved sample data for {symbol} to {file_path}")
        
        return file_path

    def sync_data(self, symbols: List[str], start_date: str, end_date: str,
                  source: str = "auto", tushare_token: Optional[str] = None,
                  force_update: bool = False) -> Dict[str, bool]:
        """
        批量同步多个股票的数据

        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            source: 数据源
            tushare_token: tushare token
            force_update: 是否强制更新缓存

        Returns:
            Dict[symbol, success]: 每个股票的同步结果
        """
        results = {}

        if force_update:
            self.clear_cache()

        self.logger.info(f"Starting data sync for {len(symbols)} symbols from {start_date} to {end_date}")

        for symbol in symbols:
            try:
                data = self.load_data(
                    symbol=symbol,
                    start_date=start_date,
                    end_date=end_date,
                    source=source,
                    tushare_token=tushare_token
                )

                if data is not None and not data.empty:
                    results[symbol] = True
                    self.logger.info(f"Successfully synced {symbol}: {len(data)} records")
                else:
                    results[symbol] = False
                    self.logger.warning(f"Failed to sync {symbol}: no data returned")

            except Exception as e:
                results[symbol] = False
                self.logger.error(f"Failed to sync {symbol}: {e}")

        success_count = sum(results.values())
        self.logger.info(f"Data sync completed: {success_count}/{len(symbols)} symbols successful")

        return results

    def get_data_info(self, symbol: str) -> Dict[str, Any]:
        """
        获取数据信息和统计

        Args:
            symbol: 股票代码

        Returns:
            数据信息字典
        """
        cache_key = f"{symbol}_info"

        if cache_key in self.data_cache:
            return self.data_cache[cache_key]

        try:
            # 尝试加载数据获取信息
            data = self.load_data(symbol, source="auto")

            if data is not None and not data.empty:
                info = {
                    'symbol': symbol,
                    'total_records': len(data),
                    'date_range': {
                        'start': data['date'].min().strftime('%Y-%m-%d'),
                        'end': data['date'].max().strftime('%Y-%m-%d')
                    },
                    'data_quality': {
                        'missing_values': data.isnull().sum().to_dict(),
                        'zero_volume_days': (data['volume'] == 0).sum(),
                        'price_anomalies': self._detect_price_anomalies(data)
                    },
                    'last_updated': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                # 缓存信息
                self.data_cache[cache_key] = info
                return info
            else:
                return {'symbol': symbol, 'error': 'No data available'}

        except Exception as e:
            return {'symbol': symbol, 'error': str(e)}

    def _detect_price_anomalies(self, data: pd.DataFrame) -> Dict[str, int]:
        """检测价格异常"""
        anomalies = {}

        try:
            # 检测价格跳跃 (日涨跌幅超过20%)
            price_change = data['close'].pct_change().abs()
            anomalies['large_price_jumps'] = (price_change > 0.2).sum()

            # 检测价格为0的情况
            anomalies['zero_prices'] = (data['close'] <= 0).sum()

            # 检测高低价异常 (high < low)
            anomalies['invalid_high_low'] = (data['high'] < data['low']).sum()

            # 检测开盘价异常 (open不在high-low范围内)
            anomalies['invalid_open'] = ((data['open'] > data['high']) | (data['open'] < data['low'])).sum()

        except Exception as e:
            self.logger.warning(f"Error detecting price anomalies: {e}")
            anomalies['detection_error'] = str(e)

        return anomalies

    def _perform_quality_check(self, data: pd.DataFrame, symbol: str, source: str) -> None:
        """Perform enhanced quality check on data"""
        if not self.quality_monitor:
            return

        try:
            # Check each row for quality
            for _, row in data.iterrows():
                row_data = row.to_dict()
                quality_score = self.quality_monitor.check_data_quality(row_data, symbol, source)

                if quality_score < 0.7:
                    self.logger.warning(f"Low quality data detected for {symbol}: score={quality_score:.2f}")

        except Exception as e:
            self.logger.error(f"Error performing quality check: {e}")

    def get_enhanced_stats(self) -> Dict[str, Any]:
        """Get enhanced statistics including cache and quality metrics"""
        stats = {
            'basic_stats': {
                'cache_size': len(self.data_cache),
                'available_symbols': len(self.get_available_symbols())
            }
        }

        if self.cache_manager:
            stats['cache_stats'] = self.cache_manager.get_all_stats()

        if self.quality_monitor:
            stats['quality_stats'] = self.quality_monitor.get_quality_report()

        if self.realtime_stream:
            stats['stream_stats'] = self.realtime_stream.get_stream_stats()

        return stats

    def start_realtime_monitoring(self, symbols: List[str]) -> None:
        """Start real-time data monitoring for symbols"""
        if not self.realtime_stream:
            self.logger.warning("Real-time stream not available")
            return

        # Add data sources for real-time monitoring
        def market_data_source():
            # This would connect to real market data feeds
            # For now, return None (no real-time data)
            return None

        self.realtime_stream.add_data_source("market_feed", market_data_source)
        self.realtime_stream.start_stream()

        self.logger.info(f"Started real-time monitoring for {len(symbols)} symbols")

    def stop_realtime_monitoring(self) -> None:
        """Stop real-time data monitoring"""
        if self.realtime_stream:
            self.realtime_stream.stop_stream()
            self.logger.info("Stopped real-time monitoring")

    def get_quality_alerts(self) -> List[Dict[str, Any]]:
        """Get current quality alerts"""
        if not self.quality_monitor:
            return []

        alerts = self.quality_monitor.get_active_alerts()
        return [
            {
                'timestamp': alert.timestamp.isoformat(),
                'level': alert.level.value,
                'symbol': alert.symbol,
                'message': alert.message,
                'source': alert.source
            }
            for alert in alerts
        ]