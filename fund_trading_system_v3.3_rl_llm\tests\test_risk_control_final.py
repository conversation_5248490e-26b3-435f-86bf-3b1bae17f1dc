"""
风控系统最终验证测试
验证修复后的风控系统功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.enhanced.risk_control_agent import RiskControlAgent
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3
from core.position_tracker import PositionTracker
from core.data_structures import DimensionEvaluationResult


def test_risk_control_agent():
    """测试风控agent基本功能"""
    print("🧪 测试风控agent基本功能")
    print("-" * 50)
    
    risk_agent = RiskControlAgent()
    
    # 测试1：符合买入条件
    good_data = {
        'fund_code': '513500',
        'analysis_result': {
            'technical_analysis': {
                'indicators': {
                    'bb_position': 0.1,  # 低于下轨
                    'rsi': 40,           # 适中RSI
                    'volume_ratio': 1.5,  # 充足成交量
                }
            },
            'dimension_evaluations': {
                '波动性': DimensionEvaluationResult('波动性', 'low', 0.3, 0.8, [], 'good'),
                '流动性': DimensionEvaluationResult('流动性', 'good', 0.8, 0.9, [], 'good'),
                '情绪': DimensionEvaluationResult('情绪', 'positive', 0.7, 0.8, [], 'good')
            }
        },
        'proposed_decision': 'buy'
    }
    
    result = risk_agent.process(good_data)
    print(f"✅ 符合条件测试: {result['fund_code']} -> {result['final_decision']}")
    
    # 测试2：不符合买入条件
    bad_data = {
        'fund_code': '513080',
        'analysis_result': {
            'technical_analysis': {
                'indicators': {
                    'bb_position': 0.9,  # 高于上轨
                    'rsi': 75,           # RSI过高
                    'volume_ratio': 0.8,  # 成交量不足
                }
            }
        },
        'proposed_decision': 'buy'
    }
    
    result = risk_agent.process(bad_data)
    print(f"❌ 不符合条件测试: {result['fund_code']} -> {result['final_decision']}")
    
    # 显示违规详情
    violations = result.get('risk_validation', {}).get('technical_violations', {})
    if violations:
        print(f"   违规项目: {list(violations.keys())}")
    
    print("✅ 风控agent测试完成\n")


def test_coordinator_integration():
    """测试协调器集成"""
    print("🧪 测试协调器集成")
    print("-" * 50)
    
    coordinator = MultiAgentCoordinatorV3()
    
    # 测试协调器分析
    result = coordinator.coordinate_analysis('513030')
    
    print(f"✅ 基金: {result['fund_code']}")
    print(f"✅ 协调器版本: {result.get('coordinator_version', 'unknown')}")
    print(f"✅ 最终决策: {result.get('final_decision', 'unknown')}")
    print(f"✅ 风险等级: {result.get('risk_control', {}).get('risk_level', 'unknown')}")
    
    # 检查风控结果
    risk_control = result.get('risk_control', {})
    if risk_control:
        print(f"✅ 风控验证: {'通过' if risk_control.get('risk_validation_result', {}).get('risk_validation', {}).get('passed', False) else '未通过'}")
    
    print("✅ 协调器集成测试完成\n")


def test_position_tracking():
    """测试持仓跟踪功能"""
    print("🧪 测试持仓跟踪功能")
    print("-" * 50)
    
    tracker = PositionTracker()
    
    # 创建测试持仓
    tracker.update_position('513500', 1000, 1.0, 'buy')
    print("✅ 创建持仓: 513500, 1000份, 成本1.0")
    
    # 模拟价格上涨
    tracker.update_position('513500', 1000, 1.3, 'update')
    print("✅ 价格上涨到1.3")
    
    # 模拟价格回撤
    tracker.update_position('513500', 1000, 1.1, 'update')
    print("✅ 价格回撤到1.1")
    
    # 检查回撤
    drawdown_result = tracker.check_drawdown_trigger('513500')
    print(f"✅ 当前盈利: {drawdown_result.current_profit_pct:.1%}")
    print(f"✅ 最高盈利: {drawdown_result.max_profit_pct:.1%}")
    print(f"✅ 回撤幅度: {drawdown_result.drawdown_from_peak:.1%}")
    print(f"✅ 是否卖出: {'是' if drawdown_result.should_sell else '否'}")
    
    if drawdown_result.should_sell:
        print(f"✅ 卖出比例: {drawdown_result.recommended_sell_ratio:.0%}")
        print(f"✅ 紧急程度: {drawdown_result.urgency_level}")
    
    print("✅ 持仓跟踪测试完成\n")


def test_cache_performance():
    """测试缓存性能"""
    print("🧪 测试缓存性能")
    print("-" * 50)
    
    risk_agent = RiskControlAgent()
    
    test_data = {
        'fund_code': '513500',
        'analysis_result': {
            'technical_analysis': {
                'indicators': {
                    'bb_position': 0.1,
                    'rsi': 40,
                    'volume_ratio': 1.5,
                }
            }
        },
        'proposed_decision': 'buy'
    }
    
    import time
    
    # 第一次调用（无缓存）
    start_time = time.time()
    result1 = risk_agent.process(test_data)
    time1 = time.time() - start_time
    
    # 第二次调用（使用缓存）
    start_time = time.time()
    result2 = risk_agent.process(test_data)
    time2 = time.time() - start_time
    
    print(f"✅ 第一次调用耗时: {time1*1000:.2f}ms (缓存: {result1.get('cached', False)})")
    print(f"✅ 第二次调用耗时: {time2*1000:.2f}ms (缓存: {result2.get('cached', False)})")
    
    # 获取缓存统计
    cache_stats = risk_agent.get_cache_stats()
    print(f"✅ 缓存条目: {cache_stats.get('total_entries', 0)}")
    
    print("✅ 缓存性能测试完成\n")


def test_error_handling():
    """测试错误处理"""
    print("🧪 测试错误处理")
    print("-" * 50)
    
    risk_agent = RiskControlAgent()
    
    # 测试空数据
    empty_data = {}
    result = risk_agent.process(empty_data)
    print(f"✅ 空数据处理: {result.get('final_decision', 'unknown')}")
    
    # 测试缺少指标数据
    incomplete_data = {
        'fund_code': '513500',
        'analysis_result': {},
        'proposed_decision': 'buy'
    }
    result = risk_agent.process(incomplete_data)
    print(f"✅ 缺少数据处理: {result.get('final_decision', 'unknown')}")
    
    print("✅ 错误处理测试完成\n")


def main():
    """主测试函数"""
    print("🚀 风控系统最终验证测试")
    print("=" * 60)
    
    try:
        test_risk_control_agent()
        test_coordinator_integration()
        test_position_tracking()
        test_cache_performance()
        test_error_handling()
        
        print("=" * 60)
        print("🎉 所有测试完成！风控系统运行正常")
        print("=" * 60)
        print("✅ 主要功能验证:")
        print("   ✅ 技术指标风控验证")
        print("   ✅ 协调器集成")
        print("   ✅ 持仓跟踪和回撤监控")
        print("   ✅ 缓存性能优化")
        print("   ✅ 错误处理机制")
        print("\n🛡️ 风控系统已准备就绪！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
