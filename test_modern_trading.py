#!/usr/bin/env python3
"""
测试现代化强化学习交易系统
"""

import numpy as np
import torch
from modern_rl_trading import (
    generate_stock_data, StockTradingEnv, SAC, 
    set_seed, train_agent, evaluate_agent
)

def quick_test():
    """快速测试系统功能"""
    print("=== 快速测试现代化RL交易系统 ===")
    
    # 设置随机种子
    set_seed(42)
    
    # 生成测试数据
    print("1. 生成测试数据...")
    df = generate_stock_data(length=500)
    print(f"   数据生成完成: {df.shape}")
    
    # 创建环境
    print("2. 创建交易环境...")
    env = StockTradingEnv(df)
    print(f"   观察空间: {env.observation_space.shape}")
    print(f"   动作空间: {env.action_space.shape}")
    
    # 测试环境重置和步进
    print("3. 测试环境交互...")
    obs, info = env.reset()
    print(f"   初始观察: {obs[:5]}...")  # 只显示前5个值
    
    # 随机动作测试
    for i in range(5):
        action = env.action_space.sample()
        obs, reward, done, truncated, info = env.step(action)
        print(f"   步骤 {i+1}: 奖励={reward:.4f}, 净值=${info['net_worth']:.2f}")
        if done or truncated:
            break
    
    # 创建智能体
    print("4. 创建SAC智能体...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"   使用设备: {device}")
    
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]
    agent = SAC(state_dim, action_dim, device)
    
    # 测试智能体动作选择
    print("5. 测试智能体动作选择...")
    obs, _ = env.reset()
    action = agent.select_action(obs)
    print(f"   智能体动作: {action}")
    
    # 简短训练测试
    print("6. 简短训练测试 (10轮)...")
    train_env = StockTradingEnv(df[:400])
    scores = train_agent(train_env, agent, episodes=10)
    print(f"   训练完成，平均得分: {np.mean(scores):.4f}")
    
    # 评估测试
    print("7. 评估测试...")
    test_env = StockTradingEnv(df[400:])
    avg_reward = evaluate_agent(test_env, agent, episodes=3)
    
    print("\n=== 测试完成 ===")
    print("✓ 所有功能正常工作")
    return True

def performance_test():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    set_seed(42)
    
    # 测试不同数据量的性能
    data_sizes = [100, 500, 1000]
    
    for size in data_sizes:
        print(f"\n测试数据量: {size}")
        df = generate_stock_data(length=size)
        env = StockTradingEnv(df)
        
        # 测试环境步进速度
        import time
        start_time = time.time()
        
        obs, _ = env.reset()
        for _ in range(min(100, size-1)):
            action = env.action_space.sample()
            obs, reward, done, truncated, info = env.step(action)
            if done or truncated:
                obs, _ = env.reset()
        
        elapsed = time.time() - start_time
        print(f"   100步用时: {elapsed:.3f}秒")
        print(f"   步进速度: {100/elapsed:.1f} 步/秒")

if __name__ == "__main__":
    try:
        # 运行快速测试
        quick_test()
        
        # 运行性能测试
        performance_test()
        
        print("\n🎉 所有测试通过！系统运行正常。")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()