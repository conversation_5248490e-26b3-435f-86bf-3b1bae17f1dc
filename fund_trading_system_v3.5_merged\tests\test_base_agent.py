"""
Unit tests for BaseAgent and MarketData classes
"""

import unittest
import numpy as np
from datetime import datetime
from typing import Dict, Any

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.base_agent import BaseAgent, MarketData, TradingSignal


class MockAgent(BaseAgent):
    """Mock agent for testing BaseAgent functionality"""
    
    def __init__(self, name: str):
        super().__init__(name)
        self.test_confidence = 0.75
    
    def generate_signal(self, data: MarketData) -> TradingSignal:
        return TradingSignal(
            agent_type=self.agent_type,
            action="buy",
            strength=0.8,
            confidence=self.test_confidence,
            timestamp=datetime.now(),
            reasoning="Mock signal for testing"
        )
    
    def get_confidence(self) -> float:
        return self.test_confidence
    
    def get_explanation(self) -> str:
        return f"Mock agent {self.name} explanation"


class TestMarketData(unittest.TestCase):
    """Test cases for MarketData class"""
    
    def setUp(self):
        """Set up test data"""
        self.sample_data = MarketData(
            symbol="TEST001",
            timestamp=datetime(2023, 1, 1, 9, 30, 0),
            open=100.0,
            high=105.0,
            low=98.0,
            close=103.0,
            volume=1000000.0,
            technical_indicators={
                'ma_5': 102.0,
                'ma_20': 101.0,
                'rsi': 65.5,
                'macd': 0.5
            },
            czsc_structure={
                'bi_direction': 1,
                'duan_level': 2,
                'zhongshu_status': 0
            }
        )
    
    def test_market_data_creation(self):
        """Test MarketData object creation"""
        self.assertEqual(self.sample_data.symbol, "TEST001")
        self.assertEqual(self.sample_data.open, 100.0)
        self.assertEqual(self.sample_data.close, 103.0)
        self.assertEqual(self.sample_data.volume, 1000000.0)
    
    def test_price_change_calculation(self):
        """Test price change calculation"""
        expected_change = (103.0 - 100.0) / 100.0  # 3%
        self.assertAlmostEqual(self.sample_data.get_price_change(), expected_change, places=4)
    
    def test_price_change_zero_open(self):
        """Test price change with zero open price"""
        data = MarketData(
            symbol="TEST", timestamp=datetime.now(),
            open=0.0, high=10.0, low=0.0, close=5.0, volume=1000.0
        )
        self.assertEqual(data.get_price_change(), 0.0)
    
    def test_volatility_calculation(self):
        """Test volatility calculation"""
        volatility = self.sample_data.get_volatility()
        # The actual implementation uses (high - low) / close, not open
        expected_volatility = (105.0 - 98.0) / 103.0  # (high - low) / close
        self.assertAlmostEqual(volatility, expected_volatility, places=4)
    
    def test_feature_vector_generation(self):
        """Test 50-dimensional feature vector generation"""
        feature_vector = self.sample_data.to_feature_vector()
        
        # Should be 50-dimensional
        self.assertEqual(feature_vector.shape, (50,))
        
        # Should be numpy array
        self.assertIsInstance(feature_vector, np.ndarray)
        
        # Should contain valid numbers
        self.assertTrue(np.all(np.isfinite(feature_vector)))
    
    def test_feature_vector_structure(self):
        """Test feature vector structure (20+10+20)"""
        feature_vector = self.sample_data.to_feature_vector()
        
        # Technical indicators should be in first 20 positions
        tech_features = feature_vector[:20]
        self.assertTrue(np.any(tech_features != 0))  # Should have some non-zero values
        
        # CZSC features should be in positions 20-30
        czsc_features = feature_vector[20:30]
        self.assertTrue(np.any(czsc_features != 0))  # Should have some non-zero values
        
        # LLM features (positions 30-50) should contain price-derived features
        llm_features = feature_vector[30:50]
        # Should have some non-zero values from price-derived features
        self.assertTrue(np.any(llm_features != 0))  # Should have some non-zero values
        # First few positions should contain price change, volatility, etc.
        self.assertNotEqual(llm_features[0], 0)  # Price change
        self.assertNotEqual(llm_features[1], 0)  # Volatility
    
    def test_default_values(self):
        """Test default values for optional fields"""
        minimal_data = MarketData(
            symbol="MIN", timestamp=datetime.now(),
            open=10.0, high=11.0, low=9.0, close=10.5, volume=1000.0
        )
        
        self.assertIsInstance(minimal_data.technical_indicators, dict)
        self.assertIsInstance(minimal_data.czsc_structure, dict)
        self.assertIsInstance(minimal_data.news_data, list)
        self.assertIsInstance(minimal_data.metadata, dict)


class TestBaseAgent(unittest.TestCase):
    """Test cases for BaseAgent class"""
    
    def setUp(self):
        """Set up test agent"""
        self.agent = MockAgent("TestAgent")
        self.sample_data = MarketData(
            symbol="TEST001",
            timestamp=datetime.now(),
            open=100.0, high=105.0, low=98.0, close=103.0, volume=1000000.0
        )
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertEqual(self.agent.name, "TestAgent")
        self.assertEqual(self.agent.agent_type, "mock")
        self.assertFalse(self.agent.is_initialized)
        self.assertIsInstance(self.agent.performance_metrics, dict)
    
    def test_signal_generation(self):
        """Test signal generation"""
        signal = self.agent.generate_signal(self.sample_data)
        
        self.assertIsInstance(signal, TradingSignal)
        self.assertEqual(signal.agent_type, "mock")
        self.assertEqual(signal.action, "buy")
        self.assertEqual(signal.strength, 0.8)
        self.assertEqual(signal.confidence, 0.75)
    
    def test_confidence_retrieval(self):
        """Test confidence retrieval"""
        confidence = self.agent.get_confidence()
        self.assertEqual(confidence, 0.75)
        self.assertIsInstance(confidence, float)
        self.assertGreaterEqual(confidence, 0.0)
        self.assertLessEqual(confidence, 1.0)
    
    def test_explanation_retrieval(self):
        """Test explanation retrieval"""
        explanation = self.agent.get_explanation()
        self.assertIsInstance(explanation, str)
        self.assertIn("TestAgent", explanation)
    
    def test_performance_metrics_structure(self):
        """Test performance metrics structure"""
        metrics = self.agent.performance_metrics
        
        required_keys = ['total_signals', 'successful_signals', 'accuracy_rate', 'last_update']
        for key in required_keys:
            self.assertIn(key, metrics)
        
        self.assertIsInstance(metrics['total_signals'], int)
        self.assertIsInstance(metrics['successful_signals'], int)
        self.assertIsInstance(metrics['accuracy_rate'], float)
        self.assertIsInstance(metrics['last_update'], datetime)


class TestTradingSignal(unittest.TestCase):
    """Test cases for TradingSignal class"""
    
    def setUp(self):
        """Set up test signal"""
        self.signal = TradingSignal(
            agent_type="test",
            action="buy",
            strength=0.8,
            confidence=0.75,
            timestamp=datetime.now(),
            reasoning="Test signal"
        )
    
    def test_signal_creation(self):
        """Test signal creation"""
        self.assertEqual(self.signal.agent_type, "test")
        self.assertEqual(self.signal.action, "buy")
        self.assertEqual(self.signal.strength, 0.8)
        self.assertEqual(self.signal.confidence, 0.75)
        self.assertEqual(self.signal.reasoning, "Test signal")
    
    def test_signal_validation(self):
        """Test signal validation"""
        # Valid actions
        valid_actions = ["buy", "sell", "hold"]
        for action in valid_actions:
            signal = TradingSignal(
                agent_type="test", action=action, strength=0.5,
                confidence=0.5, timestamp=datetime.now(), reasoning="test"
            )
            self.assertEqual(signal.action, action)
        
        # Valid ranges
        self.assertGreaterEqual(self.signal.strength, 0.0)
        self.assertLessEqual(self.signal.strength, 1.0)
        self.assertGreaterEqual(self.signal.confidence, 0.0)
        self.assertLessEqual(self.signal.confidence, 1.0)


if __name__ == '__main__':
    unittest.main()
