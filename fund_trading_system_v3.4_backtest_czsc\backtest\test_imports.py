import os
import importlib
import traceback

print("开始测试 test_imports.py")
print(f"__package__ = {__package__}")

current_dir = os.path.dirname(__file__)
files = os.listdir(current_dir)

results = []

for file in files:
    if file.endswith('.py') and file not in ('__init__.py', 'test_imports.py'):
        module_name = file[:-3]
        try:
            importlib.import_module(f'.{module_name}', package=__package__)
            results.append((module_name, '成功'))
        except Exception as e:
            tb = traceback.format_exc()
            results.append((module_name, f'失败: {type(e).__name__}: {e}\n{tb}'))

for name, result in results:
    print(f'{name}: {result}') 