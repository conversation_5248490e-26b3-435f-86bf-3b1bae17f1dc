"""
Core Module for Trading System v3.5

This module provides the foundational components for the trading system:
- Configuration management
- Data structures and types
- Utilities and helpers
- System constants and enums
"""

from .config import TradingConfig, SystemConfig
from .data_structures import MarketData, TradingSignal, PerformanceMetrics
from .utils import Logger, DataValidator, FeatureProcessor

__all__ = [
    'TradingConfig',
    'SystemConfig', 
    'MarketData',
    'TradingSignal',
    'PerformanceMetrics',
    'Logger',
    'DataValidator',
    'FeatureProcessor'
]

__version__ = "3.5.0" 