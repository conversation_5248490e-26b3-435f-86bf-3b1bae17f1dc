"""
Portfolio Risk Analysis Module

This module implements comprehensive portfolio risk analysis including:
- Correlation analysis matrix
- Risk diversification assessment
- Sector/industry concentration analysis
- Stress testing and scenario analysis
- Monte Carlo risk simulation
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

# Try to import optional dependencies
try:
    import scipy.stats as stats
    from scipy.optimize import minimize
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    stats = None


class RiskLevel(Enum):
    """Risk level classifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class PortfolioPosition:
    """Individual position in portfolio"""
    symbol: str
    weight: float
    sector: str
    market_value: float
    entry_price: float
    current_price: float
    returns: List[float]
    volatility: float
    beta: Optional[float] = None


@dataclass
class RiskMetrics:
    """Portfolio risk metrics"""
    portfolio_var: float
    portfolio_cvar: float
    portfolio_volatility: float
    sharpe_ratio: float
    max_drawdown: float
    concentration_risk: float
    correlation_risk: float
    sector_concentration: Dict[str, float]
    risk_level: RiskLevel
    confidence_interval: Tuple[float, float]


@dataclass
class StressTestResult:
    """Stress test scenario result"""
    scenario_name: str
    portfolio_return: float
    portfolio_loss: float
    worst_position: str
    best_position: str
    sector_impacts: Dict[str, float]
    probability: float


class PortfolioRiskAnalyzer:
    """Comprehensive portfolio risk analyzer"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._get_default_config()
        self.logger = logging.getLogger("PortfolioRiskAnalyzer")
        
        # Risk analysis cache
        self.analysis_cache = {}
        self.last_analysis_time = None
        
        # Market data for benchmarking
        self.market_data = {}
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'var_confidence_level': 0.95,
            'cvar_confidence_level': 0.95,
            'lookback_period': 252,  # 1 year of trading days
            'monte_carlo_simulations': 10000,
            'stress_test_scenarios': [
                {'name': 'Market Crash', 'market_shock': -0.20, 'volatility_spike': 2.0},
                {'name': 'Sector Rotation', 'sector_rotation': 0.15, 'correlation_change': 0.3},
                {'name': 'Interest Rate Shock', 'rate_change': 0.02, 'duration_impact': -0.05},
                {'name': 'Liquidity Crisis', 'liquidity_shock': -0.10, 'spread_widening': 0.005}
            ],
            'concentration_threshold': 0.10,  # 10% max single position
            'sector_concentration_threshold': 0.30,  # 30% max sector exposure
            'correlation_threshold': 0.70,  # High correlation warning
            'min_positions_for_analysis': 3
        }
    
    def analyze_portfolio_risk(self, positions: List[PortfolioPosition], 
                             benchmark_returns: Optional[List[float]] = None) -> RiskMetrics:
        """Comprehensive portfolio risk analysis"""
        try:
            if len(positions) < self.config['min_positions_for_analysis']:
                return self._create_minimal_risk_metrics(positions)
            
            # Calculate portfolio metrics
            portfolio_returns = self._calculate_portfolio_returns(positions)
            portfolio_volatility = self._calculate_portfolio_volatility(positions)
            
            # Calculate VaR and CVaR
            var = self._calculate_var(portfolio_returns)
            cvar = self._calculate_cvar(portfolio_returns)
            
            # Calculate Sharpe ratio
            sharpe_ratio = self._calculate_sharpe_ratio(portfolio_returns, benchmark_returns)
            
            # Calculate maximum drawdown
            max_drawdown = self._calculate_max_drawdown(portfolio_returns)
            
            # Analyze concentration risk
            concentration_risk = self._analyze_concentration_risk(positions)
            
            # Analyze correlation risk
            correlation_risk = self._analyze_correlation_risk(positions)
            
            # Analyze sector concentration
            sector_concentration = self._analyze_sector_concentration(positions)
            
            # Determine overall risk level
            risk_level = self._determine_risk_level(var, concentration_risk, correlation_risk)
            
            # Calculate confidence interval
            confidence_interval = self._calculate_confidence_interval(portfolio_returns)
            
            return RiskMetrics(
                portfolio_var=var,
                portfolio_cvar=cvar,
                portfolio_volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                concentration_risk=concentration_risk,
                correlation_risk=correlation_risk,
                sector_concentration=sector_concentration,
                risk_level=risk_level,
                confidence_interval=confidence_interval
            )
            
        except Exception as e:
            self.logger.error(f"Portfolio risk analysis failed: {e}")
            return self._create_fallback_risk_metrics()
    
    def run_stress_tests(self, positions: List[PortfolioPosition]) -> List[StressTestResult]:
        """Run comprehensive stress tests on portfolio"""
        stress_results = []
        
        for scenario in self.config['stress_test_scenarios']:
            try:
                result = self._run_single_stress_test(positions, scenario)
                stress_results.append(result)
            except Exception as e:
                self.logger.warning(f"Stress test failed for {scenario['name']}: {e}")
        
        return stress_results
    
    def monte_carlo_simulation(self, positions: List[PortfolioPosition], 
                             time_horizon_days: int = 30) -> Dict[str, Any]:
        """Run Monte Carlo simulation for portfolio risk"""
        try:
            num_simulations = self.config['monte_carlo_simulations']
            
            # Calculate expected returns and covariance matrix
            returns_matrix = self._build_returns_matrix(positions)
            expected_returns = np.mean(returns_matrix, axis=0)
            cov_matrix = np.cov(returns_matrix.T)
            
            # Portfolio weights
            weights = np.array([pos.weight for pos in positions])
            
            # Run simulations
            portfolio_returns = []
            
            for _ in range(num_simulations):
                # Generate random returns
                random_returns = np.random.multivariate_normal(
                    expected_returns * time_horizon_days, 
                    cov_matrix * time_horizon_days
                )
                
                # Calculate portfolio return
                portfolio_return = np.dot(weights, random_returns)
                portfolio_returns.append(portfolio_return)
            
            portfolio_returns = np.array(portfolio_returns)
            
            # Calculate statistics
            mean_return = np.mean(portfolio_returns)
            std_return = np.std(portfolio_returns)
            var_95 = np.percentile(portfolio_returns, 5)
            var_99 = np.percentile(portfolio_returns, 1)
            
            # Probability of loss
            prob_loss = np.sum(portfolio_returns < 0) / num_simulations
            
            return {
                'mean_return': mean_return,
                'volatility': std_return,
                'var_95': var_95,
                'var_99': var_99,
                'probability_of_loss': prob_loss,
                'best_case': np.max(portfolio_returns),
                'worst_case': np.min(portfolio_returns),
                'simulations': num_simulations,
                'time_horizon_days': time_horizon_days
            }
            
        except Exception as e:
            self.logger.error(f"Monte Carlo simulation failed: {e}")
            return {'error': str(e)}
    
    def calculate_correlation_matrix(self, positions: List[PortfolioPosition]) -> pd.DataFrame:
        """Calculate correlation matrix for portfolio positions"""
        try:
            # Build returns matrix
            returns_matrix = self._build_returns_matrix(positions)
            
            # Calculate correlation matrix
            correlation_matrix = np.corrcoef(returns_matrix.T)
            
            # Create DataFrame with symbol names
            symbols = [pos.symbol for pos in positions]
            correlation_df = pd.DataFrame(
                correlation_matrix, 
                index=symbols, 
                columns=symbols
            )
            
            return correlation_df
            
        except Exception as e:
            self.logger.error(f"Correlation matrix calculation failed: {e}")
            return pd.DataFrame()
    
    def _calculate_portfolio_returns(self, positions: List[PortfolioPosition]) -> np.ndarray:
        """Calculate historical portfolio returns"""
        # Build returns matrix
        returns_matrix = self._build_returns_matrix(positions)
        weights = np.array([pos.weight for pos in positions])
        
        # Calculate weighted portfolio returns
        portfolio_returns = np.dot(returns_matrix, weights)
        return portfolio_returns
    
    def _calculate_portfolio_volatility(self, positions: List[PortfolioPosition]) -> float:
        """Calculate portfolio volatility"""
        returns_matrix = self._build_returns_matrix(positions)
        weights = np.array([pos.weight for pos in positions])
        
        # Calculate covariance matrix
        cov_matrix = np.cov(returns_matrix.T)
        
        # Calculate portfolio volatility
        portfolio_variance = np.dot(weights.T, np.dot(cov_matrix, weights))
        portfolio_volatility = np.sqrt(portfolio_variance)
        
        return portfolio_volatility
    
    def _build_returns_matrix(self, positions: List[PortfolioPosition]) -> np.ndarray:
        """Build returns matrix from positions"""
        # Find minimum length to ensure all series have same length
        min_length = min(len(pos.returns) for pos in positions)
        
        # Build matrix
        returns_matrix = np.array([
            pos.returns[-min_length:] for pos in positions
        ]).T
        
        return returns_matrix

    def _calculate_var(self, returns: np.ndarray, confidence_level: Optional[float] = None) -> float:
        """Calculate Value at Risk"""
        confidence = confidence_level or self.config['var_confidence_level']
        return np.percentile(returns, (1 - confidence) * 100)

    def _calculate_cvar(self, returns: np.ndarray, confidence_level: Optional[float] = None) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall)"""
        confidence = confidence_level or self.config['cvar_confidence_level']
        var = self._calculate_var(returns, confidence)
        return np.mean(returns[returns <= var])

    def _calculate_sharpe_ratio(self, portfolio_returns: np.ndarray,
                              benchmark_returns: Optional[List[float]] = None) -> float:
        """Calculate Sharpe ratio"""
        if len(portfolio_returns) == 0:
            return 0.0

        # Assume risk-free rate of 2% annually (0.02/252 daily)
        risk_free_rate = 0.02 / 252

        excess_returns = portfolio_returns - risk_free_rate

        if np.std(excess_returns) == 0:
            return 0.0

        return np.mean(excess_returns) / np.std(excess_returns)

    def _calculate_max_drawdown(self, returns: np.ndarray) -> float:
        """Calculate maximum drawdown"""
        if len(returns) == 0:
            return 0.0

        # Calculate cumulative returns
        cumulative = np.cumprod(1 + returns)

        # Calculate running maximum
        running_max = np.maximum.accumulate(cumulative)

        # Calculate drawdown
        drawdown = (cumulative - running_max) / running_max

        return np.min(drawdown)

    def _analyze_concentration_risk(self, positions: List[PortfolioPosition]) -> float:
        """Analyze concentration risk in portfolio"""
        weights = [pos.weight for pos in positions]

        # Calculate Herfindahl-Hirschman Index
        hhi = sum(w**2 for w in weights)

        # Normalize to 0-1 scale (1 = maximum concentration)
        max_hhi = 1.0  # All weight in one position
        min_hhi = 1.0 / len(positions)  # Equal weights

        if max_hhi == min_hhi:
            return 0.0

        normalized_concentration = (hhi - min_hhi) / (max_hhi - min_hhi)

        return normalized_concentration

    def _analyze_correlation_risk(self, positions: List[PortfolioPosition]) -> float:
        """Analyze correlation risk in portfolio"""
        try:
            correlation_matrix = self.calculate_correlation_matrix(positions)

            if correlation_matrix.empty:
                return 0.5  # Medium risk if can't calculate

            # Calculate average correlation (excluding diagonal)
            correlations = correlation_matrix.values
            n = len(correlations)

            # Get upper triangle excluding diagonal
            upper_triangle = correlations[np.triu_indices(n, k=1)]

            if len(upper_triangle) == 0:
                return 0.0

            avg_correlation = np.mean(np.abs(upper_triangle))

            # Normalize to risk scale (higher correlation = higher risk)
            return min(1.0, avg_correlation)

        except Exception as e:
            self.logger.warning(f"Correlation risk analysis failed: {e}")
            return 0.5

    def _analyze_sector_concentration(self, positions: List[PortfolioPosition]) -> Dict[str, float]:
        """Analyze sector concentration in portfolio"""
        sector_weights = {}

        for position in positions:
            sector = position.sector or 'Unknown'
            if sector not in sector_weights:
                sector_weights[sector] = 0.0
            sector_weights[sector] += position.weight

        return sector_weights

    def _determine_risk_level(self, var: float, concentration_risk: float,
                            correlation_risk: float) -> RiskLevel:
        """Determine overall portfolio risk level"""
        # Normalize VaR to risk scale (assuming daily VaR)
        var_risk = min(1.0, abs(var) * 10)  # Scale daily VaR

        # Calculate composite risk score
        risk_score = (var_risk * 0.4 + concentration_risk * 0.3 + correlation_risk * 0.3)

        if risk_score < 0.25:
            return RiskLevel.LOW
        elif risk_score < 0.50:
            return RiskLevel.MEDIUM
        elif risk_score < 0.75:
            return RiskLevel.HIGH
        else:
            return RiskLevel.EXTREME

    def _calculate_confidence_interval(self, returns: np.ndarray,
                                     confidence: float = 0.95) -> Tuple[float, float]:
        """Calculate confidence interval for portfolio returns"""
        if len(returns) == 0:
            return (0.0, 0.0)

        alpha = 1 - confidence
        lower_percentile = (alpha / 2) * 100
        upper_percentile = (1 - alpha / 2) * 100

        lower_bound = np.percentile(returns, lower_percentile)
        upper_bound = np.percentile(returns, upper_percentile)

        return (lower_bound, upper_bound)

    def _run_single_stress_test(self, positions: List[PortfolioPosition],
                              scenario: Dict[str, Any]) -> StressTestResult:
        """Run a single stress test scenario"""
        scenario_name = scenario['name']

        # Apply stress scenario to each position
        stressed_returns = []
        sector_impacts = {}

        for position in positions:
            # Base return impact
            base_impact = 0.0

            # Apply market shock if present
            if 'market_shock' in scenario:
                base_impact += scenario['market_shock'] * (position.beta or 1.0)

            # Apply sector-specific impacts
            sector = position.sector or 'Unknown'
            if 'sector_rotation' in scenario:
                # Simulate sector rotation impact
                sector_impact = np.random.normal(0, scenario['sector_rotation'])
                base_impact += sector_impact
                sector_impacts[sector] = sector_impact

            # Apply volatility spike
            if 'volatility_spike' in scenario:
                volatility_impact = position.volatility * (scenario['volatility_spike'] - 1)
                base_impact -= volatility_impact  # Higher volatility = negative impact

            stressed_returns.append(base_impact * position.weight)

        # Calculate portfolio impact
        portfolio_return = sum(stressed_returns)
        portfolio_loss = abs(portfolio_return) if portfolio_return < 0 else 0

        # Find worst and best performing positions
        position_impacts = [r / pos.weight for r, pos in zip(stressed_returns, positions)]
        worst_idx = np.argmin(position_impacts)
        best_idx = np.argmax(position_impacts)

        worst_position = positions[worst_idx].symbol
        best_position = positions[best_idx].symbol

        # Estimate probability (simplified)
        probability = self._estimate_scenario_probability(scenario)

        return StressTestResult(
            scenario_name=scenario_name,
            portfolio_return=portfolio_return,
            portfolio_loss=portfolio_loss,
            worst_position=worst_position,
            best_position=best_position,
            sector_impacts=sector_impacts,
            probability=probability
        )

    def _estimate_scenario_probability(self, scenario: Dict[str, Any]) -> float:
        """Estimate probability of stress scenario (simplified)"""
        # This is a simplified probability estimation
        # In practice, this would use historical data and statistical models

        scenario_name = scenario['name'].lower()

        if 'crash' in scenario_name:
            return 0.05  # 5% annual probability
        elif 'rotation' in scenario_name:
            return 0.20  # 20% annual probability
        elif 'shock' in scenario_name:
            return 0.10  # 10% annual probability
        elif 'crisis' in scenario_name:
            return 0.02  # 2% annual probability
        else:
            return 0.15  # Default 15% probability

    def _create_minimal_risk_metrics(self, positions: List[PortfolioPosition]) -> RiskMetrics:
        """Create minimal risk metrics for small portfolios"""
        if not positions:
            return self._create_fallback_risk_metrics()

        # Simple calculations for small portfolios
        weights = [pos.weight for pos in positions]
        max_weight = max(weights)

        return RiskMetrics(
            portfolio_var=-0.02,  # Assume 2% daily VaR
            portfolio_cvar=-0.03,  # Assume 3% daily CVaR
            portfolio_volatility=0.15,  # Assume 15% volatility
            sharpe_ratio=0.5,  # Assume moderate Sharpe ratio
            max_drawdown=-0.10,  # Assume 10% max drawdown
            concentration_risk=max_weight,  # Use max weight as concentration
            correlation_risk=0.5,  # Assume medium correlation
            sector_concentration=self._analyze_sector_concentration(positions),
            risk_level=RiskLevel.MEDIUM,
            confidence_interval=(-0.05, 0.03)
        )

    def _create_fallback_risk_metrics(self) -> RiskMetrics:
        """Create fallback risk metrics when analysis fails"""
        return RiskMetrics(
            portfolio_var=-0.05,
            portfolio_cvar=-0.07,
            portfolio_volatility=0.20,
            sharpe_ratio=0.0,
            max_drawdown=-0.15,
            concentration_risk=1.0,
            correlation_risk=0.8,
            sector_concentration={'Unknown': 1.0},
            risk_level=RiskLevel.HIGH,
            confidence_interval=(-0.10, 0.05)
        )
