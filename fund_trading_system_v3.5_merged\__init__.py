"""
Trading System v3.5 - Multi-Agent Trading Platform

A sophisticated trading system that integrates three different analysis approaches:
- RL Agent: Reinforcement Learning quantitative analysis
- LLM Agent: Large Language Model semantic analysis  
- CZSC Agent: Chan Theory technical analysis (10-dimensional)

The system uses a 50-dimensional state space:
- 20 dimensions: Technical indicators
- 10 dimensions: CZSC structure analysis
- 20 dimensions: LLM context features
"""

__version__ = "3.5.0"
__author__ = "Trading System Development Team"
__description__ = "Multi-Agent Trading System integrating RL, LLM, and CZSC analysis"

# Import main components for easy access
from .coordinators import TradingAgent, MultiAgentCoordinator, AgentWeight
from .core import TradingConfig, SystemConfig
from .agents import BaseAgent, MarketData, TradingSignal

__all__ = [
    'TradingAgent',
    'MultiAgentCoordinator', 
    'AgentWeight',
    'TradingConfig',
    'SystemConfig',
    'BaseAgent',
    'MarketData',
    'TradingSignal'
] 