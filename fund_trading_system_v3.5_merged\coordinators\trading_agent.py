"""
Trading Agent - Unified Interface

This is the primary interface for external systems to interact with
the multi-agent trading system. It provides a simplified API while
leveraging the full coordination capabilities.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from coordinators.multi_agent_coordinator import MultiAgentCoordinator, AgentWeight, CoordinatedDecision
from agents import MarketData, TradingSignal


@dataclass 
class TradingRecommendation:
    """Simplified trading recommendation for external consumption"""
    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0.0 to 1.0
    reasoning: str
    timestamp: datetime
    risk_level: str  # LOW, MEDIUM, HIGH
    expected_return: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None


class TradingAgent:
    """
    Unified Trading Agent Interface
    
    Provides a simple, unified interface for external systems to interact
    with the sophisticated multi-agent trading system underneath.
    """
    
    def __init__(self,
                 agent_weights: Optional[AgentWeight] = None,
                 risk_tolerance: str = "MEDIUM"):
        """
        Initialize the Trading Agent.

        Args:
            agent_weights: Custom weights for different analysis types
            risk_tolerance: Risk tolerance level (LOW, MEDIUM, HIGH)
        """
        self.logger = logging.getLogger("TradingAgent")
        self.risk_tolerance = risk_tolerance
        self.agent_weights = agent_weights or AgentWeight()

        # Initialize agents
        from agents.rl.rl_agent import RLAgent
        from agents.llm.llm_agent import LLMAgent
        from agents.czsc.czsc_agent import CZSCAgent

        agents = {
            'rl': RLAgent("RL"),
            'llm': LLMAgent("LLM"),
            'czsc': CZSCAgent("CZSC")
        }

        # Initialize the multi-agent coordinator
        self.coordinator = MultiAgentCoordinator(agents)
        
        # Risk management parameters
        self.risk_params = self._setup_risk_parameters(risk_tolerance)
        
        # Performance tracking
        self.recommendation_history = []
        
        self.logger.info(f"TradingAgent initialized with risk tolerance: {risk_tolerance}")
    
    def _setup_risk_parameters(self, risk_tolerance: str) -> Dict[str, Any]:
        """Setup risk management parameters based on tolerance level"""
        risk_configs = {
            "LOW": {
                "min_confidence": 0.7,
                "max_position_size": 0.1,
                "stop_loss_pct": 0.05,
                "take_profit_pct": 0.10
            },
            "MEDIUM": {
                "min_confidence": 0.5,
                "max_position_size": 0.2,
                "stop_loss_pct": 0.08,
                "take_profit_pct": 0.15
            },
            "HIGH": {
                "min_confidence": 0.3,
                "max_position_size": 0.3,
                "stop_loss_pct": 0.12,
                "take_profit_pct": 0.25
            }
        }
        
        return risk_configs.get(risk_tolerance, risk_configs["MEDIUM"])
    
    def analyze_and_recommend(self, 
                            symbol: str,
                            price_data: Dict[str, Any],
                            news_data: Optional[Dict] = None,
                            technical_indicators: Optional[Dict] = None) -> TradingRecommendation:
        """
        Analyze market conditions and generate trading recommendation.
        
        Args:
            symbol: Trading symbol (e.g., "000001", "159915")
            price_data: Current price data (open, high, low, close, volume)
            news_data: Optional news and sentiment data
            technical_indicators: Optional pre-calculated technical indicators
            
        Returns:
            TradingRecommendation with unified analysis results
        """
        try:
            # Create market data structure
            market_data = self._create_market_data(
                symbol, price_data, news_data, technical_indicators
            )
            
            # Get coordinated decision from multi-agent system
            coordinated_decision = self.coordinator.generate_coordinated_decision(market_data)
            
            # Apply risk management filters
            filtered_decision = self._apply_risk_filters(coordinated_decision)
            
            # Generate final recommendation
            recommendation = self._create_recommendation(
                symbol, filtered_decision, market_data
            )
            
            # Store for performance tracking
            self.recommendation_history.append(recommendation)
            if len(self.recommendation_history) > 100:
                self.recommendation_history.pop(0)
            
            self.logger.info(f"Generated recommendation for {symbol}: "
                           f"{recommendation.action} (confidence: {recommendation.confidence:.3f})")
            
            return recommendation
            
        except Exception as e:
            self.logger.error(f"Error generating recommendation for {symbol}: {e}")
            return self._fallback_recommendation(symbol, price_data)
    
    def _create_market_data(self, 
                          symbol: str,
                          price_data: Dict[str, Any],
                          news_data: Optional[Dict],
                          technical_indicators: Optional[Dict]) -> MarketData:
        """Create MarketData structure from input parameters"""
        
        # Extract price information
        timestamp = price_data.get('timestamp', datetime.now())
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp)
        
        # Create technical indicators dictionary (20-dimensional)
        tech_indicators = technical_indicators or {}
        default_indicators = {
            f'tech_indicator_{i}': 0.0 for i in range(1, 21)
        }
        default_indicators.update(tech_indicators)
        
        # Create CZSC structure placeholder (10-dimensional) 
        czsc_structure = {
            f'czsc_feature_{i}': 0.0 for i in range(1, 11)
        }
        
        return MarketData(
            symbol=symbol,
            timestamp=timestamp,
            open=float(price_data.get('open', 0)),
            high=float(price_data.get('high', 0)),
            low=float(price_data.get('low', 0)),
            close=float(price_data.get('close', 0)),
            volume=float(price_data.get('volume', 0)),
            technical_indicators=default_indicators,
            czsc_structure=czsc_structure,
            news_data=news_data or [],
            metadata={}
        )
    
    def _apply_risk_filters(self, decision: CoordinatedDecision) -> CoordinatedDecision:
        """Apply risk management filters to coordinated decision"""
        
        # Check minimum confidence threshold
        if decision.final_confidence < self.risk_params['min_confidence']:
            # Lower confidence decisions become HOLD
            if decision.final_action in ['BUY', 'SELL']:
                self.logger.info(f"Filtered {decision.final_action} to HOLD due to low confidence "
                               f"({decision.final_confidence:.3f} < {self.risk_params['min_confidence']})")
                
                # Create new filtered decision
                filtered_decision = CoordinatedDecision(
                    final_action="HOLD",
                    final_confidence=decision.final_confidence * 0.5,  # Reduce confidence
                    individual_signals=decision.individual_signals,
                    fusion_method=decision.fusion_method + "_risk_filtered",
                    conflict_resolution=decision.conflict_resolution,
                    reasoning=decision.reasoning + " | Risk Filter: Low confidence -> HOLD",
                    timestamp=decision.timestamp
                )
                return filtered_decision
        
        return decision
    
    def _create_recommendation(self, 
                             symbol: str,
                             decision: CoordinatedDecision,
                             market_data: MarketData) -> TradingRecommendation:
        """Create final trading recommendation from coordinated decision"""
        
        # Determine risk level based on confidence and volatility
        risk_level = self._assess_risk_level(decision, market_data)
        
        # Calculate position sizing and risk parameters
        position_info = self._calculate_position_parameters(decision, market_data)
        
        return TradingRecommendation(
            symbol=symbol,
            action=decision.final_action,
            confidence=decision.final_confidence,
            reasoning=decision.reasoning,
            timestamp=decision.timestamp,
            risk_level=risk_level,
            expected_return=position_info.get('expected_return'),
            stop_loss=position_info.get('stop_loss'),
            take_profit=position_info.get('take_profit')
        )
    
    def _assess_risk_level(self, decision: CoordinatedDecision, market_data: MarketData) -> str:
        """Assess risk level of the recommendation"""
        
        # Factors affecting risk assessment
        confidence = decision.final_confidence
        action = decision.final_action
        
        # Base risk on confidence level
        if confidence > 0.8:
            base_risk = "LOW"
        elif confidence > 0.5:
            base_risk = "MEDIUM" 
        else:
            base_risk = "HIGH"
        
        # Adjust for action type
        if action == "HOLD":
            base_risk = "LOW"  # HOLD is always low risk
        
        # Check for conflicts in individual signals
        has_conflicts = len(set(signal.action for signal in decision.individual_signals.values())) > 1
        if has_conflicts and base_risk != "HIGH":
            # Increase risk if there are conflicts
            if base_risk == "LOW":
                base_risk = "MEDIUM"
            elif base_risk == "MEDIUM":
                base_risk = "HIGH"
        
        return base_risk
    
    def _calculate_position_parameters(self, 
                                     decision: CoordinatedDecision,
                                     market_data: MarketData) -> Dict[str, Optional[float]]:
        """Calculate position sizing and risk management parameters"""
        
        if decision.final_action == "HOLD":
            return {
                'expected_return': None,
                'stop_loss': None,
                'take_profit': None
            }
        
        current_price = market_data.close
        
        # Calculate expected return based on confidence and historical patterns
        expected_return = decision.final_confidence * 0.1  # Max 10% expected return
        
        # Calculate stop loss and take profit levels
        stop_loss_pct = self.risk_params['stop_loss_pct']
        take_profit_pct = self.risk_params['take_profit_pct']
        
        if decision.final_action == "BUY":
            stop_loss = current_price * (1 - stop_loss_pct)
            take_profit = current_price * (1 + take_profit_pct)
        else:  # SELL
            stop_loss = current_price * (1 + stop_loss_pct)
            take_profit = current_price * (1 - take_profit_pct)
            expected_return = -expected_return  # Negative for short positions
        
        return {
            'expected_return': expected_return,
            'stop_loss': stop_loss,
            'take_profit': take_profit
        }
    
    def _fallback_recommendation(self, symbol: str, price_data: Dict[str, Any]) -> TradingRecommendation:
        """Generate fallback recommendation when analysis fails"""
        return TradingRecommendation(
            symbol=symbol,
            action="HOLD",
            confidence=0.1,
            reasoning="Analysis failed - using conservative HOLD recommendation",
            timestamp=datetime.now(),
            risk_level="HIGH"  # High risk due to uncertainty
        )
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status and performance metrics"""
        
        # Get coordination statistics
        coord_stats = self.coordinator.get_coordination_stats()
        
        # Get agent features
        agent_features = self.coordinator.get_agent_features()
        
        # Calculate recommendation statistics
        recent_recommendations = self.recommendation_history[-20:] if self.recommendation_history else []
        
        rec_stats = {}
        if recent_recommendations:
            actions = [rec.action for rec in recent_recommendations]
            confidences = [rec.confidence for rec in recent_recommendations]
            risk_levels = [rec.risk_level for rec in recent_recommendations]
            
            rec_stats = {
                'total_recommendations': len(recent_recommendations),
                'action_distribution': {action: actions.count(action) for action in set(actions)},
                'risk_level_distribution': {risk: risk_levels.count(risk) for risk in set(risk_levels)},
                'average_confidence': sum(confidences) / len(confidences),
                'confidence_range': [min(confidences), max(confidences)]
            }
        
        return {
            'system_info': {
                'risk_tolerance': self.risk_tolerance,
                'risk_params': self.risk_params,
                'total_recommendations': len(self.recommendation_history)
            },
            'coordination_stats': coord_stats,
            'agent_features': agent_features,
            'recommendation_stats': rec_stats,
            'status': 'operational'
        }
    
    def update_risk_tolerance(self, new_risk_tolerance: str):
        """Update risk tolerance and reconfigure parameters"""
        if new_risk_tolerance not in ["LOW", "MEDIUM", "HIGH"]:
            raise ValueError("Risk tolerance must be LOW, MEDIUM, or HIGH")
        
        self.risk_tolerance = new_risk_tolerance
        self.risk_params = self._setup_risk_parameters(new_risk_tolerance)
        
        self.logger.info(f"Updated risk tolerance to: {new_risk_tolerance}")
    
    def update_agent_weights(self, rl_weight: float, llm_weight: float, czsc_weight: float):
        """Update weights for different analysis types"""
        new_weights = AgentWeight(
            rl_weight=rl_weight,
            llm_weight=llm_weight,
            czsc_weight=czsc_weight
        )
        
        self.coordinator.update_agent_weights(new_weights)
        self.logger.info(f"Updated agent weights: RL={rl_weight}, LLM={llm_weight}, CZSC={czsc_weight}")
    
    def get_agent_analysis_breakdown(self, symbol: str, price_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get detailed breakdown of individual agent analyses"""
        try:
            market_data = self._create_market_data(symbol, price_data, None, None)
            coordinated_decision = self.coordinator.generate_coordinated_decision(market_data)
            
            breakdown = {
                'symbol': symbol,
                'timestamp': coordinated_decision.timestamp,
                'individual_analyses': {},
                'coordination_info': {
                    'fusion_method': coordinated_decision.fusion_method,
                    'conflict_resolution': coordinated_decision.conflict_resolution,
                    'final_decision': {
                        'action': coordinated_decision.final_action,
                        'confidence': coordinated_decision.final_confidence
                    }
                }
            }
            
            # Extract individual agent analyses
            for agent_name, signal in coordinated_decision.individual_signals.items():
                breakdown['individual_analyses'][agent_name] = {
                    'action': signal.action,
                    'confidence': signal.confidence,
                    'reasoning': signal.reasoning,
                    'features': signal.features
                }
            
            return breakdown
            
        except Exception as e:
            self.logger.error(f"Error getting analysis breakdown: {e}")
            return {'error': str(e)} 