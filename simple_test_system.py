#!/usr/bin/env python3
"""
简化测试系统 - 不使用技术指标
"""

from integrated_trading_system import TradingSystem

def simple_test():
    """简化测试配置 - 不使用技术指标"""
    config = {
        'data': {
            'symbol': 'SIMPLE_TEST',
            'length': 300,  # 更少的数据量
            'train_ratio': 0.7,
            'val_ratio': 0.15
        },
        'env': {
            'initial_balance': 100000,
            'use_technical_indicators': False,  # 关闭技术指标
            'transaction_cost': 0.001
        },
        'training': {
            'episodes': 50,  # 更少的训练轮数
            'batch_size': 32,
            'memory_size': 5000,
            'eval_episodes': 2,
            'save_freq': 25
        },
        'model': {
            'actor_lr': 3e-4,
            'critic_lr': 3e-4,
            'gamma': 0.99,
            'tau': 0.005,
            'alpha': 0.2
        }
    }
    
    print("=== 简化测试交易系统（无技术指标）===")
    
    # 创建交易系统
    system = TradingSystem(config)
    
    # 运行完整流程
    train_results, test_results = system.run_complete_pipeline()
    
    print("\n=== 简化测试完成 ===")
    print(f"最佳验证得分: {train_results['best_val_score']:.4f}")
    print(f"平均测试收益率: {test_results['avg_profit_rate']*100:.2f}%")
    
    return train_results, test_results

if __name__ == "__main__":
    simple_test()