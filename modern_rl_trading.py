#!/usr/bin/env python3
"""
现代化的强化学习股票交易系统
使用PyTorch + Gymnasium + 简化的SAC实现
"""

import os
import random
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from collections import deque
import gymnasium as gym
from gymnasium import spaces
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# =================== 配置参数 ===================
# 归一化参数
MAX_ACCOUNT_BALANCE = 2147480
MAX_NUM_SHARES = 2147480
MAX_SHARE_PRICE = 5000
MAX_VOLUME = 1e9
MAX_AMOUNT = 1e10
INITIAL_ACCOUNT_BALANCE = 100000

# 训练参数
SEED = 42
MEMORY_SIZE = 100000
BATCH_SIZE = 64
GAMMA = 0.99
TAU = 0.005
ACTOR_LR = 3e-4
CRITIC_LR = 3e-4
ALPHA = 0.2

def set_seed(seed: int):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)

def generate_stock_data(symbol: str = '000001', length: int = 2000) -> pd.DataFrame:
    """
    生成模拟股票数据
    """
    np.random.seed(42)
    
    # 生成基础价格序列
    base_price = 10.0
    returns = np.random.normal(0.001, 0.02, length)
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1.0))
        
    prices = np.array(prices[1:])
    
    # 生成完整的OHLCV数据
    df = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.005, length)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, length))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, length))),
        'close': prices,
        'volume': np.random.lognormal(10, 1, length),
        'amount': prices * np.random.lognormal(10, 1, length),
    })
    
    # 确保OHLC逻辑正确
    df['high'] = np.maximum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['low'] = np.minimum.reduce([df['open'], df['high'], df['low'], df['close']])
    
    # 添加时间戳
    dates = pd.date_range(start='2020-01-01', periods=length, freq='15min')
    df['datetime'] = dates
    
    return df

class StockTradingEnv(gym.Env):
    """股票交易环境"""
    
    def __init__(self, df: pd.DataFrame, initial_balance: float = INITIAL_ACCOUNT_BALANCE):
        super().__init__()
        
        self.df = df.reset_index(drop=True)
        self.initial_balance = initial_balance
        
        # 动作空间：[买入比例, 卖出比例] 范围 [-1, 1]
        self.action_space = spaces.Box(low=-1, high=1, shape=(2,), dtype=np.float32)
        
        # 观察空间：市场数据 + 账户状态
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(10,), dtype=np.float32
        )
        
        self.reset()
    
    def reset(self, seed=None, options=None):
        """重置环境"""
        super().reset(seed=seed)
        
        self.current_step = 0
        self.balance = self.initial_balance
        self.shares_held = 0
        self.cost_basis = 0
        self.net_worth = self.initial_balance
        self.max_net_worth = self.initial_balance
        self.total_shares_sold = 0
        self.total_sales_value = 0
        
        return self._get_observation(), {}
    
    def _get_observation(self):
        """获取当前观察"""
        if self.current_step >= len(self.df):
            self.current_step = len(self.df) - 1
            
        row = self.df.iloc[self.current_step]
        
        obs = np.array([
            row['open'] / MAX_SHARE_PRICE,
            row['high'] / MAX_SHARE_PRICE,
            row['low'] / MAX_SHARE_PRICE,
            row['close'] / MAX_SHARE_PRICE,
            row['volume'] / MAX_VOLUME,
            row['amount'] / MAX_AMOUNT,
            self.balance / MAX_ACCOUNT_BALANCE,
            self.net_worth / MAX_ACCOUNT_BALANCE,
            self.shares_held / MAX_NUM_SHARES,
            self.cost_basis / MAX_SHARE_PRICE,
        ], dtype=np.float32)
        
        return obs
    
    def _take_action(self, action):
        """执行交易动作"""
        current_price = random.uniform(
            self.df.iloc[self.current_step]['low'],
            self.df.iloc[self.current_step]['high']
        )
        
        buy_signal = action[0]
        sell_signal = action[1]
        
        # 买入逻辑
        if buy_signal > 0 and self.balance > current_price:
            buy_amount = buy_signal * self.balance
            shares_to_buy = int(buy_amount / current_price)
            
            if shares_to_buy > 0:
                cost = shares_to_buy * current_price
                if cost <= self.balance:
                    prev_cost = self.cost_basis * self.shares_held
                    self.balance -= cost
                    self.cost_basis = (prev_cost + cost) / (self.shares_held + shares_to_buy)
                    self.shares_held += shares_to_buy
        
        # 卖出逻辑
        elif sell_signal > 0 and self.shares_held > 0:
            shares_to_sell = int(sell_signal * self.shares_held)
            
            if shares_to_sell > 0:
                revenue = shares_to_sell * current_price
                self.balance += revenue
                self.shares_held -= shares_to_sell
                self.total_shares_sold += shares_to_sell
                self.total_sales_value += revenue
                
                if self.shares_held == 0:
                    self.cost_basis = 0
        
        # 更新净值
        self.net_worth = self.balance + self.shares_held * current_price
        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth
    
    def step(self, action):
        """环境步进"""
        self._take_action(action)
        
        # 计算奖励
        profit_rate = (self.net_worth - self.initial_balance) / self.initial_balance
        reward = profit_rate
        
        # 检查终止条件
        self.current_step += 1
        done = False
        
        if self.current_step >= len(self.df) - 1:
            done = True
        elif self.net_worth <= 0:
            reward = -1
            done = True
        elif self.net_worth >= self.initial_balance * 3:
            reward = 2
            done = True
        
        info = {
            'balance': self.balance,
            'shares_held': self.shares_held,
            'net_worth': self.net_worth,
            'profit_rate': profit_rate
        }
        
        return self._get_observation(), reward, done, False, info
    
    def render(self, mode='human'):
        """渲染环境状态"""
        profit = self.net_worth - self.initial_balance
        profit_rate = profit / self.initial_balance * 100
        
        print(f"Step: {self.current_step}")
        print(f"Balance: ${self.balance:.2f}")
        print(f"Shares: {self.shares_held}")
        print(f"Net Worth: ${self.net_worth:.2f}")
        print(f"Profit: ${profit:.2f} ({profit_rate:.2f}%)")
        print("-" * 40)

class ReplayBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, capacity: int):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        """添加经验"""
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size: int):
        """采样批次数据"""
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self):
        return len(self.buffer)

class Actor(nn.Module):
    """Actor网络"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super().__init__()
        
        self.net = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
        )
        
        self.mean_head = nn.Linear(hidden_dim, action_dim)
        self.log_std_head = nn.Linear(hidden_dim, action_dim)
        
        self.log_std_min = -20
        self.log_std_max = 2
    
    def forward(self, state):
        x = self.net(state)
        mean = self.mean_head(x)
        log_std = self.log_std_head(x)
        log_std = torch.clamp(log_std, self.log_std_min, self.log_std_max)
        return mean, log_std
    
    def sample(self, state):
        mean, log_std = self.forward(state)
        std = log_std.exp()
        normal = torch.distributions.Normal(mean, std)
        x_t = normal.rsample()
        action = torch.tanh(x_t)
        log_prob = normal.log_prob(x_t)
        log_prob -= torch.log(1 - action.pow(2) + 1e-6)
        log_prob = log_prob.sum(1, keepdim=True)
        return action, log_prob

class Critic(nn.Module):
    """Critic网络"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 256):
        super().__init__()
        
        # Q1网络
        self.q1 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
        # Q2网络
        self.q2 = nn.Sequential(
            nn.Linear(state_dim + action_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
    
    def forward(self, state, action):
        sa = torch.cat([state, action], 1)
        return self.q1(sa), self.q2(sa)

class SAC:
    """简化的SAC算法实现"""
    
    def __init__(self, state_dim: int, action_dim: int, device='cpu'):
        self.device = device
        self.action_dim = action_dim
        
        # 网络初始化
        self.actor = Actor(state_dim, action_dim).to(device)
        self.critic = Critic(state_dim, action_dim).to(device)
        self.critic_target = Critic(state_dim, action_dim).to(device)
        
        # 复制参数到目标网络
        self.critic_target.load_state_dict(self.critic.state_dict())
        
        # 优化器
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=ACTOR_LR)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=CRITIC_LR)
        
        # 自动调节温度参数
        self.log_alpha = torch.zeros(1, requires_grad=True, device=device)
        self.alpha_optimizer = optim.Adam([self.log_alpha], lr=3e-4)
        self.target_entropy = -action_dim
    
    def select_action(self, state, evaluate=False):
        """选择动作"""
        state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        
        if evaluate:
            mean, _ = self.actor(state)
            action = torch.tanh(mean)
        else:
            action, _ = self.actor.sample(state)
        
        return action.cpu().data.numpy().flatten()
    
    def update(self, replay_buffer, batch_size=BATCH_SIZE):
        """更新网络参数"""
        if len(replay_buffer) < batch_size:
            return
        
        # 采样数据
        state, action, reward, next_state, done = replay_buffer.sample(batch_size)
        
        state = torch.FloatTensor(state).to(self.device)
        action = torch.FloatTensor(action).to(self.device)
        reward = torch.FloatTensor(reward).unsqueeze(1).to(self.device)
        next_state = torch.FloatTensor(next_state).to(self.device)
        done = torch.FloatTensor(done).unsqueeze(1).to(self.device)
        
        # 更新Critic
        with torch.no_grad():
            next_action, next_log_prob = self.actor.sample(next_state)
            q1_next, q2_next = self.critic_target(next_state, next_action)
            q_next = torch.min(q1_next, q2_next) - self.log_alpha.exp() * next_log_prob
            target_q = reward + (1 - done) * GAMMA * q_next
        
        q1, q2 = self.critic(state, action)
        critic_loss = F.mse_loss(q1, target_q) + F.mse_loss(q2, target_q)
        
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()
        
        # 更新Actor
        new_action, log_prob = self.actor.sample(state)
        q1_new, q2_new = self.critic(state, new_action)
        q_new = torch.min(q1_new, q2_new)
        
        actor_loss = (self.log_alpha.exp() * log_prob - q_new).mean()
        
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()
        
        # 更新温度参数
        alpha_loss = -(self.log_alpha * (log_prob + self.target_entropy).detach()).mean()
        
        self.alpha_optimizer.zero_grad()
        alpha_loss.backward()
        self.alpha_optimizer.step()
        
        # 软更新目标网络
        for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
            target_param.data.copy_(TAU * param.data + (1 - TAU) * target_param.data)
    
    def save(self, filename):
        """保存模型"""
        torch.save({
            'actor': self.actor.state_dict(),
            'critic': self.critic.state_dict(),
            'critic_target': self.critic_target.state_dict(),
            'log_alpha': self.log_alpha,
        }, filename)
    
    def load(self, filename):
        """加载模型"""
        checkpoint = torch.load(filename)
        self.actor.load_state_dict(checkpoint['actor'])
        self.critic.load_state_dict(checkpoint['critic'])
        self.critic_target.load_state_dict(checkpoint['critic_target'])
        self.log_alpha = checkpoint['log_alpha']

def train_agent(env, agent, episodes=1000):
    """训练智能体"""
    replay_buffer = ReplayBuffer(MEMORY_SIZE)
    scores = []
    
    for episode in range(episodes):
        state, _ = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            # 选择动作
            action = agent.select_action(state)
            
            # 执行动作
            next_state, reward, done, truncated, info = env.step(action)
            done = done or truncated
            
            # 存储经验
            replay_buffer.push(state, action, reward, next_state, done)
            
            # 更新网络
            if len(replay_buffer) > BATCH_SIZE:
                agent.update(replay_buffer)
            
            state = next_state
            episode_reward += reward
        
        scores.append(episode_reward)
        
        if episode % 100 == 0:
            avg_score = np.mean(scores[-100:])
            print(f"Episode {episode}, Average Score: {avg_score:.4f}, "
                  f"Net Worth: ${info['net_worth']:.2f}")
    
    return scores

def evaluate_agent(env, agent, episodes=10):
    """评估智能体"""
    total_rewards = []
    
    for episode in range(episodes):
        state, _ = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            action = agent.select_action(state, evaluate=True)
            state, reward, done, truncated, info = env.step(action)
            done = done or truncated
            episode_reward += reward
        
        total_rewards.append(episode_reward)
        print(f"Eval Episode {episode + 1}: Reward={episode_reward:.4f}, "
              f"Net Worth=${info['net_worth']:.2f}, "
              f"Profit Rate={info['profit_rate']*100:.2f}%")
    
    avg_reward = np.mean(total_rewards)
    print(f"\nAverage Evaluation Reward: {avg_reward:.4f}")
    return avg_reward

if __name__ == "__main__":
    # 设置随机种子
    set_seed(SEED)
    
    # 生成数据
    print("生成股票数据...")
    df = generate_stock_data(length=2000)
    print(f"数据生成完成，共 {len(df)} 条记录")
    
    # 创建环境
    train_df = df[:1600]
    test_df = df[1600:].reset_index(drop=True)
    
    train_env = StockTradingEnv(train_df)
    test_env = StockTradingEnv(test_df)
    
    # 创建智能体
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    state_dim = train_env.observation_space.shape[0]
    action_dim = train_env.action_space.shape[0]
    
    agent = SAC(state_dim, action_dim, device)
    
    # 创建模型保存目录
    os.makedirs('./models', exist_ok=True)
    
    # 训练
    print("开始训练...")
    scores = train_agent(train_env, agent, episodes=500)
    
    # 保存模型
    agent.save('./models/sac_trading_model.pth')
    print("模型已保存")
    
    # 评估
    print("\n开始评估...")
    avg_reward = evaluate_agent(test_env, agent, episodes=5)
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(scores)
    plt.title('Training Scores')
    plt.xlabel('Episode')
    plt.ylabel('Score')
    plt.savefig('./models/training_curve.png')
    plt.show()
    
    print("训练和评估完成！")