"""
Base LLM Provider Interface

Defines the abstract interface that all LLM providers must implement.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import time
import logging


@dataclass
class LLMResponse:
    """Standardized response from LLM providers"""
    content: str
    confidence: float
    sentiment_scores: Dict[str, float]
    reasoning: str
    metadata: Dict[str, Any]
    provider: str
    model: str
    timestamp: float
    
    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


@dataclass
class LLMConfig:
    """Configuration for LLM providers"""
    provider_name: str
    model_name: str
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_tokens: int = 1000
    temperature: float = 0.3
    timeout: float = 30.0
    retry_attempts: int = 3
    retry_delay: float = 1.0
    custom_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_params is None:
            self.custom_params = {}


class LLMProviderBase(ABC):
    """Abstract base class for all LLM providers"""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.logger = logging.getLogger(f"LLM.{config.provider_name}")
        self.is_initialized = False
        self.call_count = 0
        self.error_count = 0
        self.total_tokens = 0
        
    @abstractmethod
    def initialize(self) -> bool:
        """Initialize the LLM provider
        
        Returns:
            bool: True if initialization successful
        """
        pass
    
    @abstractmethod
    def analyze_market_sentiment(self, text: str, context: Dict[str, Any] = None) -> LLMResponse:
        """Analyze market sentiment from text
        
        Args:
            text: Text to analyze
            context: Additional context information
            
        Returns:
            LLMResponse: Analysis results
        """
        pass
    
    @abstractmethod
    def generate_market_insight(self, market_data: Dict[str, Any]) -> LLMResponse:
        """Generate market insights from data
        
        Args:
            market_data: Market data dictionary
            
        Returns:
            LLMResponse: Generated insights
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is available
        
        Returns:
            bool: True if provider is available
        """
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get provider statistics
        
        Returns:
            Dict[str, Any]: Provider statistics
        """
        return {
            'provider': self.config.provider_name,
            'model': self.config.model_name,
            'is_initialized': self.is_initialized,
            'call_count': self.call_count,
            'error_count': self.error_count,
            'error_rate': self.error_count / max(self.call_count, 1),
            'total_tokens': self.total_tokens
        }
    
    def _increment_call_count(self):
        """Increment call counter"""
        self.call_count += 1
    
    def _increment_error_count(self):
        """Increment error counter"""
        self.error_count += 1
    
    def _add_tokens(self, token_count: int):
        """Add to total token count"""
        self.total_tokens += token_count
    
    def _create_error_response(self, error_message: str) -> LLMResponse:
        """Create error response
        
        Args:
            error_message: Error message
            
        Returns:
            LLMResponse: Error response
        """
        return LLMResponse(
            content="Error occurred during analysis",
            confidence=0.0,
            sentiment_scores={'positive': 0.0, 'negative': 0.0, 'neutral': 1.0},
            reasoning=f"Error: {error_message}",
            metadata={'error': True, 'error_message': error_message},
            provider=self.config.provider_name,
            model=self.config.model_name,
            timestamp=time.time()
        )
    
    def _retry_with_backoff(self, func, *args, **kwargs):
        """Retry function with exponential backoff
        
        Args:
            func: Function to retry
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result or raises last exception
        """
        last_exception = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")
                
                if attempt < self.config.retry_attempts - 1:
                    delay = self.config.retry_delay * (2 ** attempt)
                    time.sleep(delay)
        
        # All attempts failed
        self._increment_error_count()
        raise last_exception


class MockLLMProvider(LLMProviderBase):
    """Mock LLM provider for testing and fallback"""
    
    def __init__(self, config: LLMConfig = None):
        if config is None:
            config = LLMConfig(
                provider_name="mock",
                model_name="mock-model"
            )
        super().__init__(config)
    
    def initialize(self) -> bool:
        """Initialize mock provider"""
        self.is_initialized = True
        self.logger.info("Mock LLM provider initialized")
        return True
    
    def analyze_market_sentiment(self, text: str, context: Dict[str, Any] = None) -> LLMResponse:
        """Mock sentiment analysis"""
        self._increment_call_count()
        
        # Simple mock sentiment based on text length and keywords
        positive_keywords = ['up', 'rise', 'gain', 'bull', 'growth', 'profit']
        negative_keywords = ['down', 'fall', 'loss', 'bear', 'decline', 'risk']
        
        text_lower = text.lower()
        positive_score = sum(1 for word in positive_keywords if word in text_lower) / 10
        negative_score = sum(1 for word in negative_keywords if word in text_lower) / 10
        neutral_score = max(0, 1 - positive_score - negative_score)
        
        return LLMResponse(
            content=f"Mock analysis of: {text[:100]}...",
            confidence=0.6,
            sentiment_scores={
                'positive': min(positive_score, 1.0),
                'negative': min(negative_score, 1.0),
                'neutral': neutral_score
            },
            reasoning="Mock sentiment analysis based on keyword matching",
            metadata={'mock': True, 'text_length': len(text)},
            provider=self.config.provider_name,
            model=self.config.model_name,
            timestamp=time.time()
        )
    
    def generate_market_insight(self, market_data: Dict[str, Any]) -> LLMResponse:
        """Mock market insight generation"""
        self._increment_call_count()
        
        price_change = market_data.get('price_change', 0)
        volume = market_data.get('volume', 0)
        
        if price_change > 0.02:
            sentiment = "bullish"
            confidence = 0.7
        elif price_change < -0.02:
            sentiment = "bearish"
            confidence = 0.7
        else:
            sentiment = "neutral"
            confidence = 0.5
        
        return LLMResponse(
            content=f"Mock market insight: {sentiment} sentiment detected",
            confidence=confidence,
            sentiment_scores={
                'positive': 0.7 if sentiment == 'bullish' else 0.2,
                'negative': 0.7 if sentiment == 'bearish' else 0.2,
                'neutral': 0.6 if sentiment == 'neutral' else 0.1
            },
            reasoning=f"Based on price change of {price_change:.2%} and volume {volume}",
            metadata={'mock': True, 'sentiment': sentiment},
            provider=self.config.provider_name,
            model=self.config.model_name,
            timestamp=time.time()
        )
    
    def is_available(self) -> bool:
        """Mock provider is always available"""
        return True
