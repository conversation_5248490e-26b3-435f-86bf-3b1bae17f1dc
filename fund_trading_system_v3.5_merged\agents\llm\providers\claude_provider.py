"""
Claude LLM Provider

Implements LLM provider interface for Anthropic Claude models.
"""

import json
import time
from typing import Dict, Any, Optional
import logging

from .base_provider import <PERSON><PERSON><PERSON><PERSON>B<PERSON>, LLMResponse, LLMConfig

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False
    anthropic = None


class ClaudeProvider(LLMProviderBase):
    """Anthropic Claude provider implementation"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.client = None
        
        if not ANTHROPIC_AVAILABLE:
            self.logger.warning("Anthropic library not available. Install with: pip install anthropic")
    
    def initialize(self) -> bool:
        """Initialize Claude client"""
        if not ANTHROPIC_AVAILABLE:
            self.logger.error("Anthropic library not installed")
            return False
        
        if not self.config.api_key:
            self.logger.error("Anthropic API key not provided")
            return False
        
        try:
            # Initialize Anthropic client
            self.client = anthropic.Anthropic(api_key=self.config.api_key)
            
            # Test connection with a simple call
            response = self.client.messages.create(
                model=self.config.model_name,
                max_tokens=10,
                messages=[{"role": "user", "content": "Hello"}]
            )
            
            self.is_initialized = True
            self.logger.info(f"Claude provider initialized with model {self.config.model_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Claude provider: {e}")
            return False
    
    def analyze_market_sentiment(self, text: str, context: Dict[str, Any] = None) -> LLMResponse:
        """Analyze market sentiment using Claude"""
        if not self.is_initialized:
            return self._create_error_response("Provider not initialized")
        
        self._increment_call_count()
        
        try:
            # Prepare the prompt for sentiment analysis
            system_prompt = """You are a financial market sentiment analyst with deep expertise in market psychology and technical analysis. 

Your task is to analyze the given text and provide a comprehensive sentiment assessment. Consider:
- Market sentiment indicators
- Emotional tone and language
- Technical implications
- Risk factors mentioned
- Overall market context

Respond in JSON format:
{
    "sentiment": "positive/negative/neutral",
    "confidence": 0.0-1.0,
    "reasoning": "detailed explanation of your analysis",
    "scores": {
        "positive": 0.0-1.0,
        "negative": 0.0-1.0,
        "neutral": 0.0-1.0
    },
    "key_factors": ["factor1", "factor2", "factor3"]
}"""
            
            user_prompt = f"Analyze the market sentiment of this text:\n\n{text}"
            
            if context:
                user_prompt += f"\n\nAdditional context: {json.dumps(context)}"
            
            response = self._retry_with_backoff(
                self.client.messages.create,
                model=self.config.model_name,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                system=system_prompt,
                messages=[{"role": "user", "content": user_prompt}]
            )
            
            # Parse response
            content = response.content[0].text
            self._add_tokens(response.usage.input_tokens + response.usage.output_tokens)
            
            try:
                # Try to parse JSON response
                result = json.loads(content)
                sentiment_scores = result.get('scores', {})
                confidence = result.get('confidence', 0.5)
                reasoning = result.get('reasoning', 'Claude sentiment analysis')
                
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                sentiment_scores = {'positive': 0.5, 'negative': 0.3, 'neutral': 0.2}
                confidence = 0.6
                reasoning = "Claude analysis (JSON parse failed)"
            
            return LLMResponse(
                content=content,
                confidence=confidence,
                sentiment_scores=sentiment_scores,
                reasoning=reasoning,
                metadata={
                    'model': self.config.model_name,
                    'input_tokens': response.usage.input_tokens,
                    'output_tokens': response.usage.output_tokens,
                    'stop_reason': response.stop_reason
                },
                provider=self.config.provider_name,
                model=self.config.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"Claude sentiment analysis failed: {e}")
            return self._create_error_response(str(e))
    
    def generate_market_insight(self, market_data: Dict[str, Any]) -> LLMResponse:
        """Generate market insights using Claude"""
        if not self.is_initialized:
            return self._create_error_response("Provider not initialized")
        
        self._increment_call_count()
        
        try:
            # Prepare market data summary
            data_summary = {
                'symbol': market_data.get('symbol', 'Unknown'),
                'price_change': market_data.get('price_change', 0),
                'volume': market_data.get('volume', 0),
                'technical_indicators': market_data.get('technical_indicators', {}),
                'recent_news': market_data.get('news_data', [])[:3]  # Last 3 news items
            }
            
            system_prompt = """You are a senior financial analyst with expertise in quantitative analysis, technical analysis, and market psychology.

Analyze the provided market data and generate comprehensive insights covering:
1. Technical analysis interpretation
2. Market sentiment assessment
3. Risk-reward evaluation
4. Trading implications
5. Key factors driving the market

Provide your analysis in JSON format:
{
    "insight": "comprehensive market insight summary",
    "sentiment": "bullish/bearish/neutral",
    "confidence": 0.0-1.0,
    "technical_outlook": "technical analysis summary",
    "key_points": ["point1", "point2", "point3"],
    "risk_factors": ["risk1", "risk2"],
    "opportunities": ["opp1", "opp2"],
    "risk_level": "low/medium/high",
    "scores": {
        "positive": 0.0-1.0,
        "negative": 0.0-1.0,
        "neutral": 0.0-1.0
    }
}"""
            
            user_prompt = f"Analyze this market data and provide comprehensive insights:\n\n{json.dumps(data_summary, indent=2)}"
            
            response = self._retry_with_backoff(
                self.client.messages.create,
                model=self.config.model_name,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                system=system_prompt,
                messages=[{"role": "user", "content": user_prompt}]
            )
            
            # Parse response
            content = response.content[0].text
            self._add_tokens(response.usage.input_tokens + response.usage.output_tokens)
            
            try:
                # Try to parse JSON response
                result = json.loads(content)
                sentiment_scores = result.get('scores', {})
                confidence = result.get('confidence', 0.6)
                reasoning = result.get('insight', 'Claude market analysis')
                
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                sentiment_scores = {'positive': 0.4, 'negative': 0.3, 'neutral': 0.3}
                confidence = 0.6
                reasoning = "Claude market analysis (JSON parse failed)"
            
            return LLMResponse(
                content=content,
                confidence=confidence,
                sentiment_scores=sentiment_scores,
                reasoning=reasoning,
                metadata={
                    'model': self.config.model_name,
                    'input_tokens': response.usage.input_tokens,
                    'output_tokens': response.usage.output_tokens,
                    'stop_reason': response.stop_reason,
                    'data_points': len(data_summary)
                },
                provider=self.config.provider_name,
                model=self.config.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"Claude market insight generation failed: {e}")
            return self._create_error_response(str(e))
    
    def is_available(self) -> bool:
        """Check if Claude provider is available"""
        if not ANTHROPIC_AVAILABLE:
            return False
        
        if not self.is_initialized:
            return False
        
        try:
            # Quick health check
            response = self.client.messages.create(
                model=self.config.model_name,
                max_tokens=5,
                messages=[{"role": "user", "content": "ping"}]
            )
            return True
        except Exception as e:
            self.logger.warning(f"Claude availability check failed: {e}")
            return False


def create_claude_provider(api_key: str, model_name: str = "claude-3-sonnet-20240229") -> ClaudeProvider:
    """Factory function to create Claude provider
    
    Args:
        api_key: Anthropic API key
        model_name: Model name to use
        
    Returns:
        ClaudeProvider: Configured provider instance
    """
    config = LLMConfig(
        provider_name="claude",
        model_name=model_name,
        api_key=api_key,
        max_tokens=2000,
        temperature=0.3,
        timeout=30.0,
        retry_attempts=3
    )
    
    provider = ClaudeProvider(config)
    provider.initialize()
    return provider
