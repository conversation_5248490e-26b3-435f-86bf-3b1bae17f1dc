"""
流动性维度评估器
负责评估市场流动性水平和交易活跃度
"""

import logging
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult
from core.enums import LiquidityState


class LiquidityEvaluator:
    """
    @class LiquidityEvaluator
    @brief 流动性维度评估器
    @details 负责评估市场流动性水平和交易活跃度
    """
    
    def __init__(self):
        self.name = "LiquidityEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估流动性维度
        @param data: 市场数据
        @return: 流动性评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            flow_data = data.get('flow_data', {})
            
            # 获取流动性相关数据
            high_liquidity = flow_data.get('high_liquidity', False)
            volume = flow_data.get('price_data', {}).get('volume', 0)
            volume_ratio = flow_data.get('volume_analysis', {}).get('current_volume_ratio', 1.0)
            
            # 流动性评分计算
            liquidity_signals = []
            liquidity_score = 0.0
            
            # 1. 基础流动性判断
            if high_liquidity:
                base_liquidity = "高"
                liquidity_score += 0.7
                liquidity_signals.append("基础流动性高")
            else:
                base_liquidity = "低"
                liquidity_score += 0.3
                liquidity_signals.append("基础流动性低")
            
            # 2. 成交量水平
            if volume >= 8000:
                volume_level = "超高"
                vol_strength = min(1.0, volume / 10000)
                liquidity_score += vol_strength * 0.3
                liquidity_signals.append(f"成交量超高({volume})")
            elif volume >= 5000:
                volume_level = "高"
                vol_strength = volume / 8000
                liquidity_score += vol_strength * 0.25
                liquidity_signals.append(f"成交量高({volume})")
            elif volume >= 2000:
                volume_level = "正常"
                vol_strength = volume / 5000
                liquidity_score += vol_strength * 0.2
                liquidity_signals.append(f"成交量正常({volume})")
            elif volume >= 1000:
                volume_level = "低"
                vol_strength = volume / 2000
                liquidity_score += vol_strength * 0.1
                liquidity_signals.append(f"成交量低({volume})")
            else:
                volume_level = "极低"
                liquidity_score += 0.05
                liquidity_signals.append(f"成交量极低({volume})")
            
            # 3. 成交量活跃度
            if volume_ratio >= 1.5:
                activity_level = "活跃"
                activity_strength = min(1.0, (volume_ratio - 1) / 1)
                liquidity_score += activity_strength * 0.2
                liquidity_signals.append(f"交易活跃({volume_ratio:.2f}倍)")
            elif volume_ratio >= 0.8:
                activity_level = "正常"
                liquidity_signals.append(f"交易正常({volume_ratio:.2f}倍)")
            else:
                activity_level = "不活跃"
                liquidity_score *= 0.8  # 降低整体分数
                liquidity_signals.append(f"交易不活跃({volume_ratio:.2f}倍)")
            
            # 流动性状态判断
            if liquidity_score >= 0.9:
                liquidity_state = LiquidityState.SUPER_HIGH
            elif liquidity_score >= 0.7:
                liquidity_state = LiquidityState.HIGH
            elif liquidity_score >= 0.5:
                liquidity_state = LiquidityState.NORMAL
            elif liquidity_score >= 0.3:
                liquidity_state = LiquidityState.LOW
            else:
                liquidity_state = LiquidityState.EXTREMELY_LOW
            
            # 置信度计算
            confidence = min(0.95, max(0.2, 0.6 + liquidity_score * 0.3))
            
            # 数据质量评估
            data_quality = "good" if volume > 0 and volume_ratio > 0 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="流动性",
                state=liquidity_state,
                score=liquidity_score,
                confidence=confidence,
                signals=liquidity_signals,
                data_quality=data_quality,
                details={
                    'base_liquidity': base_liquidity,
                    'volume_level': volume_level,
                    'activity_level': activity_level
                },
                indicators={
                    'volume': volume,
                    'volume_ratio': volume_ratio,
                    'high_liquidity': high_liquidity,
                    'liquidity_score': liquidity_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Liquidity evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="流动性",
                state=LiquidityState.NORMAL,
                score=0.5,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )
