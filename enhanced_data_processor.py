#!/usr/bin/env python3
"""
增强的数据处理模块
支持多种数据源和技术指标计算
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

class TechnicalIndicators:
    """技术指标计算类"""
    
    @staticmethod
    def sma(data: pd.Series, window: int) -> pd.Series:
        """简单移动平均"""
        return data.rolling(window=window).mean()
    
    @staticmethod
    def ema(data: pd.Series, window: int) -> pd.Series:
        """指数移动平均"""
        return data.ewm(span=window).mean()
    
    @staticmethod
    def rsi(data: pd.Series, window: int = 14) -> pd.Series:
        """相对强弱指数"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    @staticmethod
    def macd(data: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """MACD指标"""
        ema_fast = TechnicalIndicators.ema(data, fast)
        ema_slow = TechnicalIndicators.ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = TechnicalIndicators.ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    @staticmethod
    def bollinger_bands(data: pd.Series, window: int = 20, std_dev: int = 2) -> Dict[str, pd.Series]:
        """布林带"""
        sma = TechnicalIndicators.sma(data, window)
        std = data.rolling(window=window).std()
        
        return {
            'upper': sma + (std * std_dev),
            'middle': sma,
            'lower': sma - (std * std_dev)
        }
    
    @staticmethod
    def stochastic(high: pd.Series, low: pd.Series, close: pd.Series, 
                   k_window: int = 14, d_window: int = 3) -> Dict[str, pd.Series]:
        """随机指标"""
        lowest_low = low.rolling(window=k_window).min()
        highest_high = high.rolling(window=k_window).max()
        
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_window).mean()
        
        return {
            'k': k_percent,
            'd': d_percent
        }

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.indicators = TechnicalIndicators()
    
    def generate_realistic_data(self, symbol: str = '000001', 
                              start_date: str = '2020-01-01',
                              length: int = 2000,
                              freq: str = '15min') -> pd.DataFrame:
        """生成更真实的股票数据"""
        np.random.seed(42)
        
        # 生成基础价格序列（带趋势和波动性）
        base_price = 10.0
        trend = np.linspace(0, 0.5, length)  # 长期上升趋势
        
        # 生成价格序列
        prices = [base_price]
        volatility = 0.02
        
        for i in range(length):
            # 添加趋势、随机游走和均值回归
            trend_component = trend[i] / length
            random_component = np.random.normal(0, volatility)
            mean_reversion = -0.1 * (prices[-1] / base_price - 1) if len(prices) > 0 else 0
            
            return_rate = trend_component + random_component + mean_reversion
            new_price = prices[-1] * (1 + return_rate)
            prices.append(max(new_price, 0.1))  # 防止负价格
        
        prices = np.array(prices[1:])
        
        # 生成OHLC数据
        df = pd.DataFrame({
            'close': prices,
            'open': prices * (1 + np.random.normal(0, 0.005, length)),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.015, length))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.015, length))),
            'volume': np.random.lognormal(10, 1, length),
        })
        
        # 确保OHLC逻辑正确
        df['high'] = np.maximum.reduce([df['open'], df['high'], df['low'], df['close']])
        df['low'] = np.minimum.reduce([df['open'], df['high'], df['low'], df['close']])
        df['amount'] = df['close'] * df['volume']
        
        # 添加时间戳
        dates = pd.date_range(start=start_date, periods=length, freq=freq)
        df['datetime'] = dates
        df['symbol'] = symbol
        
        return df
    
    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加技术指标"""
        df = df.copy()
        
        # 移动平均线
        df['sma_5'] = self.indicators.sma(df['close'], 5)
        df['sma_10'] = self.indicators.sma(df['close'], 10)
        df['sma_20'] = self.indicators.sma(df['close'], 20)
        df['ema_12'] = self.indicators.ema(df['close'], 12)
        df['ema_26'] = self.indicators.ema(df['close'], 26)
        
        # RSI
        df['rsi'] = self.indicators.rsi(df['close'])
        
        # MACD
        macd_data = self.indicators.macd(df['close'])
        df['macd'] = macd_data['macd']
        df['macd_signal'] = macd_data['signal']
        df['macd_histogram'] = macd_data['histogram']
        
        # 布林带
        bb_data = self.indicators.bollinger_bands(df['close'])
        df['bb_upper'] = bb_data['upper']
        df['bb_middle'] = bb_data['middle']
        df['bb_lower'] = bb_data['lower']
        df['bb_width'] = (bb_data['upper'] - bb_data['lower']) / bb_data['middle']
        df['bb_position'] = (df['close'] - bb_data['lower']) / (bb_data['upper'] - bb_data['lower'])
        
        # 随机指标
        stoch_data = self.indicators.stochastic(df['high'], df['low'], df['close'])
        df['stoch_k'] = stoch_data['k']
        df['stoch_d'] = stoch_data['d']
        
        # 价格变化率
        df['price_change'] = df['close'].pct_change()
        df['price_change_5'] = df['close'].pct_change(5)
        df['price_change_10'] = df['close'].pct_change(10)
        
        # 成交量指标
        df['volume_sma'] = self.indicators.sma(df['volume'], 20)
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # 波动率
        df['volatility'] = df['price_change'].rolling(window=20).std()
        
        return df
    
    def normalize_features(self, df: pd.DataFrame, 
                          feature_columns: Optional[List[str]] = None) -> Tuple[pd.DataFrame, Dict]:
        """特征归一化"""
        df = df.copy()
        
        if feature_columns is None:
            # 自动选择数值列
            feature_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            # 排除一些不需要归一化的列
            exclude_cols = ['datetime', 'symbol']
            feature_columns = [col for col in feature_columns if col not in exclude_cols]
        
        normalization_params = {}
        
        for col in feature_columns:
            if col in df.columns:
                col_min = df[col].min()
                col_max = df[col].max()
                
                if col_max != col_min:
                    df[f'{col}_norm'] = (df[col] - col_min) / (col_max - col_min)
                    normalization_params[col] = {'min': col_min, 'max': col_max}
                else:
                    df[f'{col}_norm'] = 0.5  # 如果最大最小值相同，设为中间值
                    normalization_params[col] = {'min': col_min, 'max': col_max}
        
        return df, normalization_params
    
    def create_sequences(self, df: pd.DataFrame, 
                        sequence_length: int = 60,
                        feature_columns: Optional[List[str]] = None) -> Tuple[np.ndarray, np.ndarray]:
        """创建时间序列数据"""
        if feature_columns is None:
            # 使用归一化后的特征
            feature_columns = [col for col in df.columns if col.endswith('_norm')]
        
        # 填充NaN值
        df_filled = df[feature_columns].fillna(method='ffill').fillna(method='bfill')
        
        sequences = []
        targets = []
        
        for i in range(sequence_length, len(df_filled)):
            # 输入序列
            seq = df_filled.iloc[i-sequence_length:i].values
            sequences.append(seq)
            
            # 目标（下一个时间点的收盘价变化）
            current_price = df.iloc[i-1]['close']
            next_price = df.iloc[i]['close']
            price_change = (next_price - current_price) / current_price
            targets.append(price_change)
        
        return np.array(sequences), np.array(targets)
    
    def split_data(self, df: pd.DataFrame, 
                   train_ratio: float = 0.7,
                   val_ratio: float = 0.15) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """分割数据集"""
        n = len(df)
        train_end = int(n * train_ratio)
        val_end = int(n * (train_ratio + val_ratio))
        
        train_df = df.iloc[:train_end].copy()
        val_df = df.iloc[train_end:val_end].copy()
        test_df = df.iloc[val_end:].copy()
        
        return train_df, val_df, test_df
    
    def get_feature_importance(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算特征重要性（基于相关性）"""
        # 选择数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        
        if 'close' in numeric_cols:
            # 计算与收盘价的相关性
            correlations = df[numeric_cols].corr()['close'].abs().sort_values(ascending=False)
            
            importance_df = pd.DataFrame({
                'feature': correlations.index,
                'importance': correlations.values
            })
            
            return importance_df
        else:
            return pd.DataFrame(columns=['feature', 'importance'])

def demo_data_processing():
    """演示数据处理功能"""
    print("=== 数据处理演示 ===")
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 生成数据
    print("1. 生成股票数据...")
    df = processor.generate_realistic_data(symbol='DEMO', length=1000)
    print(f"   原始数据形状: {df.shape}")
    print(f"   数据列: {list(df.columns)}")
    
    # 添加技术指标
    print("2. 添加技术指标...")
    df_with_indicators = processor.add_technical_indicators(df)
    print(f"   添加指标后形状: {df_with_indicators.shape}")
    print(f"   新增指标数量: {len(df_with_indicators.columns) - len(df.columns)}")
    
    # 特征归一化
    print("3. 特征归一化...")
    df_normalized, norm_params = processor.normalize_features(df_with_indicators)
    print(f"   归一化参数数量: {len(norm_params)}")
    
    # 数据分割
    print("4. 数据分割...")
    train_df, val_df, test_df = processor.split_data(df_normalized)
    print(f"   训练集: {len(train_df)}, 验证集: {len(val_df)}, 测试集: {len(test_df)}")
    
    # 特征重要性
    print("5. 特征重要性分析...")
    importance = processor.get_feature_importance(df_with_indicators)
    print("   前10个重要特征:")
    print(importance.head(10))
    
    # 创建序列数据
    print("6. 创建时间序列...")
    sequences, targets = processor.create_sequences(df_normalized, sequence_length=30)
    print(f"   序列形状: {sequences.shape}, 目标形状: {targets.shape}")
    
    print("\n✓ 数据处理演示完成")
    return df_with_indicators, df_normalized

if __name__ == "__main__":
    demo_data_processing()