"""
持仓跟踪器
跟踪和管理基金持仓信息，支持盈利回撤监控
"""

import logging
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import asdict
from collections import defaultdict

from .data_structures import PositionInfo, Transaction, DrawdownCheckResult, SellTriggerResult


class PositionTracker:
    """
    @class PositionTracker
    @brief 持仓跟踪器
    @details 跟踪基金持仓，监控盈利回撤，触发卖出信号
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 持仓数据
        self.positions = {}  # {fund_code: PositionInfo}
        self.transaction_history = []  # List[Transaction]
        self.profit_peaks = {}  # {fund_code: max_profit_info}
        
        # 配置参数
        self.drawdown_threshold = 0.20  # 20%回撤阈值
        self.min_profit_for_monitoring = 0.05  # 5%最小盈利才开始监控
        self.stop_loss_threshold = -0.10  # 10%止损阈值
        
        # 性能优化
        self._lock = threading.RLock()  # 线程安全
        self._cache = {}  # 计算缓存
        self._cache_ttl = 60  # 缓存1分钟
        
        # 监控状态
        self._monitoring_enabled = True
        self._last_check_time = datetime.now()
        
    def update_position(self, fund_code: str, shares: float, current_price: float,
                       transaction_type: str = 'update') -> bool:
        """
        @brief 更新持仓信息
        @param fund_code: 基金代码
        @param shares: 持仓份额
        @param current_price: 当前价格
        @param transaction_type: 交易类型
        @return: 更新是否成功
        """
        try:
            with self._lock:
                now = datetime.now()
                
                if fund_code in self.positions:
                    # 更新现有持仓
                    position = self.positions[fund_code]
                    old_shares = position.shares
                    
                    # 计算新的平均成本（如果是买入）
                    if transaction_type == 'buy' and shares > 0:
                        total_cost = (position.shares * position.average_cost + 
                                    shares * current_price)
                        total_shares = position.shares + shares
                        new_avg_cost = total_cost / total_shares if total_shares > 0 else current_price
                        position.shares = total_shares
                        position.average_cost = new_avg_cost
                    elif transaction_type == 'sell' and shares > 0:
                        position.shares = max(0, position.shares - shares)
                    else:
                        position.shares = shares
                    
                    # 更新价格和盈亏
                    position.current_price = current_price
                    position.last_update = now
                    position.holding_days = (now - position.entry_date).days
                    
                    # 如果份额为0，移除持仓
                    if position.shares <= 0:
                        del self.positions[fund_code]
                        if fund_code in self.profit_peaks:
                            del self.profit_peaks[fund_code]
                        self.logger.info(f"移除持仓: {fund_code}")
                        return True
                    
                else:
                    # 创建新持仓
                    if shares <= 0:
                        return False
                        
                    position = PositionInfo(
                        fund_code=fund_code,
                        shares=shares,
                        average_cost=current_price,
                        current_price=current_price,
                        unrealized_pnl=0.0,
                        unrealized_pnl_pct=0.0,
                        max_profit=0.0,
                        max_profit_pct=0.0,
                        current_drawdown=0.0,
                        current_drawdown_pct=0.0,
                        last_update=now,
                        entry_date=now,
                        holding_days=0
                    )
                    self.positions[fund_code] = position
                    self.logger.info(f"创建新持仓: {fund_code}, 份额: {shares}")
                
                # 更新盈亏计算
                self._update_pnl_calculations(fund_code)
                
                # 记录交易
                if transaction_type in ['buy', 'sell']:
                    self._record_transaction(fund_code, transaction_type, shares, current_price)
                
                return True
                
        except Exception as e:
            self.logger.error(f"更新持仓失败 {fund_code}: {str(e)}")
            return False
    
    def _update_pnl_calculations(self, fund_code: str):
        """更新盈亏计算"""
        try:
            if fund_code not in self.positions:
                return
                
            position = self.positions[fund_code]
            
            # 计算未实现盈亏
            position.unrealized_pnl = (position.current_price - position.average_cost) * position.shares
            position.unrealized_pnl_pct = (position.current_price / position.average_cost - 1) if position.average_cost > 0 else 0
            
            # 更新最高盈利记录
            if position.unrealized_pnl_pct > position.max_profit_pct:
                position.max_profit = position.unrealized_pnl
                position.max_profit_pct = position.unrealized_pnl_pct
                
                # 更新盈利峰值记录
                self.profit_peaks[fund_code] = {
                    'max_profit_pct': position.max_profit_pct,
                    'peak_time': datetime.now(),
                    'peak_price': position.current_price
                }
            
            # 计算当前回撤
            if position.max_profit_pct > 0:
                position.current_drawdown = position.max_profit - position.unrealized_pnl
                position.current_drawdown_pct = (position.max_profit_pct - position.unrealized_pnl_pct) / position.max_profit_pct
            else:
                position.current_drawdown = 0
                position.current_drawdown_pct = 0
                
        except Exception as e:
            self.logger.error(f"盈亏计算失败 {fund_code}: {str(e)}")
    
    def check_drawdown_trigger(self, fund_code: str) -> DrawdownCheckResult:
        """
        @brief 检查回撤触发条件
        @param fund_code: 基金代码
        @return: 回撤检查结果
        """
        try:
            if fund_code not in self.positions:
                return DrawdownCheckResult(
                    fund_code=fund_code,
                    current_profit_pct=0.0,
                    max_profit_pct=0.0,
                    drawdown_from_peak=0.0,
                    drawdown_threshold=self.drawdown_threshold,
                    should_sell=False,
                    sell_reason="无持仓",
                    recommended_sell_ratio=0.0,
                    urgency_level='low',
                    check_time=datetime.now()
                )
            
            position = self.positions[fund_code]
            
            # 检查是否达到监控条件
            if position.max_profit_pct < self.min_profit_for_monitoring:
                return DrawdownCheckResult(
                    fund_code=fund_code,
                    current_profit_pct=position.unrealized_pnl_pct,
                    max_profit_pct=position.max_profit_pct,
                    drawdown_from_peak=position.current_drawdown_pct,
                    drawdown_threshold=self.drawdown_threshold,
                    should_sell=False,
                    sell_reason="盈利未达到监控阈值",
                    recommended_sell_ratio=0.0,
                    urgency_level='low',
                    check_time=datetime.now()
                )
            
            # 检查回撤触发条件
            should_sell = position.current_drawdown_pct >= self.drawdown_threshold
            
            # 确定卖出比例和紧急程度
            if should_sell:
                if position.current_drawdown_pct >= 0.30:  # 30%回撤
                    sell_ratio = 1.0  # 全部卖出
                    urgency = 'critical'
                    reason = f"回撤{position.current_drawdown_pct:.1%}，全部卖出"
                elif position.current_drawdown_pct >= 0.25:  # 25%回撤
                    sell_ratio = 0.8  # 卖出80%
                    urgency = 'high'
                    reason = f"回撤{position.current_drawdown_pct:.1%}，大部分卖出"
                else:  # 20%回撤
                    sell_ratio = 0.5  # 卖出50%
                    urgency = 'medium'
                    reason = f"回撤{position.current_drawdown_pct:.1%}，部分卖出"
            else:
                sell_ratio = 0.0
                urgency = 'low'
                reason = "回撤未达到触发阈值"
            
            return DrawdownCheckResult(
                fund_code=fund_code,
                current_profit_pct=position.unrealized_pnl_pct,
                max_profit_pct=position.max_profit_pct,
                drawdown_from_peak=position.current_drawdown_pct,
                drawdown_threshold=self.drawdown_threshold,
                should_sell=should_sell,
                sell_reason=reason,
                recommended_sell_ratio=sell_ratio,
                urgency_level=urgency,
                check_time=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"回撤检查失败 {fund_code}: {str(e)}")
            return DrawdownCheckResult(
                fund_code=fund_code,
                current_profit_pct=0.0,
                max_profit_pct=0.0,
                drawdown_from_peak=0.0,
                drawdown_threshold=self.drawdown_threshold,
                should_sell=False,
                sell_reason=f"检查失败: {str(e)}",
                recommended_sell_ratio=0.0,
                urgency_level='critical',
                check_time=datetime.now()
            )
    
    def check_all_positions(self) -> List[DrawdownCheckResult]:
        """检查所有持仓的回撤情况"""
        results = []
        
        try:
            with self._lock:
                for fund_code in list(self.positions.keys()):
                    result = self.check_drawdown_trigger(fund_code)
                    results.append(result)
                    
                self._last_check_time = datetime.now()
                
        except Exception as e:
            self.logger.error(f"批量回撤检查失败: {str(e)}")
        
        return results
    
    def get_position_info(self, fund_code: str) -> Optional[PositionInfo]:
        """获取持仓信息"""
        with self._lock:
            return self.positions.get(fund_code)
    
    def get_all_positions(self) -> Dict[str, PositionInfo]:
        """获取所有持仓信息"""
        with self._lock:
            return self.positions.copy()
    
    def _record_transaction(self, fund_code: str, transaction_type: str, 
                          shares: float, price: float):
        """记录交易"""
        try:
            transaction = Transaction(
                transaction_id=f"{fund_code}_{transaction_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                fund_code=fund_code,
                transaction_type=transaction_type,
                shares=shares,
                price=price,
                amount=shares * price,
                commission=0.0,  # 简化处理
                timestamp=datetime.now(),
                reason="系统交易",
                related_signal=None
            )
            
            self.transaction_history.append(transaction)
            
            # 限制历史记录数量
            if len(self.transaction_history) > 1000:
                self.transaction_history = self.transaction_history[-800:]
                
        except Exception as e:
            self.logger.error(f"记录交易失败: {str(e)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取绩效摘要"""
        try:
            with self._lock:
                if not self.positions:
                    return {'message': '无持仓数据'}
                
                total_value = 0
                total_cost = 0
                total_pnl = 0
                positions_with_profit = 0
                max_drawdown_position = None
                max_drawdown = 0
                
                for position in self.positions.values():
                    position_value = position.shares * position.current_price
                    position_cost = position.shares * position.average_cost
                    
                    total_value += position_value
                    total_cost += position_cost
                    total_pnl += position.unrealized_pnl
                    
                    if position.unrealized_pnl > 0:
                        positions_with_profit += 1
                    
                    if position.current_drawdown_pct > max_drawdown:
                        max_drawdown = position.current_drawdown_pct
                        max_drawdown_position = position.fund_code
                
                return {
                    'total_positions': len(self.positions),
                    'total_value': total_value,
                    'total_cost': total_cost,
                    'total_pnl': total_pnl,
                    'total_pnl_pct': (total_value / total_cost - 1) if total_cost > 0 else 0,
                    'profitable_positions': positions_with_profit,
                    'win_rate': positions_with_profit / len(self.positions),
                    'max_drawdown_position': max_drawdown_position,
                    'max_drawdown_pct': max_drawdown,
                    'last_update': self._last_check_time.isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"绩效摘要计算失败: {str(e)}")
            return {'error': str(e)}
    
    def export_data(self) -> Dict[str, Any]:
        """导出数据"""
        try:
            with self._lock:
                return {
                    'positions': {code: asdict(pos) for code, pos in self.positions.items()},
                    'profit_peaks': self.profit_peaks.copy(),
                    'transaction_history': [asdict(t) for t in self.transaction_history[-100:]],  # 最近100笔
                    'export_time': datetime.now().isoformat()
                }
        except Exception as e:
            self.logger.error(f"数据导出失败: {str(e)}")
            return {'error': str(e)}
    
    def clear_cache(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self.logger.info("持仓跟踪器缓存已清空")
