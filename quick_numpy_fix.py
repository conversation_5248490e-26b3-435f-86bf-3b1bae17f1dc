#!/usr/bin/env python3
"""
快速修复NumPy兼容性问题
解决 'numpy' has no attribute 'MachAr' 错误
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout.strip():
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stderr:
            print(f"错误详情: {e.stderr}")
        return False

def check_numpy_version():
    """检查numpy版本"""
    try:
        import numpy
        print(f"当前NumPy版本: {numpy.__version__}")
        return numpy.__version__
    except ImportError:
        print("NumPy未安装")
        return None

def quick_fix():
    """快速修复方案"""
    print("=== 快速修复NumPy兼容性问题 ===")
    
    # 检查当前版本
    version = check_numpy_version()
    
    if version and version.startswith('2.'):
        print("检测到NumPy 2.x版本，需要降级...")
        
        # 降级到1.x版本
        commands = [
            f"{sys.executable} -m pip install 'numpy<2.0' --force-reinstall",
            f"{sys.executable} -m pip install 'statsmodels<0.15' --force-reinstall",  # 使用兼容版本
            f"{sys.executable} -m pip install pandas --force-reinstall",
            f"{sys.executable} -m pip install stable-baselines3 --force-reinstall"
        ]
        
        for cmd in commands:
            success = run_command(cmd)
            if not success:
                print(f"命令失败，但继续执行...")
    
    # 验证修复
    print("\n=== 验证修复结果 ===")
    try:
        import numpy
        print(f"✓ NumPy版本: {numpy.__version__}")
        
        import statsmodels
        print(f"✓ statsmodels版本: {statsmodels.__version__}")
        
        # 测试MachAr问题
        try:
            from statsmodels.tools.numdiff import approx_fprime
            print("✓ statsmodels导入成功")
        except Exception as e:
            print(f"✗ statsmodels仍有问题: {e}")
            
        print("🎉 修复完成！")
        return True
        
    except Exception as e:
        print(f"✗ 修复失败: {e}")
        return False

def create_simple_test():
    """创建简单测试脚本"""
    test_content = '''#!/usr/bin/env python3
"""
简化版股票交易环境测试
避免复杂依赖问题
"""

import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from stable_baselines3 import SAC
from stable_baselines3.common.env_checker import check_env
import warnings
warnings.filterwarnings('ignore')

class SimpleStockEnv(gym.Env):
    """简化的股票交易环境"""
    
    def __init__(self):
        super().__init__()
        
        # 状态空间：[价格, 持仓, 余额]
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(3,), dtype=np.float32
        )
        
        # 动作空间：[买入, 持有, 卖出]
        self.action_space = spaces.Discrete(3)
        
        # 初始化
        self.reset()
        
    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        
        self.balance = 10000
        self.shares = 0
        self.price = 100
        self.step_count = 0
        
        obs = self._get_obs()
        info = {}
        return obs, info
        
    def _get_obs(self):
        return np.array([
            self.price / 200,  # 归一化价格
            self.shares / 100,  # 归一化持仓
            self.balance / 20000  # 归一化余额
        ], dtype=np.float32)
        
    def step(self, action):
        # 更新价格（随机游走）
        self.price *= (1 + np.random.normal(0, 0.02))
        self.price = max(self.price, 1)
        
        # 执行动作
        if action == 0 and self.balance >= self.price:  # 买入
            shares_to_buy = int(self.balance / self.price)
            self.shares += shares_to_buy
            self.balance -= shares_to_buy * self.price
            
        elif action == 2 and self.shares > 0:  # 卖出
            self.balance += self.shares * self.price
            self.shares = 0
            
        # 计算奖励
        net_worth = self.balance + self.shares * self.price
        reward = (net_worth - 10000) / 10000
        
        self.step_count += 1
        done = self.step_count >= 100
        truncated = False
        
        obs = self._get_obs()
        info = {'net_worth': net_worth}
        
        return obs, reward, done, truncated, info

def test_environment():
    """测试环境"""
    print("=== 测试简化环境 ===")
    
    # 创建环境
    env = SimpleStockEnv()
    
    # 检查环境
    check_env(env)
    print("✓ 环境检查通过")
    
    # 测试重置
    obs, info = env.reset()
    print(f"✓ 初始观测: {obs}")
    
    # 测试步骤
    action = env.action_space.sample()
    obs, reward, done, truncated, info = env.step(action)
    print(f"✓ 步骤测试: 动作={action}, 奖励={reward:.4f}")
    
    # 创建模型
    model = SAC('MlpPolicy', env, verbose=1)
    print("✓ SAC模型创建成功")
    
    # 简单训练
    print("开始简单训练...")
    model.learn(total_timesteps=1000)
    print("✓ 训练完成")
    
    # 测试预测
    obs, _ = env.reset()
    action, _ = model.predict(obs)
    print(f"✓ 预测测试: {action}")
    
    print("🎉 所有测试通过！")

if __name__ == "__main__":
    test_environment()
'''
    
    with open('simple_stock_test.py', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("已创建简化测试脚本: simple_stock_test.py")

def main():
    """主函数"""
    print("NumPy兼容性快速修复工具")
    print("=" * 40)
    
    # 执行修复
    success = quick_fix()
    
    if success:
        print("\n现在可以尝试运行原始脚本:")
        print("python enhanced_rl_stock_base_sb3.py")
    else:
        print("\n如果问题仍然存在，可以:")
        print("1. 重启Python环境")
        print("2. 使用简化版本测试:")
        create_simple_test()
        print("   python simple_stock_test.py")

if __name__ == "__main__":
    main()