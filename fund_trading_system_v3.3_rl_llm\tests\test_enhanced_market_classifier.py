"""
测试增强的市场分类器功能
"""

import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from analyzers.market_classifier import MultiDimensionalMarketClassifier
from core.data_structures import DimensionEvaluationResult


def create_mock_evaluations(trend=0.0, volatility=0.0, liquidity=0.0, 
                           sentiment=0.0, structure=0.0, transition=0.0):
    """创建模拟的维度评估结果"""
    return {
        '趋势': DimensionEvaluationResult(
            dimension_name='趋势', state='neutral', score=trend, confidence=0.8,
            signals=[], data_quality='good', details={}, indicators={}
        ),
        '波动性': DimensionEvaluationResult(
            dimension_name='波动性', state='neutral', score=volatility, confidence=0.8,
            signals=[], data_quality='good', details={}, indicators={}
        ),
        '流动性': DimensionEvaluationResult(
            dimension_name='流动性', state='neutral', score=liquidity, confidence=0.8,
            signals=[], data_quality='good', details={}, indicators={}
        ),
        '情绪': DimensionEvaluationResult(
            dimension_name='情绪', state='neutral', score=sentiment, confidence=0.8,
            signals=[], data_quality='good', details={}, indicators={}
        ),
        '结构': DimensionEvaluationResult(
            dimension_name='结构', state='neutral', score=structure, confidence=0.8,
            signals=[], data_quality='good', details={}, indicators={}
        ),
        '变盘': DimensionEvaluationResult(
            dimension_name='变盘', state='neutral', score=transition, confidence=0.8,
            signals=[], data_quality='good', details={}, indicators={}
        )
    }


def test_enhanced_market_classifier():
    """测试增强的市场分类器"""
    print("🔍 测试增强的市场分类器")
    print("=" * 60)
    
    classifier = MultiDimensionalMarketClassifier()
    
    # 测试案例1：513080的实际情况
    print("1. 测试513080的实际情况（趋势:0.31, 波动性:0.19, 流动性:0.60）:")
    evaluations_513080 = create_mock_evaluations(
        trend=0.31, volatility=0.19, liquidity=0.60, sentiment=0.2, structure=0.4
    )
    
    result = classifier.classify_market(
        evaluations_513080, 
        fund_code='513080', 
        fund_category='developed_equity',
        debug=True
    )
    
    print(f"   分类结果: {result['primary_classification']}")
    print(f"   置信度: {result['classification_confidence']:.2f}")
    print(f"   描述: {result['classification_description']}")
    print(f"   分类原因: {result.get('classification_reason', '无')}")
    
    # 测试案例2：强势突破
    print("\n2. 测试强势突破场景:")
    evaluations_strong = create_mock_evaluations(
        trend=0.8, volatility=0.3, liquidity=0.7, sentiment=0.6, structure=0.7
    )
    
    result = classifier.classify_market(evaluations_strong, debug=True)
    print(f"   分类结果: {result['primary_classification']}")
    print(f"   置信度: {result['classification_confidence']:.2f}")
    
    # 测试案例3：温和上涨（新增分类）
    print("\n3. 测试温和上涨场景:")
    evaluations_mild = create_mock_evaluations(
        trend=0.4, volatility=0.3, liquidity=0.5, sentiment=0.2
    )
    
    result = classifier.classify_market(evaluations_mild, debug=True)
    print(f"   分类结果: {result['primary_classification']}")
    print(f"   置信度: {result['classification_confidence']:.2f}")
    
    # 测试案例4：黄金ETF特殊处理
    print("\n4. 测试黄金ETF特殊处理:")
    evaluations_gold = create_mock_evaluations(
        trend=0.2, volatility=0.5, liquidity=0.4
    )
    
    result = classifier.classify_market(
        evaluations_gold, 
        fund_code='518880', 
        fund_category='gold_etf',
        debug=True
    )
    print(f"   分类结果: {result['primary_classification']}")
    print(f"   置信度: {result['classification_confidence']:.2f}")
    print(f"   原始波动性: {result['original_scores']['波动性']:.2f}")
    print(f"   调整后波动性: {result['adjusted_scores']['波动性']:.2f}")
    
    # 测试案例5：兜底分类
    print("\n5. 测试兜底分类:")
    evaluations_edge = create_mock_evaluations(
        trend=0.35, volatility=0.25, liquidity=0.45
    )
    
    result = classifier.classify_market(evaluations_edge, debug=True)
    print(f"   分类结果: {result['primary_classification']}")
    print(f"   置信度: {result['classification_confidence']:.2f}")
    
    print("\n✅ 增强市场分类器测试完成!")
    return classifier


def test_classification_coverage():
    """测试分类覆盖范围"""
    print("\n📊 测试分类覆盖范围")
    print("-" * 40)
    
    classifier = MultiDimensionalMarketClassifier()
    
    # 测试不同趋势值的分类覆盖
    trend_values = [-0.8, -0.6, -0.4, -0.2, 0.0, 0.2, 0.31, 0.4, 0.6, 0.8]
    
    print("趋势值分类覆盖测试:")
    for trend in trend_values:
        evaluations = create_mock_evaluations(
            trend=trend, volatility=0.3, liquidity=0.5
        )
        
        result = classifier.classify_market(evaluations)
        print(f"   趋势={trend:4.1f}: {result['primary_classification']}")
    
    print("\n波动性分类覆盖测试:")
    volatility_values = [0.1, 0.19, 0.3, 0.5, 0.7, 0.9]
    
    for vol in volatility_values:
        evaluations = create_mock_evaluations(
            trend=0.2, volatility=vol, liquidity=0.5
        )
        
        result = classifier.classify_market(evaluations)
        print(f"   波动性={vol:4.1f}: {result['primary_classification']}")
    
    print("\n✅ 分类覆盖范围测试完成!")


def test_fund_type_adjustments():
    """测试基金类型调整"""
    print("\n🎯 测试基金类型调整")
    print("-" * 40)
    
    classifier = MultiDimensionalMarketClassifier()
    
    # 相同的评估结果，不同的基金类型
    evaluations = create_mock_evaluations(
        trend=0.3, volatility=0.4, liquidity=0.5
    )
    
    fund_types = [
        ('513080', 'developed_equity'),
        ('518880', 'gold_etf'),
        ('520830', 'bond_fund'),
        ('601398', 'bank_stock')
    ]
    
    print("相同评估结果，不同基金类型的分类:")
    for fund_code, fund_category in fund_types:
        result = classifier.classify_market(
            evaluations, 
            fund_code=fund_code, 
            fund_category=fund_category
        )
        
        print(f"   {fund_code} ({fund_category}): {result['primary_classification']}")
        if 'adjusted_scores' in result:
            print(f"     调整后趋势: {result['adjusted_scores']['趋势']:.2f}")
            print(f"     调整后波动性: {result['adjusted_scores']['波动性']:.2f}")
    
    print("\n✅ 基金类型调整测试完成!")


if __name__ == "__main__":
    try:
        classifier = test_enhanced_market_classifier()
        test_classification_coverage()
        test_fund_type_adjustments()
        
        print("\n🎉 所有测试完成!")
        print("✅ 增强的市场分类器已准备就绪")
        print("🔄 现在可以更准确地识别市场状态，减少'未知市场'的出现")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
