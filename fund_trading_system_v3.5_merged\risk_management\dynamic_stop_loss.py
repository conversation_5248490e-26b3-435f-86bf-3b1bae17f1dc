"""
Dynamic Stop Loss Strategy Implementation

This module implements various dynamic stop loss strategies including:
- ATR-based dynamic stop loss
- Trend-following stop loss
- Time-decay stop loss
- Multi-layer stop loss strategies
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Try to import optional dependencies
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False


class StopLossType(Enum):
    """Types of stop loss strategies"""
    FIXED = "fixed"
    ATR_BASED = "atr_based"
    TREND_FOLLOWING = "trend_following"
    TIME_DECAY = "time_decay"
    MULTI_LAYER = "multi_layer"
    VOLATILITY_ADJUSTED = "volatility_adjusted"


@dataclass
class StopLossLevel:
    """Stop loss level configuration"""
    price: float
    type: StopLossType
    confidence: float
    created_at: datetime
    last_updated: datetime
    metadata: Dict[str, Any]


@dataclass
class StopLossResult:
    """Result of stop loss calculation"""
    stop_price: float
    stop_type: StopLossType
    confidence: float
    risk_percentage: float
    reasoning: str
    metadata: Dict[str, Any]


class DynamicStopLossManager:
    """Dynamic stop loss manager with multiple strategies"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._get_default_config()
        self.logger = logging.getLogger("DynamicStopLoss")
        
        # Active stop loss levels
        self.active_stops: Dict[str, List[StopLossLevel]] = {}
        
        # Performance tracking
        self.stop_triggers = 0
        self.successful_stops = 0
        self.false_stops = 0
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'atr_period': 14,
            'atr_multiplier': 2.0,
            'trend_period': 20,
            'volatility_period': 20,
            'time_decay_hours': 24,
            'max_risk_percentage': 0.05,  # 5% max risk
            'min_profit_target': 0.02,    # 2% min profit target
            'enable_multi_layer': True,
            'layer_spacing': 0.01,        # 1% spacing between layers
            'max_layers': 3
        }
    
    def calculate_stop_loss(self, symbol: str, current_price: float, 
                          position_type: str, entry_price: float,
                          market_data: Dict[str, Any],
                          stop_type: StopLossType = StopLossType.ATR_BASED) -> StopLossResult:
        """Calculate dynamic stop loss level"""
        try:
            if stop_type == StopLossType.ATR_BASED:
                return self._calculate_atr_stop(symbol, current_price, position_type, 
                                              entry_price, market_data)
            elif stop_type == StopLossType.TREND_FOLLOWING:
                return self._calculate_trend_stop(symbol, current_price, position_type,
                                                entry_price, market_data)
            elif stop_type == StopLossType.TIME_DECAY:
                return self._calculate_time_decay_stop(symbol, current_price, position_type,
                                                     entry_price, market_data)
            elif stop_type == StopLossType.VOLATILITY_ADJUSTED:
                return self._calculate_volatility_stop(symbol, current_price, position_type,
                                                     entry_price, market_data)
            elif stop_type == StopLossType.MULTI_LAYER:
                return self._calculate_multi_layer_stop(symbol, current_price, position_type,
                                                       entry_price, market_data)
            else:
                return self._calculate_fixed_stop(symbol, current_price, position_type,
                                                entry_price, market_data)
                
        except Exception as e:
            self.logger.error(f"Stop loss calculation failed: {e}")
            return self._create_fallback_stop(current_price, position_type, entry_price)
    
    def _calculate_atr_stop(self, symbol: str, current_price: float, 
                           position_type: str, entry_price: float,
                           market_data: Dict[str, Any]) -> StopLossResult:
        """Calculate ATR-based dynamic stop loss"""
        try:
            # Get price data
            price_data = market_data.get('price_history', [])
            if len(price_data) < self.config['atr_period']:
                return self._calculate_fixed_stop(symbol, current_price, position_type, 
                                                entry_price, market_data)
            
            # Calculate ATR
            atr = self._calculate_atr(price_data)
            atr_multiplier = self.config['atr_multiplier']
            
            # Calculate stop loss based on position type
            if position_type.lower() == 'long':
                stop_price = current_price - (atr * atr_multiplier)
                # Ensure stop is not above entry price
                stop_price = min(stop_price, entry_price * 0.95)
            else:  # short position
                stop_price = current_price + (atr * atr_multiplier)
                # Ensure stop is not below entry price
                stop_price = max(stop_price, entry_price * 1.05)
            
            # Calculate risk percentage
            risk_pct = abs(stop_price - entry_price) / entry_price
            
            # Adjust if risk is too high
            max_risk = self.config['max_risk_percentage']
            if risk_pct > max_risk:
                if position_type.lower() == 'long':
                    stop_price = entry_price * (1 - max_risk)
                else:
                    stop_price = entry_price * (1 + max_risk)
                risk_pct = max_risk
            
            confidence = min(0.9, 0.5 + (atr / current_price) * 10)
            
            return StopLossResult(
                stop_price=stop_price,
                stop_type=StopLossType.ATR_BASED,
                confidence=confidence,
                risk_percentage=risk_pct,
                reasoning=f"ATR-based stop: ATR={atr:.4f}, multiplier={atr_multiplier}",
                metadata={
                    'atr': atr,
                    'atr_multiplier': atr_multiplier,
                    'original_risk': abs(current_price - (current_price - atr * atr_multiplier)) / entry_price
                }
            )
            
        except Exception as e:
            self.logger.warning(f"ATR stop calculation failed: {e}")
            return self._calculate_fixed_stop(symbol, current_price, position_type, 
                                            entry_price, market_data)
    
    def _calculate_trend_stop(self, symbol: str, current_price: float,
                            position_type: str, entry_price: float,
                            market_data: Dict[str, Any]) -> StopLossResult:
        """Calculate trend-following stop loss"""
        try:
            price_data = market_data.get('price_history', [])
            if len(price_data) < self.config['trend_period']:
                return self._calculate_fixed_stop(symbol, current_price, position_type,
                                                entry_price, market_data)
            
            # Calculate moving average for trend
            closes = [p.get('close', current_price) for p in price_data[-self.config['trend_period']:]]
            ma = np.mean(closes)
            
            # Calculate trend strength
            trend_slope = (closes[-1] - closes[0]) / len(closes)
            trend_strength = abs(trend_slope) / np.std(closes)
            
            if position_type.lower() == 'long':
                # For long positions, use MA as dynamic support
                base_stop = ma * 0.98  # 2% below MA
                # Adjust based on trend strength
                trend_adjustment = trend_strength * 0.01
                stop_price = base_stop * (1 - trend_adjustment)
                stop_price = min(stop_price, entry_price * 0.95)
            else:
                # For short positions, use MA as dynamic resistance
                base_stop = ma * 1.02  # 2% above MA
                trend_adjustment = trend_strength * 0.01
                stop_price = base_stop * (1 + trend_adjustment)
                stop_price = max(stop_price, entry_price * 1.05)
            
            risk_pct = abs(stop_price - entry_price) / entry_price
            confidence = min(0.9, 0.6 + trend_strength * 0.3)
            
            return StopLossResult(
                stop_price=stop_price,
                stop_type=StopLossType.TREND_FOLLOWING,
                confidence=confidence,
                risk_percentage=risk_pct,
                reasoning=f"Trend-following stop: MA={ma:.4f}, trend_strength={trend_strength:.4f}",
                metadata={
                    'moving_average': ma,
                    'trend_slope': trend_slope,
                    'trend_strength': trend_strength
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Trend stop calculation failed: {e}")
            return self._calculate_fixed_stop(symbol, current_price, position_type,
                                            entry_price, market_data)
    
    def _calculate_time_decay_stop(self, symbol: str, current_price: float,
                                 position_type: str, entry_price: float,
                                 market_data: Dict[str, Any]) -> StopLossResult:
        """Calculate time-decay stop loss"""
        try:
            entry_time = market_data.get('entry_time', datetime.now())
            current_time = datetime.now()
            
            # Calculate time elapsed
            time_elapsed = (current_time - entry_time).total_seconds() / 3600  # hours
            decay_period = self.config['time_decay_hours']
            
            # Calculate decay factor (0 to 1)
            decay_factor = min(1.0, time_elapsed / decay_period)
            
            # Base stop loss (conservative)
            base_risk = 0.03  # 3%
            
            # Tighten stop loss over time
            if position_type.lower() == 'long':
                # Start with 3% risk, tighten to 1% over time
                current_risk = base_risk * (1 - decay_factor * 0.67)  # 3% -> 1%
                stop_price = entry_price * (1 - current_risk)
            else:
                current_risk = base_risk * (1 - decay_factor * 0.67)
                stop_price = entry_price * (1 + current_risk)
            
            confidence = 0.7 + decay_factor * 0.2  # Increase confidence over time
            
            return StopLossResult(
                stop_price=stop_price,
                stop_type=StopLossType.TIME_DECAY,
                confidence=confidence,
                risk_percentage=current_risk,
                reasoning=f"Time-decay stop: {time_elapsed:.1f}h elapsed, decay_factor={decay_factor:.3f}",
                metadata={
                    'time_elapsed_hours': time_elapsed,
                    'decay_factor': decay_factor,
                    'base_risk': base_risk
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Time decay stop calculation failed: {e}")
            return self._calculate_fixed_stop(symbol, current_price, position_type,
                                            entry_price, market_data)

    def _calculate_volatility_stop(self, symbol: str, current_price: float,
                                 position_type: str, entry_price: float,
                                 market_data: Dict[str, Any]) -> StopLossResult:
        """Calculate volatility-adjusted stop loss"""
        try:
            price_data = market_data.get('price_history', [])
            if len(price_data) < self.config['volatility_period']:
                return self._calculate_fixed_stop(symbol, current_price, position_type,
                                                entry_price, market_data)

            # Calculate volatility
            closes = [p.get('close', current_price) for p in price_data[-self.config['volatility_period']:]]
            returns = np.diff(closes) / closes[:-1]
            volatility = np.std(returns)

            # Adjust stop based on volatility
            base_risk = 0.02  # 2% base risk
            volatility_multiplier = 1 + (volatility * 50)  # Scale volatility
            adjusted_risk = base_risk * volatility_multiplier

            # Cap maximum risk
            adjusted_risk = min(adjusted_risk, self.config['max_risk_percentage'])

            if position_type.lower() == 'long':
                stop_price = entry_price * (1 - adjusted_risk)
            else:
                stop_price = entry_price * (1 + adjusted_risk)

            confidence = min(0.9, 0.5 + (1 / (1 + volatility * 10)))

            return StopLossResult(
                stop_price=stop_price,
                stop_type=StopLossType.VOLATILITY_ADJUSTED,
                confidence=confidence,
                risk_percentage=adjusted_risk,
                reasoning=f"Volatility-adjusted stop: vol={volatility:.4f}, risk={adjusted_risk:.3f}",
                metadata={
                    'volatility': volatility,
                    'base_risk': base_risk,
                    'volatility_multiplier': volatility_multiplier
                }
            )

        except Exception as e:
            self.logger.warning(f"Volatility stop calculation failed: {e}")
            return self._calculate_fixed_stop(symbol, current_price, position_type,
                                            entry_price, market_data)

    def _calculate_multi_layer_stop(self, symbol: str, current_price: float,
                                  position_type: str, entry_price: float,
                                  market_data: Dict[str, Any]) -> StopLossResult:
        """Calculate multi-layer stop loss strategy"""
        try:
            if not self.config['enable_multi_layer']:
                return self._calculate_atr_stop(symbol, current_price, position_type,
                                              entry_price, market_data)

            layers = []
            layer_spacing = self.config['layer_spacing']
            max_layers = self.config['max_layers']

            # Create multiple stop layers
            for i in range(max_layers):
                layer_risk = (i + 1) * layer_spacing

                if position_type.lower() == 'long':
                    layer_price = entry_price * (1 - layer_risk)
                else:
                    layer_price = entry_price * (1 + layer_risk)

                layers.append({
                    'price': layer_price,
                    'risk': layer_risk,
                    'weight': 1.0 / (i + 1)  # Higher weight for closer stops
                })

            # Select primary stop (first layer)
            primary_stop = layers[0]

            confidence = 0.8  # High confidence for multi-layer approach

            return StopLossResult(
                stop_price=primary_stop['price'],
                stop_type=StopLossType.MULTI_LAYER,
                confidence=confidence,
                risk_percentage=primary_stop['risk'],
                reasoning=f"Multi-layer stop: {len(layers)} layers, primary at {primary_stop['risk']:.1%}",
                metadata={
                    'layers': layers,
                    'primary_layer': 0,
                    'total_layers': len(layers)
                }
            )

        except Exception as e:
            self.logger.warning(f"Multi-layer stop calculation failed: {e}")
            return self._calculate_fixed_stop(symbol, current_price, position_type,
                                            entry_price, market_data)

    def _calculate_fixed_stop(self, symbol: str, current_price: float,
                            position_type: str, entry_price: float,
                            market_data: Dict[str, Any]) -> StopLossResult:
        """Calculate fixed percentage stop loss"""
        fixed_risk = 0.03  # 3% fixed risk

        if position_type.lower() == 'long':
            stop_price = entry_price * (1 - fixed_risk)
        else:
            stop_price = entry_price * (1 + fixed_risk)

        return StopLossResult(
            stop_price=stop_price,
            stop_type=StopLossType.FIXED,
            confidence=0.6,
            risk_percentage=fixed_risk,
            reasoning=f"Fixed stop loss: {fixed_risk:.1%} risk",
            metadata={'fixed_risk': fixed_risk}
        )

    def _create_fallback_stop(self, current_price: float, position_type: str,
                            entry_price: float) -> StopLossResult:
        """Create fallback stop loss when calculations fail"""
        fallback_risk = 0.05  # 5% fallback risk

        if position_type.lower() == 'long':
            stop_price = entry_price * (1 - fallback_risk)
        else:
            stop_price = entry_price * (1 + fallback_risk)

        return StopLossResult(
            stop_price=stop_price,
            stop_type=StopLossType.FIXED,
            confidence=0.3,
            risk_percentage=fallback_risk,
            reasoning="Fallback stop loss due to calculation error",
            metadata={'fallback': True}
        )

    def _calculate_atr(self, price_data: List[Dict[str, Any]]) -> float:
        """Calculate Average True Range"""
        try:
            if TALIB_AVAILABLE and len(price_data) >= self.config['atr_period']:
                # Use TA-Lib if available
                highs = np.array([p.get('high', 0) for p in price_data])
                lows = np.array([p.get('low', 0) for p in price_data])
                closes = np.array([p.get('close', 0) for p in price_data])

                atr_values = talib.ATR(highs, lows, closes, timeperiod=self.config['atr_period'])
                return atr_values[-1] if not np.isnan(atr_values[-1]) else self._calculate_simple_atr(price_data)
            else:
                return self._calculate_simple_atr(price_data)

        except Exception as e:
            self.logger.warning(f"ATR calculation failed: {e}")
            return self._calculate_simple_atr(price_data)

    def _calculate_simple_atr(self, price_data: List[Dict[str, Any]]) -> float:
        """Calculate simple ATR without TA-Lib"""
        if len(price_data) < 2:
            return 0.01  # Default 1% ATR

        true_ranges = []
        for i in range(1, len(price_data)):
            current = price_data[i]
            previous = price_data[i-1]

            high = current.get('high', current.get('close', 0))
            low = current.get('low', current.get('close', 0))
            prev_close = previous.get('close', 0)

            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)

            true_range = max(tr1, tr2, tr3)
            true_ranges.append(true_range)

        # Return average of true ranges
        return np.mean(true_ranges[-self.config['atr_period']:]) if true_ranges else 0.01

    def update_stop_loss(self, symbol: str, new_stop: StopLossResult) -> bool:
        """Update stop loss for a symbol"""
        try:
            if symbol not in self.active_stops:
                self.active_stops[symbol] = []

            # Create stop loss level
            stop_level = StopLossLevel(
                price=new_stop.stop_price,
                type=new_stop.stop_type,
                confidence=new_stop.confidence,
                created_at=datetime.now(),
                last_updated=datetime.now(),
                metadata=new_stop.metadata
            )

            # Add to active stops
            self.active_stops[symbol].append(stop_level)

            # Keep only recent stops (last 10)
            if len(self.active_stops[symbol]) > 10:
                self.active_stops[symbol] = self.active_stops[symbol][-10:]

            self.logger.info(f"Stop loss updated for {symbol}: {new_stop.stop_price:.4f}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to update stop loss for {symbol}: {e}")
            return False

    def check_stop_trigger(self, symbol: str, current_price: float,
                          position_type: str) -> Tuple[bool, Optional[StopLossLevel]]:
        """Check if stop loss should be triggered"""
        if symbol not in self.active_stops or not self.active_stops[symbol]:
            return False, None

        # Get most recent stop
        latest_stop = self.active_stops[symbol][-1]

        # Check trigger condition
        if position_type.lower() == 'long':
            triggered = current_price <= latest_stop.price
        else:
            triggered = current_price >= latest_stop.price

        if triggered:
            self.stop_triggers += 1
            self.logger.warning(f"Stop loss triggered for {symbol}: {current_price} vs {latest_stop.price}")

        return triggered, latest_stop if triggered else None

    def get_statistics(self) -> Dict[str, Any]:
        """Get stop loss performance statistics"""
        total_stops = self.stop_triggers
        success_rate = self.successful_stops / max(total_stops, 1)

        return {
            'total_stop_triggers': self.stop_triggers,
            'successful_stops': self.successful_stops,
            'false_stops': self.false_stops,
            'success_rate': success_rate,
            'active_symbols': len(self.active_stops),
            'total_active_stops': sum(len(stops) for stops in self.active_stops.values())
        }
