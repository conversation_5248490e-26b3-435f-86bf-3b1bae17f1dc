"""
评估器模块测试
测试六大维度评估器的功能
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from evaluators.trend_evaluator import TrendEvaluator
from evaluators.volatility_evaluator import VolatilityEvaluator
from evaluators.liquidity_evaluator import LiquidityEvaluator
from evaluators.sentiment_evaluator import SentimentEvaluator
from evaluators.structural_evaluator import StructuralEvaluator
from evaluators.transition_evaluator import TransitionEvaluator
from core.enums import TrendState, VolatilityState


class TestTrendEvaluator(unittest.TestCase):
    """测试趋势评估器"""
    
    def setUp(self):
        self.evaluator = TrendEvaluator()
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        self.assertEqual(self.evaluator.name, "TrendEvaluator")
        self.assertIsNotNone(self.evaluator.logger)
    
    def test_evaluate_with_mock_data(self):
        """测试使用模拟数据进行评估"""
        mock_data = {
            'fund_code': '513500',
            'price_data': {
                'current_price': 100,
                'change_rate': 0.02
            },
            'technical_data': {
                'ma5': 102,
                'ma20': 98,
                'ma60': 95,
                'rsi': 65,
                'macd': 0.5,
                'macd_signal': 0.3
            }
        }
        
        result = self.evaluator.evaluate(mock_data)
        
        # 验证返回结果的基本结构
        self.assertEqual(result.dimension_name, "趋势")
        self.assertIsInstance(result.state, TrendState)
        self.assertIsInstance(result.score, float)
        self.assertIsInstance(result.confidence, float)
        self.assertIsInstance(result.signals, list)
        self.assertIn('ma_trend', result.details)


class TestVolatilityEvaluator(unittest.TestCase):
    """测试波动性评估器"""
    
    def setUp(self):
        self.evaluator = VolatilityEvaluator()
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        self.assertEqual(self.evaluator.name, "VolatilityEvaluator")
        self.assertIsNotNone(self.evaluator.logger)
    
    def test_evaluate_with_mock_data(self):
        """测试使用模拟数据进行评估"""
        mock_data = {
            'fund_code': '513500',
            'price_data': {
                'volatility': 0.15,
                'atr': 2.5,
                'price_changes': [0.01, -0.02, 0.015, -0.01, 0.02]
            }
        }
        
        result = self.evaluator.evaluate(mock_data)
        
        # 验证返回结果的基本结构
        self.assertEqual(result.dimension_name, "波动性")
        self.assertIsInstance(result.state, VolatilityState)
        self.assertIsInstance(result.score, float)
        self.assertIsInstance(result.confidence, float)


class TestLiquidityEvaluator(unittest.TestCase):
    """测试流动性评估器"""
    
    def setUp(self):
        self.evaluator = LiquidityEvaluator()
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        self.assertEqual(self.evaluator.name, "LiquidityEvaluator")


class TestSentimentEvaluator(unittest.TestCase):
    """测试情绪评估器"""
    
    def setUp(self):
        self.evaluator = SentimentEvaluator()
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        self.assertEqual(self.evaluator.name, "SentimentEvaluator")


class TestStructuralEvaluator(unittest.TestCase):
    """测试结构评估器"""
    
    def setUp(self):
        self.evaluator = StructuralEvaluator()
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        self.assertEqual(self.evaluator.name, "StructuralEvaluator")


class TestTransitionEvaluator(unittest.TestCase):
    """测试转换评估器"""
    
    def setUp(self):
        self.evaluator = TransitionEvaluator()
    
    def test_evaluator_initialization(self):
        """测试评估器初始化"""
        self.assertEqual(self.evaluator.name, "TransitionEvaluator")


if __name__ == '__main__':
    unittest.main()
