"""
在线学习管理器 - 支持RL模型的增量学习和实时更新
实现在线学习算法、实时数据流处理、性能监控和自动重训练机制
"""

import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Callable, Union
import os
import json
import pickle
import threading
import time
from collections import deque, defaultdict
from dataclasses import dataclass, asdict
from enum import Enum
import queue
import warnings

# 尝试导入可选依赖
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False


class OnlineLearningMode(Enum):
    """在线学习模式"""
    INCREMENTAL = "incremental"  # 增量学习
    ADAPTIVE = "adaptive"        # 自适应学习
    CONTINUOUS = "continuous"    # 连续学习
    TRIGGERED = "triggered"      # 触发式学习


class LearningTrigger(Enum):
    """学习触发条件"""
    PERFORMANCE_DEGRADATION = "performance_degradation"
    DATA_DRIFT = "data_drift"
    TIME_BASED = "time_based"
    MANUAL = "manual"


@dataclass
class OnlineLearningConfig:
    """在线学习配置"""
    mode: OnlineLearningMode = OnlineLearningMode.ADAPTIVE
    learning_rate: float = 0.001
    batch_size: int = 32
    buffer_size: int = 10000
    update_frequency: int = 100  # 每N个样本更新一次
    performance_threshold: float = 0.05  # 性能下降阈值
    drift_threshold: float = 0.1  # 数据漂移阈值
    max_memory_usage: float = 0.8  # 最大内存使用率
    enable_forgetting: bool = True  # 启用遗忘机制
    forgetting_factor: float = 0.95  # 遗忘因子
    validation_split: float = 0.2  # 验证集比例
    early_stopping_patience: int = 10  # 早停耐心值
    save_frequency: int = 1000  # 模型保存频率


@dataclass
class LearningMetrics:
    """学习指标"""
    timestamp: str
    samples_processed: int
    current_loss: float
    average_loss: float
    learning_rate: float
    memory_usage: float
    processing_time: float
    performance_score: float
    drift_score: float
    model_version: int


@dataclass
class OnlineLearningResult:
    """在线学习结果"""
    success: bool
    metrics: LearningMetrics
    model_updated: bool
    trigger_reason: str
    next_update_time: Optional[datetime] = None
    recommendations: List[str] = None
    warnings: List[str] = None


class DataBuffer:
    """数据缓冲区"""
    
    def __init__(self, max_size: int = 10000, enable_forgetting: bool = True, 
                 forgetting_factor: float = 0.95):
        self.max_size = max_size
        self.enable_forgetting = enable_forgetting
        self.forgetting_factor = forgetting_factor
        self.buffer = deque(maxlen=max_size)
        self.weights = deque(maxlen=max_size)
        self.timestamps = deque(maxlen=max_size)
        self.lock = threading.Lock()
        
    def add(self, data: Tuple[np.ndarray, np.ndarray], timestamp: datetime = None):
        """添加数据到缓冲区"""
        with self.lock:
            if timestamp is None:
                timestamp = datetime.now()
                
            self.buffer.append(data)
            self.timestamps.append(timestamp)
            
            # 计算权重（新数据权重更高）
            if self.enable_forgetting and len(self.buffer) > 1:
                # 更新所有权重
                current_time = timestamp
                new_weights = []
                for i, ts in enumerate(self.timestamps):
                    time_diff = (current_time - ts).total_seconds() / 3600  # 小时差
                    weight = self.forgetting_factor ** time_diff
                    new_weights.append(weight)
                self.weights = deque(new_weights, maxlen=self.max_size)
            else:
                self.weights.append(1.0)
    
    def get_batch(self, batch_size: int) -> Tuple[List, List]:
        """获取批次数据"""
        with self.lock:
            if len(self.buffer) < batch_size:
                return list(self.buffer), list(self.weights)
            
            # 根据权重采样
            indices = np.random.choice(
                len(self.buffer), 
                size=batch_size, 
                replace=False,
                p=np.array(self.weights) / np.sum(self.weights)
            )
            
            batch_data = [self.buffer[i] for i in indices]
            batch_weights = [self.weights[i] for i in indices]
            
            return batch_data, batch_weights
    
    def size(self) -> int:
        """获取缓冲区大小"""
        return len(self.buffer)
    
    def clear(self):
        """清空缓冲区"""
        with self.lock:
            self.buffer.clear()
            self.weights.clear()
            self.timestamps.clear()


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.performance_history = deque(maxlen=window_size)
        self.loss_history = deque(maxlen=window_size)
        self.baseline_performance = None
        self.lock = threading.Lock()
        
    def update(self, performance_score: float, loss: float):
        """更新性能指标"""
        with self.lock:
            self.performance_history.append(performance_score)
            self.loss_history.append(loss)
            
            if self.baseline_performance is None and len(self.performance_history) >= 10:
                self.baseline_performance = np.mean(list(self.performance_history)[-10:])
    
    def check_degradation(self, threshold: float = 0.05) -> bool:
        """检查性能是否下降"""
        with self.lock:
            if len(self.performance_history) < 10 or self.baseline_performance is None:
                return False
            
            recent_performance = np.mean(list(self.performance_history)[-10:])
            degradation = (self.baseline_performance - recent_performance) / self.baseline_performance
            
            return degradation > threshold
    
    def get_current_performance(self) -> float:
        """获取当前性能"""
        with self.lock:
            if not self.performance_history:
                return 0.0
            return np.mean(list(self.performance_history)[-5:])
    
    def get_average_loss(self) -> float:
        """获取平均损失"""
        with self.lock:
            if not self.loss_history:
                return float('inf')
            return np.mean(list(self.loss_history)[-10:])


class OnlineLearningManager:
    """在线学习管理器"""
    
    def __init__(self, config: OnlineLearningConfig = None):
        self.config = config or OnlineLearningConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化组件
        self.data_buffer = DataBuffer(
            max_size=self.config.buffer_size,
            enable_forgetting=self.config.enable_forgetting,
            forgetting_factor=self.config.forgetting_factor
        )
        self.performance_monitor = PerformanceMonitor()
        
        # 状态管理
        self.model = None
        self.optimizer = None
        self.is_learning = False
        self.samples_processed = 0
        self.model_version = 1
        self.last_update_time = datetime.now()
        
        # 线程管理
        self.learning_thread = None
        self.stop_learning = threading.Event()
        self.data_queue = queue.Queue()
        
        # 性能统计
        self.learning_metrics_history = []
        
        self.logger.info("在线学习管理器初始化完成")

    def set_model(self, model: nn.Module, optimizer: optim.Optimizer = None):
        """设置模型和优化器"""
        self.model = model
        if optimizer is None:
            self.optimizer = optim.Adam(model.parameters(), lr=self.config.learning_rate)
        else:
            self.optimizer = optimizer
        self.logger.info("模型和优化器设置完成")

    def add_training_data(self, state: np.ndarray, action: np.ndarray,
                         reward: float, next_state: np.ndarray, done: bool):
        """添加训练数据"""
        try:
            # 构造训练样本
            training_sample = (
                np.array(state, dtype=np.float32),
                np.array([action, reward, done], dtype=np.float32)
            )

            # 添加到缓冲区
            self.data_buffer.add(training_sample)
            self.samples_processed += 1

            # 检查是否需要触发学习
            if self._should_trigger_learning():
                self._trigger_learning()

        except Exception as e:
            self.logger.error(f"添加训练数据失败: {str(e)}")

    def _should_trigger_learning(self) -> bool:
        """检查是否应该触发学习"""
        # 检查数据量
        if self.data_buffer.size() < self.config.batch_size:
            return False

        # 根据模式检查触发条件
        if self.config.mode == OnlineLearningMode.CONTINUOUS:
            return True
        elif self.config.mode == OnlineLearningMode.INCREMENTAL:
            return self.samples_processed % self.config.update_frequency == 0
        elif self.config.mode == OnlineLearningMode.ADAPTIVE:
            return self._check_adaptive_triggers()
        elif self.config.mode == OnlineLearningMode.TRIGGERED:
            return False  # 只能手动触发

        return False

    def _check_adaptive_triggers(self) -> bool:
        """检查自适应触发条件"""
        # 检查性能下降
        if self.performance_monitor.check_degradation(self.config.performance_threshold):
            return True

        # 检查时间间隔
        time_since_update = (datetime.now() - self.last_update_time).total_seconds()
        if time_since_update > 3600:  # 1小时
            return True

        # 检查数据漂移（简化实现）
        if self.data_buffer.size() > 1000:
            return True

        return False

    def _trigger_learning(self):
        """触发学习过程"""
        if self.is_learning:
            self.logger.warning("学习过程已在进行中，跳过触发")
            return

        if self.model is None:
            self.logger.warning("模型未设置，无法开始学习")
            return

        # 启动学习线程
        self.learning_thread = threading.Thread(target=self._learning_loop)
        self.learning_thread.daemon = True
        self.learning_thread.start()

        self.logger.info("触发在线学习")

    def _learning_loop(self):
        """学习循环"""
        self.is_learning = True
        start_time = time.time()

        try:
            # 获取训练批次
            batch_data, batch_weights = self.data_buffer.get_batch(self.config.batch_size)

            if not batch_data:
                self.logger.warning("没有可用的训练数据")
                return

            # 执行训练步骤
            loss = self._train_step(batch_data, batch_weights)

            # 更新性能监控
            performance_score = self._evaluate_performance()
            self.performance_monitor.update(performance_score, loss)

            # 记录指标
            processing_time = time.time() - start_time
            metrics = self._create_learning_metrics(loss, performance_score, processing_time)
            self.learning_metrics_history.append(metrics)

            # 更新状态
            self.last_update_time = datetime.now()
            self.model_version += 1

            self.logger.info(f"在线学习完成 - 损失: {loss:.4f}, 性能: {performance_score:.4f}")

        except Exception as e:
            self.logger.error(f"在线学习失败: {str(e)}")
        finally:
            self.is_learning = False

    def _train_step(self, batch_data: List, batch_weights: List) -> float:
        """执行训练步骤"""
        self.model.train()
        self.optimizer.zero_grad()

        total_loss = 0.0
        batch_size = len(batch_data)

        for i, (state, target) in enumerate(batch_data):
            # 转换为张量
            state_tensor = torch.FloatTensor(state).unsqueeze(0)
            target_tensor = torch.FloatTensor(target)

            # 前向传播
            output = self.model(state_tensor)

            # 计算损失（简化实现）
            loss = nn.MSELoss()(output.squeeze(), target_tensor)

            # 应用权重
            weight = batch_weights[i] if i < len(batch_weights) else 1.0
            weighted_loss = loss * weight

            # 反向传播
            weighted_loss.backward()
            total_loss += weighted_loss.item()

        # 更新参数
        self.optimizer.step()

        return total_loss / batch_size

    def _evaluate_performance(self) -> float:
        """评估模型性能"""
        if self.model is None:
            return 0.0

        try:
            self.model.eval()

            # 获取验证数据
            validation_size = max(10, int(self.data_buffer.size() * self.config.validation_split))
            val_data, _ = self.data_buffer.get_batch(validation_size)

            if not val_data:
                return 0.0

            total_error = 0.0
            with torch.no_grad():
                for state, target in val_data:
                    state_tensor = torch.FloatTensor(state).unsqueeze(0)
                    target_tensor = torch.FloatTensor(target)

                    output = self.model(state_tensor)
                    error = torch.abs(output.squeeze() - target_tensor).mean().item()
                    total_error += error

            # 返回平均准确率（1 - 平均误差）
            average_error = total_error / len(val_data)
            performance_score = max(0.0, 1.0 - average_error)

            return performance_score

        except Exception as e:
            self.logger.error(f"性能评估失败: {str(e)}")
            return 0.0

    def _create_learning_metrics(self, loss: float, performance_score: float,
                               processing_time: float) -> LearningMetrics:
        """创建学习指标"""
        import psutil

        try:
            import psutil
            memory_usage = psutil.virtual_memory().percent / 100.0
        except ImportError:
            memory_usage = 0.0

        return LearningMetrics(
            timestamp=datetime.now().isoformat(),
            samples_processed=self.samples_processed,
            current_loss=loss,
            average_loss=self.performance_monitor.get_average_loss(),
            learning_rate=self.config.learning_rate,
            memory_usage=memory_usage,
            processing_time=processing_time,
            performance_score=performance_score,
            drift_score=0.0,  # 简化实现
            model_version=self.model_version
        )

    def manual_trigger_learning(self) -> OnlineLearningResult:
        """手动触发学习"""
        try:
            if self.is_learning:
                return OnlineLearningResult(
                    success=False,
                    metrics=self._get_current_metrics(),
                    model_updated=False,
                    trigger_reason="学习过程已在进行中",
                    warnings=["学习过程已在进行中，请稍后再试"]
                )

            # 强制触发学习
            self._trigger_learning()

            # 等待学习完成
            if self.learning_thread:
                self.learning_thread.join(timeout=30)  # 最多等待30秒

            return OnlineLearningResult(
                success=True,
                metrics=self._get_current_metrics(),
                model_updated=True,
                trigger_reason="手动触发",
                recommendations=["学习已完成，模型已更新"]
            )

        except Exception as e:
            self.logger.error(f"手动触发学习失败: {str(e)}")
            return OnlineLearningResult(
                success=False,
                metrics=self._get_current_metrics(),
                model_updated=False,
                trigger_reason="手动触发失败",
                warnings=[str(e)]
            )

    def get_learning_status(self) -> Dict[str, Any]:
        """获取学习状态"""
        return {
            'is_learning': self.is_learning,
            'samples_processed': self.samples_processed,
            'buffer_size': self.data_buffer.size(),
            'model_version': self.model_version,
            'last_update_time': self.last_update_time.isoformat(),
            'current_performance': self.performance_monitor.get_current_performance(),
            'average_loss': self.performance_monitor.get_average_loss(),
            'config': asdict(self.config)
        }

    def _get_current_metrics(self) -> LearningMetrics:
        """获取当前指标"""
        return LearningMetrics(
            timestamp=datetime.now().isoformat(),
            samples_processed=self.samples_processed,
            current_loss=self.performance_monitor.get_average_loss(),
            average_loss=self.performance_monitor.get_average_loss(),
            learning_rate=self.config.learning_rate,
            memory_usage=0.0,
            processing_time=0.0,
            performance_score=self.performance_monitor.get_current_performance(),
            drift_score=0.0,
            model_version=self.model_version
        )

    def save_model(self, filepath: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型未设置")

        try:
            # 创建保存目录
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # 保存模型状态
            save_data = {
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict() if self.optimizer else None,
                'model_version': self.model_version,
                'samples_processed': self.samples_processed,
                'config': asdict(self.config),
                'metrics_history': self.learning_metrics_history[-100:]  # 保存最近100条记录
            }

            torch.save(save_data, filepath)
            self.logger.info(f"模型已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"保存模型失败: {str(e)}")
            raise

    def load_model(self, filepath: str, model: nn.Module):
        """加载模型"""
        try:
            if not os.path.exists(filepath):
                raise FileNotFoundError(f"模型文件不存在: {filepath}")

            # 加载模型数据
            save_data = torch.load(filepath, map_location='cpu')

            # 恢复模型状态
            model.load_state_dict(save_data['model_state_dict'])
            self.model = model

            # 恢复优化器
            if save_data.get('optimizer_state_dict') and self.optimizer:
                self.optimizer.load_state_dict(save_data['optimizer_state_dict'])

            # 恢复状态
            self.model_version = save_data.get('model_version', 1)
            self.samples_processed = save_data.get('samples_processed', 0)

            # 恢复配置（可选）
            if 'config' in save_data:
                loaded_config = OnlineLearningConfig(**save_data['config'])
                # 只更新兼容的配置项
                self.config.learning_rate = loaded_config.learning_rate
                self.config.batch_size = loaded_config.batch_size

            # 恢复指标历史
            if 'metrics_history' in save_data:
                self.learning_metrics_history = save_data['metrics_history']

            self.logger.info(f"模型已从 {filepath} 加载")

        except Exception as e:
            self.logger.error(f"加载模型失败: {str(e)}")
            raise

    def stop(self):
        """停止在线学习"""
        self.stop_learning.set()

        if self.learning_thread and self.learning_thread.is_alive():
            self.learning_thread.join(timeout=5)

        self.logger.info("在线学习管理器已停止")

    def reset(self):
        """重置学习状态"""
        self.stop()

        # 清空缓冲区和历史
        self.data_buffer.clear()
        self.performance_monitor = PerformanceMonitor()
        self.learning_metrics_history.clear()

        # 重置状态
        self.samples_processed = 0
        self.model_version = 1
        self.last_update_time = datetime.now()
        self.is_learning = False

        self.logger.info("在线学习状态已重置")
