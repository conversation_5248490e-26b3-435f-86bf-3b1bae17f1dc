#!/usr/bin/env python3
"""
快速测试整合交易系统
使用较少的训练轮数进行快速验证
"""

from integrated_trading_system import TradingSystem

def quick_test():
    """快速测试配置"""
    config = {
        'data': {
            'symbol': 'QUICK_TEST',
            'length': 500,  # 减少数据量
            'train_ratio': 0.7,
            'val_ratio': 0.15
        },
        'env': {
            'initial_balance': 100000,
            'use_technical_indicators': True,
            'transaction_cost': 0.001
        },
        'training': {
            'episodes': 100,  # 减少训练轮数
            'batch_size': 32,  # 减少批次大小
            'memory_size': 10000,  # 减少内存大小
            'eval_episodes': 3,  # 减少评估轮数
            'save_freq': 25  # 更频繁保存
        },
        'model': {
            'actor_lr': 3e-4,
            'critic_lr': 3e-4,
            'gamma': 0.99,
            'tau': 0.005,
            'alpha': 0.2
        }
    }
    
    print("=== 快速测试整合交易系统 ===")
    
    # 创建交易系统
    system = TradingSystem(config)
    
    # 运行完整流程
    train_results, test_results = system.run_complete_pipeline()
    
    print("\n=== 快速测试完成 ===")
    print(f"最佳验证得分: {train_results['best_val_score']:.4f}")
    print(f"平均测试收益率: {test_results['avg_profit_rate']*100:.2f}%")
    
    return train_results, test_results

if __name__ == "__main__":
    quick_test()