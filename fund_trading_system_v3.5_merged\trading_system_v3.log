2025-07-22 13:41:56,645 - LLMAgent.<PERSON>M_Coordinator - INFO - LLM service initialized successfully
2025-07-22 13:41:56,645 - CZSCAgent.<PERSON><PERSON><PERSON>_Coordinator - INFO - CZSC Agent initialized with lookback period: 50
2025-07-22 13:41:56,645 - MultiAgentCoordinator - INFO - All trading agents initialized successfully
2025-07-22 13:41:56,645 - MultiAgentCoordinator - INFO - MultiAgentCoordinator initialized with weights: RL=0.40, LLM=0.30, CZSC=0.30
2025-07-22 13:41:56,645 - TradingAgent - INFO - TradingAgent initialized with risk tolerance: MEDIUM
2025-07-22 13:41:56,645 - MainDemo - INFO - Trading agent initialized successfully
2025-07-22 13:41:56,651 - MainDemo - INFO - Processing market data for 000001.SZ
2025-07-22 13:41:56,651 - MainDemo - ERROR - Demo failed: analyze_and_recommend() got an unexpected keyword argument 'context'
2025-07-22 13:43:57,971 - LLMAgent.LLM_Coordinator - INFO - LLM service initialized successfully
2025-07-22 13:43:57,972 - CZSCAgent.<PERSON><PERSON><PERSON>_Coordinator - INFO - CZSC Agent initialized with lookback period: 50
2025-07-22 13:43:57,972 - MultiAgentCoordinator - INFO - All trading agents initialized successfully
2025-07-22 13:43:57,972 - MultiAgentCoordinator - INFO - MultiAgentCoordinator initialized with weights: RL=0.40, LLM=0.30, CZSC=0.30
2025-07-22 13:43:57,972 - TradingAgent - INFO - TradingAgent initialized with risk tolerance: MEDIUM
2025-07-22 13:43:57,972 - MainDemo - INFO - Trading agent initialized successfully
2025-07-22 13:43:57,972 - MainDemo - INFO - Processing market data for 000001.SZ
2025-07-22 13:43:57,973 - LLMAgent.LLM_Coordinator - INFO - Generated signal: hold with confidence 0.000
2025-07-22 13:43:57,973 - CZSCAgent.CZSC_Coordinator - INFO - Generated CZSC signal: hold with confidence 0.000
2025-07-22 13:43:57,973 - MultiAgentCoordinator - INFO - Generated coordinated decision: hold (confidence: 0.100, method: consensus)
2025-07-22 13:43:57,973 - TradingAgent - INFO - Generated recommendation for 000001.SZ: hold (confidence: 0.100)
2025-07-22 13:43:57,974 - MainDemo - ERROR - Demo failed: 'TradingRecommendation' object has no attribute 'agent_contributions'
2025-07-22 13:44:37,183 - LLMAgent.LLM_Coordinator - INFO - LLM service initialized successfully
2025-07-22 13:44:37,183 - CZSCAgent.CZSC_Coordinator - INFO - CZSC Agent initialized with lookback period: 50
2025-07-22 13:44:37,183 - MultiAgentCoordinator - INFO - All trading agents initialized successfully
2025-07-22 13:44:37,184 - MultiAgentCoordinator - INFO - MultiAgentCoordinator initialized with weights: RL=0.40, LLM=0.30, CZSC=0.30
2025-07-22 13:44:37,184 - TradingAgent - INFO - TradingAgent initialized with risk tolerance: MEDIUM
2025-07-22 13:44:37,184 - MainDemo - INFO - Trading agent initialized successfully
2025-07-22 13:44:37,184 - MainDemo - INFO - Processing market data for 000001.SZ
2025-07-22 13:44:37,184 - LLMAgent.LLM_Coordinator - INFO - Generated signal: hold with confidence 0.000
2025-07-22 13:44:37,185 - CZSCAgent.CZSC_Coordinator - INFO - Generated CZSC signal: hold with confidence 0.000
2025-07-22 13:44:37,185 - MultiAgentCoordinator - INFO - Generated coordinated decision: hold (confidence: 0.100, method: consensus)
2025-07-22 13:44:37,185 - TradingAgent - INFO - Generated recommendation for 000001.SZ: hold (confidence: 0.100)
2025-07-22 13:44:37,186 - MainDemo - ERROR - Demo failed: 'active_agents'
