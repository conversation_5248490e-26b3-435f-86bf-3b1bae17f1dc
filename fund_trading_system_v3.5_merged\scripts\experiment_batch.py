#!/usr/bin/env python3
"""
Batch Experiment Script

批量融合策略与风控参数实验，自动输出结构化对比报告。
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import logging
import pandas as pd
from pathlib import Path
from datetime import datetime
from core.config import TradingConfig
from backtest.engines.data_manager import DataManager
from backtest.engines.backtest_engine import BacktestEngine
from coordinators.multi_agent_coordinator import MultiAgentCoordinator

# 配置实验参数
fusion_methods = ["weighted", "consensus", "confidence_override", "conservative"]
var_levels = [0.90, 0.95, 0.99]
dynamic_stop_loss_options = [False, True]

symbol = "000001.SZ"
train_start = "2020-01-01"
train_end = "2020-12-31"

# 初始化日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BatchExperiment")

# 加载数据
config = TradingConfig()
data_manager = DataManager(data_dir="data")
data = data_manager.load_data(symbol, start_date=train_start, end_date=train_end)

results = []
trial_id = 0
for fusion in fusion_methods:
    for var_level in var_levels:
        for dyn_stop in dynamic_stop_loss_options:
            trial_id += 1
            logger.info(f"Trial {trial_id}: fusion={fusion}, VaR={var_level}, dynamic_stop_loss={dyn_stop}")
            # 配置风控参数
            config.risk_config.var_confidence = var_level
            config.risk_config.dynamic_stop_loss = dyn_stop
            # 初始化回测引擎
            backtest_engine = BacktestEngine(config)
            # 初始化多智能体协调器（假设已实现结构化接口）
            # 这里只做占位，实际应传入已实现的agent实例
            backtest_engine.coordinator = MultiAgentCoordinator(agents={}, fusion_methods=[fusion])
            # 运行回测
            try:
                result = backtest_engine.run_backtest(data)
                res = {
                    'trial': trial_id,
                    'fusion': fusion,
                    'var_level': var_level,
                    'dynamic_stop_loss': dyn_stop,
                    'total_return': result.total_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate,
                    'total_trades': result.total_trades,
                    'final_capital': result.final_capital
                }
                results.append(res)
            except Exception as e:
                logger.error(f"Trial {trial_id} failed: {e}")

# 输出实验报告
output_dir = Path("output/batch_experiment")
output_dir.mkdir(parents=True, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
report_file = output_dir / f"experiment_report_{timestamp}.csv"
df = pd.DataFrame(results)
df.to_csv(report_file, index=False)
logger.info(f"Batch experiment completed. Report saved to: {report_file}")
print(f"✅ Batch experiment completed. Report: {report_file}") 