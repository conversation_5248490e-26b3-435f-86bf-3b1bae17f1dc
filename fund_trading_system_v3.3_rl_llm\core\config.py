"""
系统配置和日志设置
包含日志格式化器和配置函数
"""

import logging


class SafeFormatter(logging.Formatter):
    """安全的日志格式化器，避免编码问题"""
    def format(self, record):
        # 移除或替换可能导致编码问题的字符
        if hasattr(record, 'msg'):
            # 将emoji和特殊字符替换为简单文本
            msg = str(record.msg)
            # 替换常见的emoji
            msg = msg.replace('🔄', '[CYCLE]')
            msg = msg.replace('✅', '[OK]')
            msg = msg.replace('❌', '[ERROR]')
            msg = msg.replace('⚠️', '[WARNING]')
            msg = msg.replace('🎯', '[TARGET]')
            msg = msg.replace('📊', '[CHART]')
            msg = msg.replace('🚀', '[ROCKET]')
            msg = msg.replace('🔧', '[TOOL]')
            msg = msg.replace('💡', '[IDEA]')
            record.msg = msg
        return super().format(record)


def setup_logging():
    """设置日志配置"""
    # 配置文件日志（保留emoji）
    file_handler = logging.FileHandler('fund_trading_v3.log', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    
    # 配置控制台日志（移除emoji）
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_formatter)
    
    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler]
    )


# 创建默认logger
logger = logging.getLogger(__name__)
