"""
A/B测试框架 - 支持多模型并行测试和性能对比
实现流量分配管理、性能对比分析、统计显著性检验
"""

import logging
import numpy as np
import random
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Callable, Union
import json
import os
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import defaultdict, deque
import uuid

# 尝试导入可选依赖
try:
    import scipy.stats as stats
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False


class TestStatus(Enum):
    """测试状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    STOPPED = "stopped"
    FAILED = "failed"


class TrafficAllocationMethod(Enum):
    """流量分配方法"""
    RANDOM = "random"
    ROUND_ROBIN = "round_robin"
    WEIGHTED = "weighted"
    PERFORMANCE_BASED = "performance_based"


@dataclass
class ModelVariant:
    """模型变体"""
    variant_id: str
    model_name: str
    model_path: str
    traffic_weight: float = 1.0
    description: str = ""
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ABTestConfig:
    """A/B测试配置"""
    test_name: str
    variants: List[ModelVariant]
    traffic_allocation_method: TrafficAllocationMethod = TrafficAllocationMethod.RANDOM
    min_sample_size: int = 100
    max_duration_hours: int = 24
    significance_level: float = 0.05
    minimum_effect_size: float = 0.05
    enable_early_stopping: bool = True
    early_stopping_patience: int = 50
    metrics_to_track: List[str] = None
    
    def __post_init__(self):
        if self.metrics_to_track is None:
            self.metrics_to_track = ['accuracy', 'precision', 'recall', 'f1_score', 'auc']


@dataclass
class TestResult:
    """测试结果"""
    variant_id: str
    sample_count: int
    metrics: Dict[str, float]
    confidence_intervals: Dict[str, Tuple[float, float]]
    timestamp: str


@dataclass
class ABTestReport:
    """A/B测试报告"""
    test_id: str
    test_name: str
    status: TestStatus
    start_time: str
    end_time: Optional[str]
    duration_hours: float
    total_samples: int
    variant_results: List[TestResult]
    statistical_significance: Dict[str, bool]
    winner: Optional[str]
    recommendations: List[str]
    metadata: Dict[str, Any]


class TrafficAllocator:
    """流量分配器"""
    
    def __init__(self, variants: List[ModelVariant], method: TrafficAllocationMethod):
        self.variants = variants
        self.method = method
        self.current_index = 0
        self.performance_history = defaultdict(list)
        self.lock = threading.Lock()
        
    def allocate(self, request_id: str = None) -> str:
        """分配流量到变体"""
        with self.lock:
            if self.method == TrafficAllocationMethod.RANDOM:
                return self._random_allocation()
            elif self.method == TrafficAllocationMethod.ROUND_ROBIN:
                return self._round_robin_allocation()
            elif self.method == TrafficAllocationMethod.WEIGHTED:
                return self._weighted_allocation()
            elif self.method == TrafficAllocationMethod.PERFORMANCE_BASED:
                return self._performance_based_allocation()
            else:
                return self._random_allocation()
    
    def _random_allocation(self) -> str:
        """随机分配"""
        weights = [v.traffic_weight for v in self.variants]
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]
        
        return np.random.choice(
            [v.variant_id for v in self.variants],
            p=normalized_weights
        )
    
    def _round_robin_allocation(self) -> str:
        """轮询分配"""
        variant = self.variants[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.variants)
        return variant.variant_id
    
    def _weighted_allocation(self) -> str:
        """权重分配"""
        return self._random_allocation()  # 与随机分配相同
    
    def _performance_based_allocation(self) -> str:
        """基于性能的分配"""
        if not self.performance_history:
            return self._random_allocation()
        
        # 计算每个变体的平均性能
        avg_performance = {}
        for variant in self.variants:
            if variant.variant_id in self.performance_history:
                avg_performance[variant.variant_id] = np.mean(
                    self.performance_history[variant.variant_id][-10:]  # 最近10次
                )
            else:
                avg_performance[variant.variant_id] = 0.5  # 默认值
        
        # 使用softmax分配概率
        performances = list(avg_performance.values())
        exp_performances = np.exp(np.array(performances))
        probabilities = exp_performances / np.sum(exp_performances)
        
        return np.random.choice(
            list(avg_performance.keys()),
            p=probabilities
        )
    
    def update_performance(self, variant_id: str, performance: float):
        """更新性能历史"""
        with self.lock:
            self.performance_history[variant_id].append(performance)
            # 保持最近100次记录
            if len(self.performance_history[variant_id]) > 100:
                self.performance_history[variant_id] = self.performance_history[variant_id][-100:]


class StatisticalAnalyzer:
    """统计分析器"""
    
    @staticmethod
    def calculate_confidence_interval(data: List[float], confidence_level: float = 0.95) -> Tuple[float, float]:
        """计算置信区间"""
        if not data or len(data) < 2:
            return (0.0, 0.0)
        
        mean = np.mean(data)
        std_err = stats.sem(data) if SCIPY_AVAILABLE else np.std(data) / np.sqrt(len(data))
        
        if SCIPY_AVAILABLE:
            h = std_err * stats.t.ppf((1 + confidence_level) / 2., len(data) - 1)
        else:
            # 简化实现，使用正态分布近似
            h = std_err * 1.96  # 95%置信区间
        
        return (mean - h, mean + h)
    
    @staticmethod
    def t_test(group_a: List[float], group_b: List[float]) -> Tuple[float, float]:
        """执行t检验"""
        if not SCIPY_AVAILABLE:
            # 简化实现
            mean_a, mean_b = np.mean(group_a), np.mean(group_b)
            return abs(mean_a - mean_b), 0.05 if abs(mean_a - mean_b) > 0.1 else 0.5
        
        try:
            statistic, p_value = stats.ttest_ind(group_a, group_b)
            return statistic, p_value
        except Exception:
            return 0.0, 1.0
    
    @staticmethod
    def effect_size(group_a: List[float], group_b: List[float]) -> float:
        """计算效应大小（Cohen's d）"""
        if not group_a or not group_b:
            return 0.0
        
        mean_a, mean_b = np.mean(group_a), np.mean(group_b)
        std_a, std_b = np.std(group_a, ddof=1), np.std(group_b, ddof=1)
        
        # 合并标准差
        n_a, n_b = len(group_a), len(group_b)
        pooled_std = np.sqrt(((n_a - 1) * std_a**2 + (n_b - 1) * std_b**2) / (n_a + n_b - 2))
        
        if pooled_std == 0:
            return 0.0
        
        return (mean_a - mean_b) / pooled_std


class ABTestingFramework:
    """A/B测试框架"""
    
    def __init__(self, config: ABTestConfig):
        self.config = config
        self.test_id = str(uuid.uuid4())
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 初始化组件
        self.traffic_allocator = TrafficAllocator(
            config.variants, 
            config.traffic_allocation_method
        )
        self.statistical_analyzer = StatisticalAnalyzer()
        
        # 测试状态
        self.status = TestStatus.PENDING
        self.start_time = None
        self.end_time = None
        
        # 数据收集
        self.variant_data = defaultdict(lambda: defaultdict(list))
        self.sample_counts = defaultdict(int)
        self.lock = threading.Lock()
        
        self.logger.info(f"A/B测试框架初始化完成 - 测试ID: {self.test_id}")
    
    def start_test(self):
        """开始测试"""
        with self.lock:
            if self.status != TestStatus.PENDING:
                raise ValueError(f"测试已开始或已完成，当前状态: {self.status}")
            
            self.status = TestStatus.RUNNING
            self.start_time = datetime.now()
            
        self.logger.info(f"A/B测试开始 - {self.config.test_name}")
    
    def allocate_variant(self, request_id: str = None) -> str:
        """分配变体"""
        if self.status != TestStatus.RUNNING:
            raise ValueError(f"测试未运行，当前状态: {self.status}")
        
        variant_id = self.traffic_allocator.allocate(request_id)
        
        with self.lock:
            self.sample_counts[variant_id] += 1
        
        return variant_id
    
    def record_result(self, variant_id: str, metrics: Dict[str, float]):
        """记录结果"""
        if self.status != TestStatus.RUNNING:
            self.logger.warning(f"测试未运行，忽略结果记录 - 状态: {self.status}")
            return
        
        with self.lock:
            for metric_name, value in metrics.items():
                self.variant_data[variant_id][metric_name].append(value)
        
        # 更新流量分配器的性能历史
        if 'accuracy' in metrics:
            self.traffic_allocator.update_performance(variant_id, metrics['accuracy'])
        
        # 检查是否应该早停
        if self.config.enable_early_stopping:
            self._check_early_stopping()
    
    def _check_early_stopping(self):
        """检查早停条件"""
        # 检查最小样本量
        total_samples = sum(self.sample_counts.values())
        if total_samples < self.config.min_sample_size:
            return
        
        # 检查时间限制
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds() / 3600
            if duration >= self.config.max_duration_hours:
                self.stop_test()
                return
        
        # 检查统计显著性
        if len(self.config.variants) == 2:
            variant_ids = [v.variant_id for v in self.config.variants]
            if all(v_id in self.variant_data for v_id in variant_ids):
                for metric in self.config.metrics_to_track:
                    if metric in self.variant_data[variant_ids[0]] and metric in self.variant_data[variant_ids[1]]:
                        group_a = self.variant_data[variant_ids[0]][metric]
                        group_b = self.variant_data[variant_ids[1]][metric]
                        
                        if len(group_a) >= self.config.early_stopping_patience and len(group_b) >= self.config.early_stopping_patience:
                            _, p_value = self.statistical_analyzer.t_test(group_a, group_b)
                            if p_value < self.config.significance_level:
                                self.logger.info(f"检测到统计显著性，早停测试 - {metric}: p={p_value:.4f}")
                                self.stop_test()
                                return

    def stop_test(self):
        """停止测试"""
        with self.lock:
            if self.status == TestStatus.RUNNING:
                self.status = TestStatus.COMPLETED
                self.end_time = datetime.now()
                self.logger.info(f"A/B测试完成 - {self.config.test_name}")

    def get_current_results(self) -> Dict[str, Any]:
        """获取当前结果"""
        with self.lock:
            results = {}
            for variant_id in self.sample_counts:
                variant_metrics = {}
                for metric_name, values in self.variant_data[variant_id].items():
                    if values:
                        variant_metrics[metric_name] = {
                            'mean': np.mean(values),
                            'std': np.std(values),
                            'count': len(values),
                            'confidence_interval': self.statistical_analyzer.calculate_confidence_interval(values)
                        }

                results[variant_id] = {
                    'sample_count': self.sample_counts[variant_id],
                    'metrics': variant_metrics
                }

            return results

    def generate_report(self) -> ABTestReport:
        """生成测试报告"""
        with self.lock:
            # 计算测试结果
            variant_results = []
            for variant in self.config.variants:
                variant_id = variant.variant_id
                metrics = {}
                confidence_intervals = {}

                for metric_name, values in self.variant_data[variant_id].items():
                    if values:
                        metrics[metric_name] = np.mean(values)
                        confidence_intervals[metric_name] = self.statistical_analyzer.calculate_confidence_interval(values)

                variant_results.append(TestResult(
                    variant_id=variant_id,
                    sample_count=self.sample_counts[variant_id],
                    metrics=metrics,
                    confidence_intervals=confidence_intervals,
                    timestamp=datetime.now().isoformat()
                ))

            # 统计显著性检验
            statistical_significance = {}
            winner = None

            if len(self.config.variants) == 2:
                variant_ids = [v.variant_id for v in self.config.variants]
                for metric in self.config.metrics_to_track:
                    if (metric in self.variant_data[variant_ids[0]] and
                        metric in self.variant_data[variant_ids[1]]):

                        group_a = self.variant_data[variant_ids[0]][metric]
                        group_b = self.variant_data[variant_ids[1]][metric]

                        if group_a and group_b:
                            _, p_value = self.statistical_analyzer.t_test(group_a, group_b)
                            statistical_significance[metric] = p_value < self.config.significance_level

                            # 确定获胜者
                            if p_value < self.config.significance_level:
                                mean_a, mean_b = np.mean(group_a), np.mean(group_b)
                                if mean_a > mean_b:
                                    winner = variant_ids[0]
                                else:
                                    winner = variant_ids[1]

            # 生成建议
            recommendations = self._generate_recommendations(variant_results, statistical_significance, winner)

            # 计算持续时间
            duration_hours = 0.0
            if self.start_time:
                end_time = self.end_time or datetime.now()
                duration_hours = (end_time - self.start_time).total_seconds() / 3600

            return ABTestReport(
                test_id=self.test_id,
                test_name=self.config.test_name,
                status=self.status,
                start_time=self.start_time.isoformat() if self.start_time else "",
                end_time=self.end_time.isoformat() if self.end_time else None,
                duration_hours=duration_hours,
                total_samples=sum(self.sample_counts.values()),
                variant_results=variant_results,
                statistical_significance=statistical_significance,
                winner=winner,
                recommendations=recommendations,
                metadata={
                    'config': asdict(self.config),
                    'traffic_allocation_method': self.config.traffic_allocation_method.value
                }
            )

    def _generate_recommendations(self, variant_results: List[TestResult],
                                statistical_significance: Dict[str, bool],
                                winner: Optional[str]) -> List[str]:
        """生成建议"""
        recommendations = []

        total_samples = sum(result.sample_count for result in variant_results)

        # 样本量建议
        if total_samples < self.config.min_sample_size:
            recommendations.append(f"样本量不足（{total_samples}/{self.config.min_sample_size}），建议继续收集数据")

        # 统计显著性建议
        if any(statistical_significance.values()):
            if winner:
                recommendations.append(f"检测到统计显著差异，建议选择变体 {winner}")
            else:
                recommendations.append("检测到统计显著差异，但无明确获胜者，建议进一步分析")
        else:
            recommendations.append("未检测到统计显著差异，可能需要更多数据或重新设计实验")

        # 效应大小建议
        if len(variant_results) == 2:
            for metric in self.config.metrics_to_track:
                if (metric in variant_results[0].metrics and
                    metric in variant_results[1].metrics):

                    group_a = self.variant_data[variant_results[0].variant_id][metric]
                    group_b = self.variant_data[variant_results[1].variant_id][metric]

                    if group_a and group_b:
                        effect_size = self.statistical_analyzer.effect_size(group_a, group_b)
                        if abs(effect_size) < 0.2:
                            recommendations.append(f"{metric}的效应大小较小（{effect_size:.3f}），实际业务影响可能有限")
                        elif abs(effect_size) > 0.8:
                            recommendations.append(f"{metric}的效应大小较大（{effect_size:.3f}），建议优先考虑部署")

        return recommendations

    def save_results(self, filepath: str):
        """保存结果"""
        try:
            report = self.generate_report()

            # 创建保存目录
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # 保存为JSON
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(asdict(report), f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"A/B测试结果已保存到: {filepath}")

        except Exception as e:
            self.logger.error(f"保存A/B测试结果失败: {str(e)}")
            raise

    def load_results(self, filepath: str) -> ABTestReport:
        """加载结果"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 重构TestResult对象
            variant_results = []
            for result_data in data['variant_results']:
                variant_results.append(TestResult(**result_data))

            # 重构ABTestReport对象
            report_data = data.copy()
            report_data['variant_results'] = variant_results
            report_data['status'] = TestStatus(report_data['status'])

            return ABTestReport(**report_data)

        except Exception as e:
            self.logger.error(f"加载A/B测试结果失败: {str(e)}")
            raise


def create_ab_test(test_name: str, model_variants: List[Dict[str, Any]],
                  **kwargs) -> ABTestingFramework:
    """创建A/B测试的便捷函数"""

    # 构建变体列表
    variants = []
    for i, variant_data in enumerate(model_variants):
        variant = ModelVariant(
            variant_id=variant_data.get('variant_id', f'variant_{i}'),
            model_name=variant_data.get('model_name', f'model_{i}'),
            model_path=variant_data.get('model_path', ''),
            traffic_weight=variant_data.get('traffic_weight', 1.0),
            description=variant_data.get('description', ''),
            metadata=variant_data.get('metadata', {})
        )
        variants.append(variant)

    # 创建配置
    config = ABTestConfig(
        test_name=test_name,
        variants=variants,
        **kwargs
    )

    return ABTestingFramework(config)
