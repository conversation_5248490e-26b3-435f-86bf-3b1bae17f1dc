#!/usr/bin/env python3
"""
简化版强化学习股票交易系统
避免复杂依赖问题，专注核心功能
"""

import numpy as np
import pandas as pd
import random
import warnings
warnings.filterwarnings('ignore')

# 尝试导入gymnasium，如果失败则使用gym
try:
    import gymnasium as gym
    from gymnasium import spaces
    GYM_VERSION = "gymnasium"
    print("使用 gymnasium")
except ImportError:
    try:
        import gym
        from gym import spaces
        GYM_VERSION = "gym"
        print("使用 gym")
    except ImportError:
        print("错误: 需要安装 gymnasium 或 gym")
        exit(1)

# 尝试导入stable-baselines3
try:
    from stable_baselines3 import SAC, PPO
    from stable_baselines3.common.env_checker import check_env
    from stable_baselines3.common.vec_env import DummyVecEnv
    from stable_baselines3.common.monitor import Monitor
    SB3_AVAILABLE = True
    print("stable-baselines3 可用")
except ImportError:
    SB3_AVAILABLE = False
    print("警告: stable-baselines3 不可用，将使用简化版本")

# 配置参数
INITIAL_BALANCE = 100000
MAX_STEPS = 1000
SEED = 42

class SimpleStockEnv(gym.Env):
    """简化的股票交易环境"""
    
    def __init__(self, df):
        super().__init__()
        
        self.df = df.reset_index(drop=True)
        self.current_step = 0
        
        # 状态空间: [价格特征(4) + 账户状态(3) + 技术指标(3)] = 10维
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(10,), dtype=np.float32
        )
        
        # 动作空间: 连续动作 [交易信号(-1到1), 仓位比例(0到1)]
        self.action_space = spaces.Box(
            low=np.array([-1.0, 0.0]), 
            high=np.array([1.0, 1.0]), 
            dtype=np.float32
        )
        
        self.reset()
        
    def reset(self, seed=None, options=None):
        if hasattr(super(), 'reset'):
            super().reset(seed=seed)
        
        # 重置账户状态
        self.balance = INITIAL_BALANCE
        self.shares_held = 0
        self.cost_basis = 0
        self.current_step = 0
        self.net_worth = INITIAL_BALANCE
        self.max_net_worth = INITIAL_BALANCE
        
        observation = self._get_observation()
        info = self._get_info()
        
        if GYM_VERSION == "gymnasium":
            return observation, info
        else:
            return observation
        
    def _get_observation(self):
        """获取当前状态观测"""
        if self.current_step >= len(self.df):
            self.current_step = len(self.df) - 1
            
        row = self.df.iloc[self.current_step]
        
        # 价格特征 (4维)
        price_features = np.array([
            row['open'] / 200,    # 归一化开盘价
            row['high'] / 200,    # 归一化最高价
            row['low'] / 200,     # 归一化最低价
            row['close'] / 200,   # 归一化收盘价
        ])
        
        # 账户状态 (3维)
        account_features = np.array([
            self.balance / (INITIAL_BALANCE * 2),           # 归一化余额
            self.shares_held / 1000,                        # 归一化持股
            (self.net_worth / INITIAL_BALANCE - 1) / 2 + 0.5  # 归一化净值变化
        ])
        
        # 简单技术指标 (3维)
        if self.current_step >= 5:
            recent_closes = self.df['close'].iloc[max(0, self.current_step-5):self.current_step+1]
            sma_5 = recent_closes.mean()
            volatility = recent_closes.std()
            momentum = (recent_closes.iloc[-1] / recent_closes.iloc[0] - 1) if len(recent_closes) > 1 else 0
        else:
            sma_5 = row['close']
            volatility = 0
            momentum = 0
            
        tech_features = np.array([
            sma_5 / 200,                    # 5日均线
            min(volatility / 10, 1),        # 波动率
            (momentum + 0.1) / 0.2          # 动量指标
        ])
        
        # 合并所有特征
        observation = np.concatenate([price_features, account_features, tech_features])
        return np.clip(observation, 0, 1).astype(np.float32)
        
    def step(self, action):
        """执行一步交易"""
        # 解析动作
        trade_signal = action[0]  # -1到1
        position_ratio = action[1]  # 0到1
        
        # 获取当前价格
        if self.current_step >= len(self.df):
            self.current_step = len(self.df) - 1
            
        current_price = self.df.iloc[self.current_step]['close']
        
        # 执行交易
        reward = self._execute_trade(trade_signal, position_ratio, current_price)
        
        # 更新状态
        self.current_step += 1
        done = self._is_done()
        
        # 获取新观测
        observation = self._get_observation()
        info = self._get_info()
        
        if GYM_VERSION == "gymnasium":
            truncated = False
            return observation, reward, done, truncated, info
        else:
            return observation, reward, done, info
            
    def _execute_trade(self, signal, ratio, price):
        """执行交易逻辑"""
        initial_net_worth = self.net_worth
        
        if signal > 0.3 and self.balance >= price:  # 买入信号
            max_shares = int(self.balance / price)
            shares_to_buy = int(max_shares * ratio)
            
            if shares_to_buy > 0:
                cost = shares_to_buy * price
                self.balance -= cost
                
                # 更新持仓成本
                if self.shares_held > 0:
                    total_cost = self.cost_basis * self.shares_held + cost
                    self.shares_held += shares_to_buy
                    self.cost_basis = total_cost / self.shares_held
                else:
                    self.shares_held = shares_to_buy
                    self.cost_basis = price
                    
        elif signal < -0.3 and self.shares_held > 0:  # 卖出信号
            shares_to_sell = int(self.shares_held * ratio)
            
            if shares_to_sell > 0:
                revenue = shares_to_sell * price
                self.balance += revenue
                self.shares_held -= shares_to_sell
                
                if self.shares_held == 0:
                    self.cost_basis = 0
        
        # 更新净资产
        self.net_worth = self.balance + self.shares_held * price
        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth
            
        # 计算奖励
        profit_rate = (self.net_worth - INITIAL_BALANCE) / INITIAL_BALANCE
        
        # 基础收益奖励
        if profit_rate > 0:
            reward = profit_rate * 10
        else:
            reward = profit_rate * 5
            
        # 风险惩罚
        if self.shares_held > 0:
            unrealized_loss = (self.cost_basis - price) / self.cost_basis
            if unrealized_loss > 0.1:  # 超过10%损失
                reward -= unrealized_loss * 2
                
        return reward
        
    def _is_done(self):
        """检查是否结束"""
        if self.current_step >= len(self.df) - 1:
            return True
        if self.net_worth <= INITIAL_BALANCE * 0.1:  # 损失90%
            return True
        if self.net_worth >= INITIAL_BALANCE * 5:  # 收益500%
            return True
        return False
        
    def _get_info(self):
        """获取环境信息"""
        profit = self.net_worth - INITIAL_BALANCE
        return {
            'profit': profit,
            'profit_rate': profit / INITIAL_BALANCE,
            'net_worth': self.net_worth,
            'balance': self.balance,
            'shares_held': self.shares_held,
            'current_step': self.current_step
        }
        
    def render(self, mode='human'):
        """渲染环境状态"""
        info = self._get_info()
        print(f"Step: {info['current_step']}, "
              f"Net Worth: ${info['net_worth']:,.2f}, "
              f"Profit: ${info['profit']:,.2f} ({info['profit_rate']:.2%}), "
              f"Balance: ${info['balance']:,.2f}, "
              f"Shares: {info['shares_held']}")

def generate_stock_data(length=1000):
    """生成模拟股票数据"""
    np.random.seed(SEED)
    
    # 生成价格序列
    base_price = 100
    returns = np.random.normal(0.001, 0.02, length)
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1))
        
    prices = np.array(prices[1:])
    
    # 生成OHLC数据
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.normal(0, 0.005, length)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, length))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, length))),
        'volume': np.random.lognormal(10, 1, length),
    })
    
    # 确保OHLC逻辑正确
    df['high'] = np.maximum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['low'] = np.minimum.reduce([df['open'], df['high'], df['low'], df['close']])
    
    return df

def test_environment():
    """测试环境"""
    print("=== 测试交易环境 ===")
    
    # 生成数据
    df = generate_stock_data(500)
    print(f"生成数据: {len(df)} 条记录")
    
    # 创建环境
    env = SimpleStockEnv(df)
    
    # 检查环境
    if SB3_AVAILABLE:
        check_env(env)
        print("[OK] 环境检查通过")
    
    # 测试重置
    if GYM_VERSION == "gymnasium":
        obs, info = env.reset()
    else:
        obs = env.reset()
        info = env._get_info()
    print(f"[OK] 初始观测维度: {obs.shape}")
    
    # 测试几步交易
    for i in range(5):
        action = env.action_space.sample()
        if GYM_VERSION == "gymnasium":
            obs, reward, done, truncated, info = env.step(action)
        else:
            obs, reward, done, info = env.step(action)
        print(f"步骤 {i+1}: 动作={action}, 奖励={reward:.4f}, 净值=${info['net_worth']:,.2f}")
        
        if done:
            break
    
    print("[OK] 环境测试完成")
    return env

def simple_training():
    """简单训练示例"""
    if not SB3_AVAILABLE:
        print("stable-baselines3 不可用，跳过训练")
        return
        
    print("\n=== 开始简单训练 ===")
    
    # 生成训练数据
    train_df = generate_stock_data(800)
    eval_df = generate_stock_data(200)
    
    # 创建环境
    train_env = SimpleStockEnv(train_df)
    eval_env = SimpleStockEnv(eval_df)
    
    # 包装环境
    train_env = Monitor(train_env)
    eval_env = Monitor(eval_env)
    
    # 创建SAC模型
    model = SAC('MlpPolicy', train_env, verbose=1, seed=SEED)
    
    # 训练
    print("开始训练...")
    model.learn(total_timesteps=10000)
    
    # 评估
    print("\n=== 评估结果 ===")
    obs, _ = eval_env.reset() if GYM_VERSION == "gymnasium" else (eval_env.reset(), {})
    
    total_reward = 0
    for step in range(100):
        action, _ = model.predict(obs, deterministic=True)
        if GYM_VERSION == "gymnasium":
            obs, reward, done, truncated, info = eval_env.step(action)
        else:
            obs, reward, done, info = eval_env.step(action)
        total_reward += reward
        
        if step % 20 == 0:
            eval_env.render()
            
        if done:
            break
    
    print(f"\n总奖励: {total_reward:.3f}")
    print(f"最终净值: ${info['net_worth']:,.2f}")
    print(f"总收益: ${info['profit']:,.2f} ({info['profit_rate']:.2%})")

def main():
    """主函数"""
    print("简化版强化学习股票交易系统")
    print("=" * 50)
    
    # 设置随机种子
    np.random.seed(SEED)
    random.seed(SEED)
    
    # 测试环境
    env = test_environment()
    
    # 如果SB3可用，进行训练
    if SB3_AVAILABLE:
        simple_training()
    else:
        print("\n要使用完整功能，请安装 stable-baselines3:")
        print("pip install stable-baselines3")
        
        # 手动测试
        print("\n=== 手动测试 ===")
        obs, _ = env.reset() if GYM_VERSION == "gymnasium" else (env.reset(), {})
        
        for i in range(20):
            # 简单策略：基于价格趋势
            if i > 0:
                price_change = obs[3] - prev_price  # 收盘价变化
                if price_change > 0.01:
                    action = np.array([0.8, 0.5])  # 买入
                elif price_change < -0.01:
                    action = np.array([-0.8, 0.5])  # 卖出
                else:
                    action = np.array([0.0, 0.0])  # 持有
            else:
                action = np.array([0.0, 0.0])
                
            prev_price = obs[3]
            
            if GYM_VERSION == "gymnasium":
                obs, reward, done, truncated, info = env.step(action)
            else:
                obs, reward, done, info = env.step(action)
                
            if i % 5 == 0:
                env.render()
                
            if done:
                break
        
        print(f"\n手动测试完成")
        print(f"最终净值: ${info['net_worth']:,.2f}")
        print(f"收益率: {info['profit_rate']:.2%}")

if __name__ == "__main__":
    main()