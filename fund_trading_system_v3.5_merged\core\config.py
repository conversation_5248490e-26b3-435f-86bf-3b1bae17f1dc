"""
Configuration Management for Trading System v3.5

Provides centralized configuration management for all system components,
including trading parameters, agent settings, and system configurations.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, asdict, field
import os


@dataclass
class AgentConfig:
    """Configuration for individual agents"""
    enabled: bool = True
    weight: float = 0.333333
    confidence_threshold: float = 0.3
    max_features: int = 50
    model_path: Optional[str] = None
    custom_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RLConfig(AgentConfig):
    """Configuration for RL Agent"""
    algorithm: str = "SAC"
    learning_rate: float = 0.0003
    batch_size: int = 256
    buffer_size: int = 100000
    update_frequency: int = 1
    target_update_frequency: int = 1000
    gamma: float = 0.99
    tau: float = 0.005
    state_dim: int = 50
    action_dim: int = 3  # BUY, SELL, HOLD
    hidden_layers: List[int] = None
    
    def __post_init__(self):
        if self.hidden_layers is None:
            self.hidden_layers = [256, 256]


@dataclass
class LLMConfig(AgentConfig):
    """Configuration for LLM Agent"""
    model_name: str = "gpt-3.5-turbo"
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    max_tokens: int = 1000
    temperature: float = 0.3
    context_window: int = 10
    sentiment_weight: float = 0.6
    news_weight: float = 0.4
    retry_attempts: int = 3
    timeout: float = 30.0


@dataclass
class CZSCConfig(AgentConfig):
    """Configuration for CZSC Agent"""
    lookback_period: int = 50
    min_bi_length: int = 5
    min_duan_length: int = 3
    fractal_window: int = 5
    zhongshu_min_length: int = 3
    trend_threshold: float = 0.01
    volatility_window: int = 20
    support_resistance_levels: int = 5


@dataclass
class CoordinationConfig:
    """Configuration for agent coordination"""
    fusion_method: str = "weighted"  # weighted, voting, confidence_based
    conflict_resolution: str = "conservative"  # conservative, aggressive, adaptive
    min_confidence_threshold: float = 0.2
    conflict_threshold: float = 0.3
    decision_history_size: int = 100
    adaptive_weights: bool = True
    learning_rate: float = 0.01
    
    # Agent weights for coordination
    rl_weight: float = 0.4
    llm_weight: float = 0.3
    czsc_weight: float = 0.3


@dataclass
class RiskConfig:
    """Risk management configuration"""
    max_position_size: float = 0.2
    max_daily_loss: float = 0.05
    max_drawdown: float = 0.15
    stop_loss_pct: float = 0.08
    take_profit_pct: float = 0.15
    risk_free_rate: float = 0.03
    volatility_lookback: int = 30
    var_confidence: float = 0.95
    cvar_confidence: float = 0.95  # 新增CVaR置信度
    dynamic_stop_loss: bool = False  # 是否启用动态止损
    dynamic_stop_loss_window: int = 10  # 动态止损窗口
    risk_model: str = "basic"  # 风险管理策略名（basic/advanced/custom）
    correlation_threshold: float = 0.7


@dataclass
class DataConfig:
    """Data configuration"""
    data_source: str = "local"  # local, tushare, akshare, custom
    update_frequency: str = "1d"  # 1m, 5m, 15m, 30m, 1h, 1d
    cache_enabled: bool = True
    cache_duration: int = 3600  # seconds
    data_validation: bool = True
    outlier_detection: bool = True
    missing_data_strategy: str = "interpolate"  # drop, interpolate, forward_fill
    max_missing_ratio: float = 0.1


@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    start_date: str = "2020-01-01"
    end_date: str = "2023-12-31"
    initial_capital: float = 100000.0
    commission: float = 0.0003
    slippage: float = 0.0001
    benchmark: str = "000300.SH"  # CSI 300
    rebalance_frequency: str = "daily"
    max_holdings: int = 10
    min_holding_period: int = 1  # days


@dataclass
class SystemConfig:
    """System-wide configuration"""
    log_level: str = "INFO"
    log_file: Optional[str] = None
    max_workers: int = 4
    memory_limit: int = 8192  # MB
    gpu_enabled: bool = False
    cache_dir: str = "cache"
    model_dir: str = "models"
    data_dir: str = "data"
    output_dir: str = "output"
    seed: int = 42


class TradingConfig:
    """
    Main configuration class for the trading system.
    
    Manages all configuration aspects including agents, coordination,
    risk management, data, backtesting, and system settings.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize trading configuration.
        
        Args:
            config_file: Path to configuration file (JSON format)
        """
        self.logger = logging.getLogger("TradingConfig")
        
        # Initialize with default configurations
        self.rl_config = RLConfig()
        self.llm_config = LLMConfig()
        self.czsc_config = CZSCConfig()
        self.coordination_config = CoordinationConfig()
        self.risk_config = RiskConfig()
        self.data_config = DataConfig()
        self.backtest_config = BacktestConfig()
        self.system_config = SystemConfig()
        
        # Load from file if provided
        if config_file:
            self.load_from_file(config_file)
        
        # Load from environment variables
        self._load_from_env()
        
        # Normalize agent weights
        self._normalize_agent_weights()
        
        # Validate configuration
        self._validate_config()
        
        self.logger.info("Trading configuration initialized successfully")
    
    def load_from_file(self, config_file: str):
        """Load configuration from JSON file"""
        try:
            config_path = Path(config_file)
            if not config_path.exists():
                self.logger.warning(f"Configuration file not found: {config_file}")
                return
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Update configurations from file
            if 'rl' in config_data:
                self._update_config(self.rl_config, config_data['rl'])
            
            if 'llm' in config_data:
                self._update_config(self.llm_config, config_data['llm'])
            
            if 'czsc' in config_data:
                self._update_config(self.czsc_config, config_data['czsc'])
            
            if 'coordination' in config_data:
                self._update_config(self.coordination_config, config_data['coordination'])
            
            if 'risk' in config_data:
                self._update_config(self.risk_config, config_data['risk'])
            
            if 'data' in config_data:
                self._update_config(self.data_config, config_data['data'])
            
            if 'backtest' in config_data:
                self._update_config(self.backtest_config, config_data['backtest'])
            
            if 'system' in config_data:
                self._update_config(self.system_config, config_data['system'])
            
            self.logger.info(f"Configuration loaded from: {config_file}")
            
        except Exception as e:
            self.logger.error(f"Error loading configuration from {config_file}: {e}")
            raise
    
    def _update_config(self, config_obj, config_data: Dict[str, Any]):
        """Update configuration object with data from dictionary"""
        for key, value in config_data.items():
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
            else:
                self.logger.warning(f"Unknown configuration key: {key}")
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # LLM configuration from environment
        if os.getenv('LLM_API_KEY'):
            self.llm_config.api_key = os.getenv('LLM_API_KEY')
        
        if os.getenv('LLM_API_BASE'):
            self.llm_config.api_base = os.getenv('LLM_API_BASE')
        
        if os.getenv('LLM_MODEL_NAME'):
            self.llm_config.model_name = os.getenv('LLM_MODEL_NAME')
        
        # System configuration from environment
        if os.getenv('LOG_LEVEL'):
            self.system_config.log_level = os.getenv('LOG_LEVEL')
        
        if os.getenv('GPU_ENABLED'):
            self.system_config.gpu_enabled = os.getenv('GPU_ENABLED').lower() == 'true'
        
        # Data configuration from environment
        if os.getenv('DATA_SOURCE'):
            self.data_config.data_source = os.getenv('DATA_SOURCE')
        
        self.logger.debug("Environment variables loaded")
    
    def _normalize_agent_weights(self):
        """Normalize agent weights to sum to 1.0"""
        total_weight = (self.coordination_config.rl_weight + 
                       self.coordination_config.llm_weight + 
                       self.coordination_config.czsc_weight)
        
        if total_weight > 0:
            self.coordination_config.rl_weight /= total_weight
            self.coordination_config.llm_weight /= total_weight
            self.coordination_config.czsc_weight /= total_weight
            
            # Also update individual agent configs for backward compatibility
            self.rl_config.weight = self.coordination_config.rl_weight
            self.llm_config.weight = self.coordination_config.llm_weight
            self.czsc_config.weight = self.coordination_config.czsc_weight
            
            self.logger.debug(f"Normalized agent weights: RL={self.coordination_config.rl_weight:.6f}, "
                             f"LLM={self.coordination_config.llm_weight:.6f}, CZSC={self.coordination_config.czsc_weight:.6f}")
    
    def _validate_config(self):
        """Validate configuration values"""
        errors = []
        
        # Validate weights sum to 1.0 (approximately)
        total_weight = (self.coordination_config.rl_weight + 
                       self.coordination_config.llm_weight + 
                       self.coordination_config.czsc_weight)
        
        if abs(total_weight - 1.0) > 0.01:
            errors.append(f"Agent weights sum to {total_weight:.3f}, should be 1.0")
        
        # Validate risk parameters
        if self.risk_config.max_position_size <= 0 or self.risk_config.max_position_size > 1:
            errors.append("max_position_size must be between 0 and 1")
        
        if self.risk_config.stop_loss_pct <= 0:
            errors.append("stop_loss_pct must be positive")
        
        # Validate RL parameters
        if self.rl_config.state_dim != 50:
            errors.append("RL state_dim must be 50 (20 technical + 10 CZSC + 20 LLM)")
        
        # Validate system parameters
        if self.system_config.max_workers <= 0:
            errors.append("max_workers must be positive")
        
        if errors:
            error_msg = "Configuration validation failed:\n" + "\n".join(errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        
        self.logger.info("Configuration validation passed")
    
    def save_to_file(self, config_file: str):
        """Save current configuration to JSON file"""
        try:
            config_data = {
                'rl': asdict(self.rl_config),
                'llm': asdict(self.llm_config),
                'czsc': asdict(self.czsc_config),
                'coordination': asdict(self.coordination_config),
                'risk': asdict(self.risk_config),
                'data': asdict(self.data_config),
                'backtest': asdict(self.backtest_config),
                'system': asdict(self.system_config)
            }
            
            config_path = Path(config_file)
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to: {config_file}")
            
        except Exception as e:
            self.logger.error(f"Error saving configuration to {config_file}: {e}")
            raise
    
    def get_agent_weights(self) -> Dict[str, float]:
        """Get agent weights for coordination"""
        return {
            'rl': self.coordination_config.rl_weight,
            'llm': self.coordination_config.llm_weight,
            'czsc': self.coordination_config.czsc_weight
        }
    
    def update_agent_weights(self, weights: Dict[str, float]):
        """Update agent weights and normalize"""
        total = sum(weights.values())
        if total > 0:
            self.coordination_config.rl_weight = weights.get('rl', 0) / total
            self.coordination_config.llm_weight = weights.get('llm', 0) / total
            self.coordination_config.czsc_weight = weights.get('czsc', 0) / total
            
            # Update individual agent configs for backward compatibility
            self.rl_config.weight = self.coordination_config.rl_weight
            self.llm_config.weight = self.coordination_config.llm_weight
            self.czsc_config.weight = self.coordination_config.czsc_weight
        
        self.logger.info(f"Updated agent weights: {self.get_agent_weights()}")
    
    def get_feature_dimensions(self) -> Dict[str, int]:
        """Get feature dimensions for each analysis type"""
        return {
            'technical': 20,  # Technical indicators
            'czsc': 10,       # CZSC structure analysis
            'llm': 20,        # LLM context features
            'total': 50       # Total state dimension
        }
    
    def create_directories(self):
        """Create necessary directories for the system"""
        directories = [
            self.system_config.cache_dir,
            self.system_config.model_dir,
            self.system_config.data_dir,
            self.system_config.output_dir
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Created directory: {directory}")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        return {
            'agent_weights': self.get_agent_weights(),
            'feature_dimensions': self.get_feature_dimensions(),
            'risk_tolerance': {
                'max_position_size': self.risk_config.max_position_size,
                'stop_loss_pct': self.risk_config.stop_loss_pct,
                'take_profit_pct': self.risk_config.take_profit_pct
            },
            'coordination': {
                'fusion_method': self.coordination_config.fusion_method,
                'conflict_resolution': self.coordination_config.conflict_resolution
            },
            'system': {
                'log_level': self.system_config.log_level,
                'gpu_enabled': self.system_config.gpu_enabled,
                'max_workers': self.system_config.max_workers
            }
        }
    
    def __str__(self) -> str:
        """String representation of configuration"""
        summary = self.get_config_summary()
        return f"TradingConfig({json.dumps(summary, indent=2)})" 

    def get_risk_management_strategy(self):
        """预留风险管理策略接口，便于后续扩展"""
        return self.risk_config.risk_model 