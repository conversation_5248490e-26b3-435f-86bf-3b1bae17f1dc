"""
MACD参数动态优化器
根据基金特性动态调整MACD参数以提高分析准确性
"""

import logging
import sys
import os
from typing import Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.utils import *


class DynamicMACDOptimizer:
    """
    @class DynamicMACDOptimizer
    @brief MACD参数动态优化器
    @details 根据基金特性动态调整MACD参数以提高分析准确性
    """
    
    def __init__(self):
        self.base_params = {'macd_fast': 12, 'macd_slow': 26, 'macd_signal': 9}
        self.param_ranges = {'macd_fast': (6, 20), 'macd_slow': (20, 40), 'macd_signal': (6, 15)}
        self.optimization_stats = {'total_optimizations': 0, 'cache_hits': 0, 'parameter_adjustments': 0}
    
    def calculate_optimal_params(self, fund_features: Dict[str, float]) -> Dict[str, int]:
        """计算最优MACD参数"""
        try:
            self.optimization_stats['total_optimizations'] += 1
            
            liquidity_score = fund_features.get('liquidity_score', 0.5)
            volatility_score = fund_features.get('volatility_score', 0.5)
            category_adjustment = fund_features.get('category_adjustment', {})
            
            optimized_params = self.base_params.copy()
            optimized_params = self.optimize_for_liquidity(liquidity_score, optimized_params)
            optimized_params = self.optimize_for_volatility(volatility_score, optimized_params)
            optimized_params = self.apply_category_adjustment(optimized_params, category_adjustment)
            
            if self.validate_params(optimized_params):
                self.optimization_stats['parameter_adjustments'] += 1
                return optimized_params
            else:
                return self.base_params
        except Exception as e:
            logging.error(f"MACD参数优化失败: {e}")
            return self.base_params
    
    def optimize_for_liquidity(self, liquidity_score: float, base_params: Dict[str, int]) -> Dict[str, int]:
        """基于流动性优化参数"""
        try:
            params = base_params.copy()
            
            if liquidity_score > 0.7:
                adjustment = -2
            elif liquidity_score > 0.5:
                adjustment = -1
            elif liquidity_score < 0.3:
                adjustment = 2
            elif liquidity_score < 0.5:
                adjustment = 1
            else:
                adjustment = 0
            
            params['macd_fast'] = max(self.param_ranges['macd_fast'][0],
                                     min(self.param_ranges['macd_fast'][1], params['macd_fast'] + adjustment))
            params['macd_slow'] = max(self.param_ranges['macd_slow'][0],
                                     min(self.param_ranges['macd_slow'][1], params['macd_slow'] + adjustment * 2))
            return params
        except Exception:
            return base_params
    
    def optimize_for_volatility(self, volatility_score: float, base_params: Dict[str, int]) -> Dict[str, int]:
        """基于波动性优化参数"""
        try:
            params = base_params.copy()
            
            if volatility_score > 0.8:
                signal_adjustment = 3
            elif volatility_score > 0.6:
                signal_adjustment = 2
            elif volatility_score > 0.4:
                signal_adjustment = 1
            elif volatility_score < 0.2:
                signal_adjustment = -1
            else:
                signal_adjustment = 0
            
            params['macd_signal'] = max(self.param_ranges['macd_signal'][0],
                                       min(self.param_ranges['macd_signal'][1], params['macd_signal'] + signal_adjustment))
            return params
        except Exception:
            return base_params
    
    def apply_category_adjustment(self, params: Dict[str, int], category_adjustment: Dict[str, float]) -> Dict[str, int]:
        """应用类别调整"""
        try:
            adjusted_params = params.copy()
            sensitivity_adj = category_adjustment.get('sensitivity', 0.0)
            smoothness_adj = category_adjustment.get('smoothness', 0.0)
            
            if abs(sensitivity_adj) > 0.05:
                fast_adj = int(sensitivity_adj * -10)
                slow_adj = int(sensitivity_adj * -20)
                
                adjusted_params['macd_fast'] = max(self.param_ranges['macd_fast'][0],
                                                  min(self.param_ranges['macd_fast'][1], adjusted_params['macd_fast'] + fast_adj))
                adjusted_params['macd_slow'] = max(self.param_ranges['macd_slow'][0], 
                                                  min(self.param_ranges['macd_slow'][1], adjusted_params['macd_slow'] + slow_adj))
            
            if abs(smoothness_adj) > 0.05:
                signal_adj = int(smoothness_adj * 10)
                adjusted_params['macd_signal'] = max(self.param_ranges['macd_signal'][0],
                                                    min(self.param_ranges['macd_signal'][1], adjusted_params['macd_signal'] + signal_adj))
            
            return adjusted_params
        except Exception:
            return params
    
    def validate_params(self, params: Dict[str, int]) -> bool:
        """验证参数有效性"""
        try:
            for param_name, value in params.items():
                if param_name in self.param_ranges:
                    min_val, max_val = self.param_ranges[param_name]
                    if not (min_val <= value <= max_val):
                        return False
            
            if params['macd_fast'] >= params['macd_slow']:
                return False
            if params['macd_signal'] > params['macd_fast']:
                return False
            
            return True
        except Exception:
            return False
    
    def get_optimization_stats(self) -> Dict[str, int]:
        """获取优化统计信息"""
        return self.optimization_stats.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.optimization_stats = {'total_optimizations': 0, 'cache_hits': 0, 'parameter_adjustments': 0}
    
    def get_param_ranges(self) -> Dict[str, tuple]:
        """获取参数范围"""
        return self.param_ranges.copy()
    
    def update_param_ranges(self, new_ranges: Dict[str, tuple]) -> None:
        """更新参数范围"""
        for param_name, range_tuple in new_ranges.items():
            if param_name in self.param_ranges and len(range_tuple) == 2:
                self.param_ranges[param_name] = range_tuple
    
    def get_base_params(self) -> Dict[str, int]:
        """获取基础参数"""
        return self.base_params.copy()
    
    def update_base_params(self, new_params: Dict[str, int]) -> None:
        """更新基础参数"""
        if self.validate_params(new_params):
            self.base_params.update(new_params)
        else:
            logging.warning("Invalid base parameters provided, keeping current values")
