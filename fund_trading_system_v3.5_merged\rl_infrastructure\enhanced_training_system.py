"""
Enhanced RL Training System

Advanced reinforcement learning training system with intelligent hyperparameter
optimization, sophisticated convergence detection, and comprehensive training
flow management.
"""

import logging
import time
import json
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, field
from pathlib import Path
from collections import deque
import threading
from enum import Enum

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.optim.lr_scheduler import ReduceLROnPlateau, CosineAnnealingLR
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    from skopt import gp_minimize
    from skopt.space import Real, Integer, Categorical
    from skopt.utils import use_named_args
    BAYESIAN_OPT_AVAILABLE = True
except ImportError:
    BAYESIAN_OPT_AVAILABLE = False
    # Create dummy classes for fallback
    class Real:
        def __init__(self, low, high, name=None):
            self.low = low
            self.high = high
            self.name = name

    class Integer:
        def __init__(self, low, high, name=None):
            self.low = low
            self.high = high
            self.name = name

# 导入新增的组件
try:
    from .online_learning_manager import OnlineLearningManager, OnlineLearningConfig
    from .ab_testing_framework import ABTestingFramework, ABTestConfig, ModelVariant
    from .advanced_evaluator import AdvancedEvaluator
    from .evaluation_report_generator import EvaluationReportGenerator, ReportConfig
    ENHANCED_COMPONENTS_AVAILABLE = True
except ImportError:
    ENHANCED_COMPONENTS_AVAILABLE = False

    class Categorical:
        def __init__(self, categories, name=None):
            self.categories = categories
            self.name = name


class TrainingPhase(Enum):
    """Training phase enumeration"""
    INITIALIZATION = "initialization"
    EXPLORATION = "exploration"
    EXPLOITATION = "exploitation"
    FINE_TUNING = "fine_tuning"
    COMPLETED = "completed"


@dataclass
class TrainingMetrics:
    """Training metrics tracking"""
    episode: int = 0
    total_reward: float = 0.0
    average_reward: float = 0.0
    loss: float = 0.0
    learning_rate: float = 0.0
    epsilon: float = 0.0
    training_time: float = 0.0
    memory_usage: float = 0.0
    convergence_score: float = 0.0
    phase: TrainingPhase = TrainingPhase.INITIALIZATION
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'episode': self.episode,
            'total_reward': self.total_reward,
            'average_reward': self.average_reward,
            'loss': self.loss,
            'learning_rate': self.learning_rate,
            'epsilon': self.epsilon,
            'training_time': self.training_time,
            'memory_usage': self.memory_usage,
            'convergence_score': self.convergence_score,
            'phase': self.phase.value
        }


@dataclass
class HyperparameterConfig:
    """Hyperparameter configuration"""
    learning_rate: float = 0.001
    batch_size: int = 32
    epsilon_start: float = 1.0
    epsilon_end: float = 0.01
    epsilon_decay: float = 0.995
    gamma: float = 0.99
    tau: float = 0.005
    hidden_dims: List[int] = field(default_factory=lambda: [256, 256])
    dropout_rate: float = 0.1
    weight_decay: float = 1e-5
    gradient_clip: float = 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'learning_rate': self.learning_rate,
            'batch_size': self.batch_size,
            'epsilon_start': self.epsilon_start,
            'epsilon_end': self.epsilon_end,
            'epsilon_decay': self.epsilon_decay,
            'gamma': self.gamma,
            'tau': self.tau,
            'hidden_dims': self.hidden_dims,
            'dropout_rate': self.dropout_rate,
            'weight_decay': self.weight_decay,
            'gradient_clip': self.gradient_clip
        }


class AdvancedConvergenceDetector:
    """
    Advanced convergence detection system
    
    Uses multiple criteria to determine training convergence:
    - Reward stability
    - Loss plateau detection
    - Performance improvement rate
    - Statistical significance tests
    """
    
    def __init__(self, 
                 window_size: int = 100,
                 stability_threshold: float = 0.05,
                 improvement_threshold: float = 0.01,
                 patience: int = 50):
        """
        Initialize convergence detector
        
        Args:
            window_size: Window size for moving averages
            stability_threshold: Threshold for reward stability
            improvement_threshold: Minimum improvement rate
            patience: Episodes to wait without improvement
        """
        self.window_size = window_size
        self.stability_threshold = stability_threshold
        self.improvement_threshold = improvement_threshold
        self.patience = patience
        
        # Tracking variables
        self.reward_history = deque(maxlen=window_size * 2)
        self.loss_history = deque(maxlen=window_size * 2)
        self.best_performance = float('-inf')
        self.episodes_without_improvement = 0
        self.convergence_score = 0.0
        
        self.logger = logging.getLogger("ConvergenceDetector")
    
    def update(self, reward: float, loss: float) -> Tuple[bool, float]:
        """
        Update convergence detector with new metrics
        
        Args:
            reward: Episode reward
            loss: Training loss
            
        Returns:
            Tuple of (converged, convergence_score)
        """
        self.reward_history.append(reward)
        self.loss_history.append(loss)
        
        # Calculate convergence score
        self.convergence_score = self._calculate_convergence_score()
        
        # Check convergence criteria
        converged = self._check_convergence()
        
        return converged, self.convergence_score
    
    def _calculate_convergence_score(self) -> float:
        """Calculate overall convergence score (0-1)"""
        if len(self.reward_history) < self.window_size:
            return 0.0
        
        scores = []
        
        # Reward stability score
        recent_rewards = list(self.reward_history)[-self.window_size:]
        reward_std = np.std(recent_rewards)
        reward_mean = np.mean(recent_rewards)
        if reward_mean != 0:
            stability_score = max(0, 1 - (reward_std / abs(reward_mean)))
            scores.append(stability_score)
        
        # Loss plateau score
        if len(self.loss_history) >= self.window_size:
            recent_losses = list(self.loss_history)[-self.window_size:]
            loss_trend = np.polyfit(range(len(recent_losses)), recent_losses, 1)[0]
            plateau_score = max(0, 1 - abs(loss_trend))
            scores.append(plateau_score)
        
        # Performance improvement score
        if len(self.reward_history) >= self.window_size * 2:
            early_rewards = list(self.reward_history)[-self.window_size * 2:-self.window_size]
            recent_rewards = list(self.reward_history)[-self.window_size:]
            
            early_mean = np.mean(early_rewards)
            recent_mean = np.mean(recent_rewards)
            
            if early_mean != 0:
                improvement_rate = (recent_mean - early_mean) / abs(early_mean)
                improvement_score = min(1.0, max(0, improvement_rate / self.improvement_threshold))
                scores.append(improvement_score)
        
        return np.mean(scores) if scores else 0.0
    
    def _check_convergence(self) -> bool:
        """Check if training has converged"""
        if len(self.reward_history) < self.window_size:
            return False
        
        # Check if convergence score is high enough
        if self.convergence_score < 0.8:
            return False
        
        # Check for improvement
        recent_performance = np.mean(list(self.reward_history)[-self.window_size:])
        
        if recent_performance > self.best_performance:
            self.best_performance = recent_performance
            self.episodes_without_improvement = 0
        else:
            self.episodes_without_improvement += 1
        
        # Converged if no improvement for patience episodes
        return self.episodes_without_improvement >= self.patience


class BayesianHyperparameterOptimizer:
    """
    Bayesian optimization for hyperparameter tuning
    
    Uses Gaussian Process optimization to efficiently search
    the hyperparameter space.
    """
    
    def __init__(self, 
                 search_space: Dict[str, Any],
                 n_calls: int = 20,
                 random_state: int = 42):
        """
        Initialize Bayesian optimizer
        
        Args:
            search_space: Hyperparameter search space
            n_calls: Number of optimization calls
            random_state: Random seed
        """
        self.search_space = search_space
        self.n_calls = n_calls
        self.random_state = random_state
        
        # Convert search space to skopt format
        self.dimensions = self._convert_search_space()
        self.dimension_names = list(search_space.keys())
        
        # Optimization results
        self.optimization_results = []
        self.best_params = None
        self.best_score = float('-inf')
        
        self.logger = logging.getLogger("BayesianOptimizer")
    
    def optimize(self, objective_function: Callable[[Dict[str, Any]], float]) -> Dict[str, Any]:
        """
        Optimize hyperparameters using Bayesian optimization
        
        Args:
            objective_function: Function to optimize (higher is better)
            
        Returns:
            Best hyperparameters found
        """
        if not BAYESIAN_OPT_AVAILABLE:
            self.logger.warning("Bayesian optimization not available, using random search")
            return self._random_search(objective_function)
        
        @use_named_args(self.dimensions)
        def objective(**params):
            try:
                score = objective_function(params)
                self.optimization_results.append({
                    'params': params.copy(),
                    'score': score,
                    'timestamp': datetime.now()
                })
                
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params.copy()
                
                self.logger.info(f"Trial completed: score={score:.4f}, params={params}")
                return -score  # Minimize negative score
                
            except Exception as e:
                self.logger.error(f"Objective function error: {e}")
                return 1e6  # Large penalty for failed trials
        
        # Run Bayesian optimization
        self.logger.info(f"Starting Bayesian optimization with {self.n_calls} calls")
        
        result = gp_minimize(
            func=objective,
            dimensions=self.dimensions,
            n_calls=self.n_calls,
            random_state=self.random_state,
            acq_func='EI',  # Expected Improvement
            n_initial_points=5
        )
        
        # Extract best parameters
        best_params_list = result.x
        self.best_params = dict(zip(self.dimension_names, best_params_list))
        self.best_score = -result.fun
        
        self.logger.info(f"Optimization completed. Best score: {self.best_score:.4f}")
        self.logger.info(f"Best parameters: {self.best_params}")
        
        return self.best_params
    
    def _convert_search_space(self) -> List:
        """Convert search space to skopt dimensions"""
        dimensions = []
        
        for param_name, param_config in self.search_space.items():
            if param_config['type'] == 'real':
                dimensions.append(Real(
                    low=param_config['low'],
                    high=param_config['high'],
                    name=param_name
                ))
            elif param_config['type'] == 'integer':
                dimensions.append(Integer(
                    low=param_config['low'],
                    high=param_config['high'],
                    name=param_name
                ))
            elif param_config['type'] == 'categorical':
                dimensions.append(Categorical(
                    categories=param_config['choices'],
                    name=param_name
                ))
        
        return dimensions
    
    def _random_search(self, objective_function: Callable[[Dict[str, Any]], float]) -> Dict[str, Any]:
        """Fallback random search when Bayesian optimization is not available"""
        self.logger.info("Running random search optimization")
        
        for i in range(self.n_calls):
            # Generate random parameters
            params = {}
            for param_name, param_config in self.search_space.items():
                if param_config['type'] == 'real':
                    params[param_name] = np.random.uniform(param_config['low'], param_config['high'])
                elif param_config['type'] == 'integer':
                    params[param_name] = np.random.randint(param_config['low'], param_config['high'] + 1)
                elif param_config['type'] == 'categorical':
                    params[param_name] = np.random.choice(param_config['choices'])
            
            # Evaluate
            try:
                score = objective_function(params)
                self.optimization_results.append({
                    'params': params.copy(),
                    'score': score,
                    'timestamp': datetime.now()
                })
                
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params.copy()
                
                self.logger.info(f"Random trial {i+1}: score={score:.4f}")
                
            except Exception as e:
                self.logger.error(f"Random trial {i+1} failed: {e}")
        
        return self.best_params or {}
    
    def get_optimization_history(self) -> pd.DataFrame:
        """Get optimization history as DataFrame"""
        if not self.optimization_results:
            return pd.DataFrame()
        
        return pd.DataFrame(self.optimization_results)


class EnhancedTrainingSystem:
    """
    Enhanced RL training system with advanced features
    
    Features:
    - Intelligent hyperparameter optimization
    - Advanced convergence detection
    - Adaptive learning rate scheduling
    - Early stopping with patience
    - Comprehensive training monitoring
    - Automatic checkpoint management
    """
    
    def __init__(self, 
                 config: Dict[str, Any],
                 save_dir: str = "models",
                 enable_bayesian_opt: bool = True):
        """
        Initialize enhanced training system
        
        Args:
            config: Training configuration
            save_dir: Directory to save models and logs
            enable_bayesian_opt: Enable Bayesian optimization
        """
        self.config = config
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(exist_ok=True)
        self.enable_bayesian_opt = enable_bayesian_opt and BAYESIAN_OPT_AVAILABLE
        
        # Training components
        self.convergence_detector = AdvancedConvergenceDetector()
        self.hyperparameter_optimizer = None

        # Enhanced components (新增)
        self.online_learning_manager = None
        self.ab_testing_framework = None
        self.advanced_evaluator = None
        self.report_generator = None

        # 初始化增强组件
        if ENHANCED_COMPONENTS_AVAILABLE:
            self._initialize_enhanced_components()

        # Training state
        self.current_phase = TrainingPhase.INITIALIZATION
        self.training_metrics = []
        self.best_model_path = None
        self.best_performance = float('-inf')

        # Monitoring
        self.training_start_time = None
        self.last_checkpoint_time = None

        self.logger = logging.getLogger("EnhancedTrainingSystem")
        self.logger.info("Enhanced training system initialized")

    def _initialize_enhanced_components(self):
        """初始化增强组件"""
        try:
            # 在线学习管理器
            online_config = OnlineLearningConfig(
                mode=self.config.get('online_learning_mode', 'adaptive'),
                learning_rate=self.config.get('learning_rate', 0.001),
                batch_size=self.config.get('batch_size', 32),
                buffer_size=self.config.get('buffer_size', 10000)
            )
            self.online_learning_manager = OnlineLearningManager(online_config)

            # 高级评估器
            self.advanced_evaluator = AdvancedEvaluator(
                risk_free_rate=self.config.get('risk_free_rate', 0.02)
            )

            # 报告生成器
            report_config = ReportConfig(
                title=self.config.get('report_title', 'RL模型训练报告'),
                include_charts=self.config.get('include_charts', True)
            )
            self.report_generator = EvaluationReportGenerator(report_config)

            self.logger.info("增强组件初始化完成")

        except Exception as e:
            self.logger.warning(f"增强组件初始化失败: {str(e)}")

    def setup_ab_testing(self, model_variants: List[Dict[str, Any]],
                        test_name: str = "RL_Model_AB_Test") -> bool:
        """设置A/B测试"""
        if not ENHANCED_COMPONENTS_AVAILABLE:
            self.logger.warning("增强组件不可用，无法设置A/B测试")
            return False

        try:
            # 构建变体列表
            variants = []
            for i, variant_data in enumerate(model_variants):
                variant = ModelVariant(
                    variant_id=variant_data.get('variant_id', f'model_{i}'),
                    model_name=variant_data.get('model_name', f'Model_{i}'),
                    model_path=variant_data.get('model_path', ''),
                    traffic_weight=variant_data.get('traffic_weight', 1.0),
                    description=variant_data.get('description', f'Model variant {i}'),
                    metadata=variant_data.get('metadata', {})
                )
                variants.append(variant)

            # 创建A/B测试配置
            ab_config = ABTestConfig(
                test_name=test_name,
                variants=variants,
                min_sample_size=self.config.get('ab_min_samples', 100),
                max_duration_hours=self.config.get('ab_max_hours', 24),
                significance_level=self.config.get('ab_significance', 0.05)
            )

            # 创建A/B测试框架
            self.ab_testing_framework = ABTestingFramework(ab_config)

            self.logger.info(f"A/B测试设置完成: {test_name}, {len(variants)}个变体")
            return True

        except Exception as e:
            self.logger.error(f"A/B测试设置失败: {str(e)}")
            return False

    def start_online_learning(self, model, optimizer=None) -> bool:
        """启动在线学习"""
        if not self.online_learning_manager:
            self.logger.warning("在线学习管理器未初始化")
            return False

        try:
            self.online_learning_manager.set_model(model, optimizer)
            self.logger.info("在线学习已启动")
            return True

        except Exception as e:
            self.logger.error(f"启动在线学习失败: {str(e)}")
            return False

    def add_online_training_data(self, state, action, reward, next_state, done):
        """添加在线训练数据"""
        if self.online_learning_manager:
            self.online_learning_manager.add_training_data(state, action, reward, next_state, done)

    def trigger_manual_learning(self):
        """手动触发学习"""
        if self.online_learning_manager:
            return self.online_learning_manager.manual_trigger_learning()
        return None

    def get_online_learning_status(self) -> Dict[str, Any]:
        """获取在线学习状态"""
        if self.online_learning_manager:
            return self.online_learning_manager.get_learning_status()
        return {}

    def start_ab_test(self) -> bool:
        """开始A/B测试"""
        if not self.ab_testing_framework:
            self.logger.warning("A/B测试框架未设置")
            return False

        try:
            self.ab_testing_framework.start_test()
            self.logger.info("A/B测试已开始")
            return True

        except Exception as e:
            self.logger.error(f"开始A/B测试失败: {str(e)}")
            return False

    def allocate_ab_variant(self, request_id: str = None) -> Optional[str]:
        """分配A/B测试变体"""
        if self.ab_testing_framework:
            try:
                return self.ab_testing_framework.allocate_variant(request_id)
            except Exception as e:
                self.logger.error(f"分配A/B变体失败: {str(e)}")
        return None

    def record_ab_result(self, variant_id: str, metrics: Dict[str, float]):
        """记录A/B测试结果"""
        if self.ab_testing_framework:
            self.ab_testing_framework.record_result(variant_id, metrics)

    def get_ab_test_results(self) -> Dict[str, Any]:
        """获取A/B测试结果"""
        if self.ab_testing_framework:
            return self.ab_testing_framework.get_current_results()
        return {}

    def generate_ab_test_report(self, output_dir: str) -> Optional[str]:
        """生成A/B测试报告"""
        if not self.ab_testing_framework:
            return None

        try:
            report = self.ab_testing_framework.generate_report()
            filepath = self.ab_testing_framework.save_results(
                os.path.join(output_dir, "ab_test_report.json")
            )
            self.logger.info(f"A/B测试报告已生成: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"生成A/B测试报告失败: {str(e)}")
            return None

    def train_with_optimization(self,
                              model_factory: Callable[[Dict[str, Any]], Any],
                              training_function: Callable[[Any, Dict[str, Any]], Dict[str, Any]],
                              search_space: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Train model with hyperparameter optimization
        
        Args:
            model_factory: Function to create model from hyperparameters
            training_function: Function to train model
            search_space: Hyperparameter search space
            
        Returns:
            Training results with best model and metrics
        """
        self.training_start_time = datetime.now()
        
        if search_space and self.enable_bayesian_opt:
            return self._train_with_bayesian_optimization(
                model_factory, training_function, search_space
            )
        else:
            # Train with default hyperparameters
            default_params = HyperparameterConfig().to_dict()
            model = model_factory(default_params)
            return self._train_single_model(model, default_params, training_function)
    
    def _train_with_bayesian_optimization(self,
                                        model_factory: Callable[[Dict[str, Any]], Any],
                                        training_function: Callable[[Any, Dict[str, Any]], Dict[str, Any]],
                                        search_space: Dict[str, Any]) -> Dict[str, Any]:
        """Train with Bayesian hyperparameter optimization"""
        self.logger.info("Starting Bayesian hyperparameter optimization")
        
        self.hyperparameter_optimizer = BayesianHyperparameterOptimizer(search_space)
        
        def objective_function(params: Dict[str, Any]) -> float:
            """Objective function for optimization"""
            try:
                model = model_factory(params)
                result = self._train_single_model(model, params, training_function)
                return result.get('final_performance', 0.0)
            except Exception as e:
                self.logger.error(f"Training failed with params {params}: {e}")
                return float('-inf')
        
        # Run optimization
        best_params = self.hyperparameter_optimizer.optimize(objective_function)
        
        # Train final model with best parameters
        self.logger.info(f"Training final model with best parameters: {best_params}")
        final_model = model_factory(best_params)
        final_result = self._train_single_model(final_model, best_params, training_function)
        
        # Add optimization results
        final_result['optimization_history'] = self.hyperparameter_optimizer.get_optimization_history()
        final_result['best_hyperparameters'] = best_params
        
        return final_result
    
    def _train_single_model(self,
                          model: Any,
                          hyperparams: Dict[str, Any],
                          training_function: Callable[[Any, Dict[str, Any]], Dict[str, Any]]) -> Dict[str, Any]:
        """Train a single model with given hyperparameters"""
        self.logger.info(f"Training model with hyperparameters: {hyperparams}")
        
        # Reset training state
        self.convergence_detector = AdvancedConvergenceDetector()
        self.training_metrics = []
        self.current_phase = TrainingPhase.EXPLORATION
        
        # Train model
        training_result = training_function(model, hyperparams)
        
        # Compile final results
        result = {
            'model': model,
            'hyperparameters': hyperparams,
            'training_metrics': self.training_metrics,
            'final_performance': training_result.get('final_performance', 0.0),
            'convergence_episode': training_result.get('convergence_episode', -1),
            'training_time': (datetime.now() - self.training_start_time).total_seconds(),
            'best_model_path': self.best_model_path
        }
        
        return result
    
    def update_training_metrics(self, episode: int, reward: float, loss: float, **kwargs) -> bool:
        """
        Update training metrics and check convergence
        
        Args:
            episode: Current episode
            reward: Episode reward
            loss: Training loss
            **kwargs: Additional metrics
            
        Returns:
            True if training should stop (converged)
        """
        # Update convergence detector
        converged, convergence_score = self.convergence_detector.update(reward, loss)
        
        # Create metrics object
        metrics = TrainingMetrics(
            episode=episode,
            total_reward=reward,
            average_reward=np.mean([m.total_reward for m in self.training_metrics[-100:]] + [reward]),
            loss=loss,
            convergence_score=convergence_score,
            phase=self.current_phase,
            **kwargs
        )
        
        self.training_metrics.append(metrics)
        
        # Update training phase
        self._update_training_phase(episode, convergence_score)
        
        # Log progress
        if episode % 50 == 0:
            self.logger.info(
                f"Episode {episode}: reward={reward:.3f}, "
                f"loss={loss:.6f}, convergence={convergence_score:.3f}, "
                f"phase={self.current_phase.value}"
            )
        
        return converged
    
    def _update_training_phase(self, episode: int, convergence_score: float):
        """Update training phase based on progress"""
        if convergence_score > 0.8:
            self.current_phase = TrainingPhase.FINE_TUNING
        elif convergence_score > 0.5:
            self.current_phase = TrainingPhase.EXPLOITATION
        elif episode > 100:
            self.current_phase = TrainingPhase.EXPLORATION
        else:
            self.current_phase = TrainingPhase.INITIALIZATION
    
    def save_checkpoint(self, model: Any, episode: int, performance: float):
        """Save training checkpoint"""
        checkpoint_path = self.save_dir / f"checkpoint_episode_{episode}.pt"
        
        if TORCH_AVAILABLE and hasattr(model, 'state_dict'):
            torch.save({
                'model_state_dict': model.state_dict(),
                'episode': episode,
                'performance': performance,
                'hyperparameters': getattr(self, 'current_hyperparams', {}),
                'training_metrics': [m.to_dict() for m in self.training_metrics]
            }, checkpoint_path)
        
        # Update best model if performance improved
        if performance > self.best_performance:
            self.best_performance = performance
            self.best_model_path = str(checkpoint_path)
        
        self.last_checkpoint_time = datetime.now()
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def get_training_summary(self) -> Dict[str, Any]:
        """Get comprehensive training summary"""
        if not self.training_metrics:
            return {}
        
        metrics_df = pd.DataFrame([m.to_dict() for m in self.training_metrics])
        
        return {
            'total_episodes': len(self.training_metrics),
            'final_performance': self.training_metrics[-1].total_reward,
            'best_performance': self.best_performance,
            'convergence_score': self.training_metrics[-1].convergence_score,
            'training_time': (datetime.now() - self.training_start_time).total_seconds() if self.training_start_time else 0,
            'final_phase': self.current_phase.value,
            'metrics_summary': {
                'avg_reward': metrics_df['total_reward'].mean(),
                'max_reward': metrics_df['total_reward'].max(),
                'min_reward': metrics_df['total_reward'].min(),
                'avg_loss': metrics_df['loss'].mean(),
                'final_convergence': metrics_df['convergence_score'].iloc[-1] if len(metrics_df) > 0 else 0
            }
        }


class AdaptiveLearningRateScheduler:
    """
    Adaptive learning rate scheduler for RL training

    Automatically adjusts learning rate based on training progress
    and convergence indicators.
    """

    def __init__(self,
                 initial_lr: float = 0.001,
                 min_lr: float = 1e-6,
                 patience: int = 20,
                 factor: float = 0.5,
                 warmup_episodes: int = 100):
        """
        Initialize adaptive scheduler

        Args:
            initial_lr: Initial learning rate
            min_lr: Minimum learning rate
            patience: Episodes to wait before reducing LR
            factor: Factor to reduce LR by
            warmup_episodes: Episodes for warmup phase
        """
        self.initial_lr = initial_lr
        self.min_lr = min_lr
        self.patience = patience
        self.factor = factor
        self.warmup_episodes = warmup_episodes

        self.current_lr = initial_lr
        self.best_performance = float('-inf')
        self.episodes_without_improvement = 0
        self.episode_count = 0

        self.logger = logging.getLogger("AdaptiveLRScheduler")

    def step(self, performance: float) -> float:
        """
        Update learning rate based on performance

        Args:
            performance: Current performance metric

        Returns:
            Updated learning rate
        """
        self.episode_count += 1

        # Warmup phase
        if self.episode_count <= self.warmup_episodes:
            self.current_lr = self.initial_lr * (self.episode_count / self.warmup_episodes)
            return self.current_lr

        # Check for improvement
        if performance > self.best_performance:
            self.best_performance = performance
            self.episodes_without_improvement = 0
        else:
            self.episodes_without_improvement += 1

        # Reduce learning rate if no improvement
        if self.episodes_without_improvement >= self.patience:
            old_lr = self.current_lr
            self.current_lr = max(self.min_lr, self.current_lr * self.factor)

            if self.current_lr < old_lr:
                self.logger.info(f"Reducing learning rate: {old_lr:.6f} -> {self.current_lr:.6f}")
                self.episodes_without_improvement = 0

        return self.current_lr

    def get_lr(self) -> float:
        """Get current learning rate"""
        return self.current_lr


class EarlyStoppingManager:
    """
    Early stopping manager for RL training

    Monitors training progress and stops training when
    no improvement is observed for a specified period.
    """

    def __init__(self,
                 patience: int = 100,
                 min_delta: float = 0.001,
                 restore_best_weights: bool = True):
        """
        Initialize early stopping manager

        Args:
            patience: Episodes to wait without improvement
            min_delta: Minimum change to qualify as improvement
            restore_best_weights: Whether to restore best weights
        """
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights

        self.best_performance = float('-inf')
        self.best_weights = None
        self.episodes_without_improvement = 0
        self.should_stop = False

        self.logger = logging.getLogger("EarlyStoppingManager")

    def check(self, performance: float, model_weights: Any = None) -> bool:
        """
        Check if training should stop early

        Args:
            performance: Current performance metric
            model_weights: Current model weights

        Returns:
            True if training should stop
        """
        # Check for improvement
        if performance > self.best_performance + self.min_delta:
            self.best_performance = performance
            self.episodes_without_improvement = 0

            if self.restore_best_weights and model_weights is not None:
                self.best_weights = model_weights

        else:
            self.episodes_without_improvement += 1

        # Check if should stop
        if self.episodes_without_improvement >= self.patience:
            self.should_stop = True
            self.logger.info(f"Early stopping triggered after {self.episodes_without_improvement} episodes without improvement")

        return self.should_stop

    def get_best_weights(self) -> Any:
        """Get best model weights"""
        return self.best_weights


class TrainingProgressMonitor:
    """
    Real-time training progress monitor

    Tracks and visualizes training progress with
    comprehensive metrics and alerts.
    """

    def __init__(self,
                 update_frequency: int = 10,
                 save_frequency: int = 100):
        """
        Initialize progress monitor

        Args:
            update_frequency: How often to update metrics
            save_frequency: How often to save progress
        """
        self.update_frequency = update_frequency
        self.save_frequency = save_frequency

        self.metrics_history = []
        self.alerts = []
        self.start_time = None

        self.logger = logging.getLogger("TrainingProgressMonitor")

    def start_monitoring(self):
        """Start monitoring training progress"""
        self.start_time = datetime.now()
        self.logger.info("Training progress monitoring started")

    def update(self, episode: int, metrics: Dict[str, Any]):
        """Update training progress"""
        if episode % self.update_frequency == 0:
            # Add timestamp and episode info
            metrics_with_time = {
                'episode': episode,
                'timestamp': datetime.now(),
                'elapsed_time': (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
                **metrics
            }

            self.metrics_history.append(metrics_with_time)

            # Check for alerts
            self._check_alerts(metrics_with_time)

            # Log progress
            self.logger.info(f"Episode {episode}: {self._format_metrics(metrics)}")

        # Save progress periodically
        if episode % self.save_frequency == 0:
            self._save_progress()

    def _check_alerts(self, metrics: Dict[str, Any]):
        """Check for training alerts"""
        # Check for performance degradation
        if len(self.metrics_history) > 10:
            recent_performance = [m.get('reward', 0) for m in self.metrics_history[-10:]]
            if len(recent_performance) > 5:
                trend = np.polyfit(range(len(recent_performance)), recent_performance, 1)[0]
                if trend < -0.1:  # Significant negative trend
                    self.alerts.append({
                        'type': 'performance_degradation',
                        'message': 'Performance is degrading',
                        'timestamp': datetime.now(),
                        'episode': metrics['episode']
                    })

        # Check for loss explosion
        loss = metrics.get('loss', 0)
        if loss > 10.0:  # Arbitrary threshold
            self.alerts.append({
                'type': 'loss_explosion',
                'message': f'Loss is very high: {loss:.4f}',
                'timestamp': datetime.now(),
                'episode': metrics['episode']
            })

    def _format_metrics(self, metrics: Dict[str, Any]) -> str:
        """Format metrics for logging"""
        formatted = []
        for key, value in metrics.items():
            if key in ['episode', 'timestamp', 'elapsed_time']:
                continue
            if isinstance(value, float):
                formatted.append(f"{key}={value:.4f}")
            else:
                formatted.append(f"{key}={value}")
        return ", ".join(formatted)

    def _save_progress(self):
        """Save training progress to file"""
        try:
            progress_file = Path("training_progress.json")
            with open(progress_file, 'w') as f:
                json.dump({
                    'metrics_history': [
                        {k: v.isoformat() if isinstance(v, datetime) else v
                         for k, v in m.items()}
                        for m in self.metrics_history
                    ],
                    'alerts': [
                        {k: v.isoformat() if isinstance(v, datetime) else v
                         for k, v in alert.items()}
                        for alert in self.alerts
                    ]
                }, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save progress: {e}")

    def get_summary(self) -> Dict[str, Any]:
        """Get training summary"""
        if not self.metrics_history:
            return {}

        return {
            'total_episodes': len(self.metrics_history),
            'training_time': (datetime.now() - self.start_time).total_seconds() if self.start_time else 0,
            'alerts_count': len(self.alerts),
            'latest_metrics': self.metrics_history[-1] if self.metrics_history else {},
            'performance_trend': self._calculate_performance_trend()
        }

    def _calculate_performance_trend(self) -> str:
        """Calculate overall performance trend"""
        if len(self.metrics_history) < 10:
            return "insufficient_data"

        rewards = [m.get('reward', 0) for m in self.metrics_history[-20:]]
        if len(rewards) < 10:
            return "insufficient_data"

        trend = np.polyfit(range(len(rewards)), rewards, 1)[0]

        if trend > 0.05:
            return "improving"
        elif trend < -0.05:
            return "degrading"
        else:
            return "stable"
