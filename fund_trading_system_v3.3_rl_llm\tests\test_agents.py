"""
智能体模块测试
测试传统智能体和增强智能体的功能
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.traditional.technical_agent import TechnicalAgent
from agents.traditional.fund_flow_agent import FundFlowAgent
from agents.traditional.gua_analysis_agent import GuaAnalysisAgent
from agents.enhanced.enhanced_technical_agent import EnhancedTechnicalAgent
from agents.enhanced.soros_agent import SorosReflexivityAgent


class TestTechnicalAgent(unittest.TestCase):
    """测试技术分析智能体"""
    
    def setUp(self):
        self.agent = TechnicalAgent()
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertEqual(self.agent.name, "TechnicalAgent")
        self.assertEqual(self.agent.agent_type, "technical_analysis")
        self.assertIsNotNone(self.agent.logger)
    
    def test_process_with_valid_data(self):
        """测试使用有效数据进行处理"""
        test_data = {'fund_code': '513500'}
        result = self.agent.process(test_data)

        # 验证返回结果包含必要字段（可能是正常结果或错误）
        self.assertIn('fund_code', result)
        # 如果有错误，验证错误字段；如果正常，验证分析字段
        if 'error' in result:
            self.assertIsInstance(result['error'], str)
        else:
            # 正常分析结果应该包含这些字段
            self.assertIn('buy_signal', result)
            self.assertIn('confidence_score', result)
    
    def test_process_without_fund_code(self):
        """测试没有基金代码的处理"""
        test_data = {}
        result = self.agent.process(test_data)
        
        self.assertIn('error', result)
        self.assertEqual(result['error'], 'No fund_code provided')


class TestFundFlowAgent(unittest.TestCase):
    """测试资金流向分析智能体"""
    
    def setUp(self):
        self.agent = FundFlowAgent()
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertEqual(self.agent.name, "FundFlowAgent")
        self.assertEqual(self.agent.agent_type, "fund_flow_analysis")
    
    def test_process_with_valid_data(self):
        """测试使用有效数据进行处理"""
        test_data = {'fund_code': '513500'}
        result = self.agent.process(test_data)

        # 验证返回结果包含必要字段（可能是正常结果或错误）
        self.assertIn('fund_code', result)
        # 如果有错误，验证错误字段；如果正常，验证分析字段
        if 'error' in result:
            self.assertIsInstance(result['error'], str)
        else:
            # 正常分析结果应该包含这些字段
            self.assertIn('flow_strength', result)
            self.assertIn('volume_analysis', result)


class TestGuaAnalysisAgent(unittest.TestCase):
    """测试卦象分析智能体"""
    
    def setUp(self):
        self.agent = GuaAnalysisAgent()
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertEqual(self.agent.name, "GuaAnalysisAgent")
        self.assertEqual(self.agent.agent_type, "gua_analysis")
    
    def test_gua_64_list(self):
        """测试64卦列表"""
        self.assertEqual(len(self.agent.gua_64), 64)
        self.assertIn("乾", self.agent.gua_64)
        self.assertIn("坤", self.agent.gua_64)


class TestEnhancedTechnicalAgent(unittest.TestCase):
    """测试增强版技术分析智能体"""
    
    def setUp(self):
        self.agent = EnhancedTechnicalAgent()
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertEqual(self.agent.name, "EnhancedTechnicalAgent")
        self.assertEqual(self.agent.agent_type, "enhanced_technical_analysis")


class TestSorosReflexivityAgent(unittest.TestCase):
    """测试索罗斯反身性理论智能体"""
    
    def setUp(self):
        self.agent = SorosReflexivityAgent()
    
    def test_agent_initialization(self):
        """测试智能体初始化"""
        self.assertEqual(self.agent.name, "SorosReflexivityAgent")
        self.assertEqual(self.agent.agent_type, "soros_reflexivity_analysis")
    
    def test_process_with_valid_data(self):
        """测试使用有效数据进行处理"""
        test_data = {'fund_code': '513500'}
        result = self.agent.process(test_data)
        
        # 应该返回基本的反身性分析结果
        self.assertIn('fund_code', result)
        self.assertIn('action', result)
        self.assertIn('confidence', result)


if __name__ == '__main__':
    unittest.main()
