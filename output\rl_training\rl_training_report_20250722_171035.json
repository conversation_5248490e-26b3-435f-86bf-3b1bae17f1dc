{"evaluation_timestamp": "2025-07-22T17:10:35.242260", "training_configuration": {"algorithm": "DQN", "architecture": {"state_dim": 50, "action_dim": 3, "hidden_dims": [256, 256, 128]}, "episodes": 10, "learning_rate": 0.001, "epsilon_start": 1.0, "epsilon_end": 0.01, "epsilon_decay": 0.995, "batch_size": 32, "save_frequency": 50}, "evaluation_results": {"avg_total_return": 0.0, "std_total_return": 0.0, "avg_volatility": 0.0, "std_volatility": 0.0, "avg_sharpe_ratio": 0.0, "std_sharpe_ratio": 0.0, "avg_max_drawdown": 0.0, "std_max_drawdown": 0.0, "avg_total_trades": 0.0, "std_total_trades": 0.0, "avg_portfolio_value": 100000.0, "std_portfolio_value": 0.0, "avg_total_rewards": 0.0, "std_total_rewards": 0.0, "avg_total_reward": 0.0, "std_total_reward": 0.0, "avg_steps": 1.0, "std_steps": 0.0}, "summary": {"model_performance": "Needs Improvement", "recommendation": "Requires further optimization"}}