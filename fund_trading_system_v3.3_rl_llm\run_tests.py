"""
测试运行脚本
运行所有测试并生成报告
"""

import unittest
import sys
import os
from io import StringIO

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行 Fund Trading System V3 测试套件")
    print("=" * 60)
    
    # 发现并运行所有测试
    loader = unittest.TestLoader()
    start_dir = os.path.join(current_dir, 'tests')
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 创建测试运行器
    stream = StringIO()
    runner = unittest.TextTestRunner(stream=stream, verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    print(stream.getvalue())
    
    # 生成测试报告
    print("\n📊 测试结果汇总")
    print("-" * 40)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n⚠️ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # 计算成功率
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n✅ 测试成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 所有测试通过！")
    elif success_rate >= 80:
        print("👍 大部分测试通过，需要修复少量问题")
    else:
        print("⚠️ 需要修复较多问题")
    
    return result.wasSuccessful()


def run_specific_test(test_module):
    """运行特定测试模块"""
    print(f"🧪 运行测试模块: {test_module}")
    print("=" * 40)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromName(f'tests.{test_module}')
    
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    if len(sys.argv) > 1:
        # 运行特定测试模块
        test_module = sys.argv[1]
        success = run_specific_test(test_module)
    else:
        # 运行所有测试
        success = run_all_tests()
    
    sys.exit(0 if success else 1)
