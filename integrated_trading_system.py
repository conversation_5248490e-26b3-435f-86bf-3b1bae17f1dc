#!/usr/bin/env python3
"""
整合的强化学习股票交易系统
结合技术指标、现代化RL算法和完整的训练流程
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
from gymnasium import spaces

from enhanced_data_processor import DataProcessor, TechnicalIndicators
from modern_rl_trading import StockTradingEnv, SAC, ReplayBuffer, set_seed

class EnhancedStockTradingEnv(StockTradingEnv):
    """增强的股票交易环境，支持技术指标"""
    
    def __init__(self, df: pd.DataFrame, 
                 initial_balance: float = 100000,
                 use_technical_indicators: bool = True,
                 transaction_cost: float = 0.001):
        
        self.use_technical_indicators = use_technical_indicators
        self.transaction_cost = transaction_cost
        
        # 处理数据
        if use_technical_indicators:
            processor = DataProcessor()
            df = processor.add_technical_indicators(df)
            # 填充NaN值
            df = df.fillna(method='ffill').fillna(method='bfill')
        
        # 先调用父类初始化
        super().__init__(df, initial_balance)
        
        # 重新定义观察空间以包含技术指标
        if use_technical_indicators:
            # 选择关键技术指标
            self.indicator_columns = [
                'sma_5', 'sma_10', 'sma_20', 'ema_12', 'ema_26',
                'rsi', 'macd', 'macd_signal', 'bb_position', 'bb_width',
                'stoch_k', 'stoch_d', 'volume_ratio', 'volatility'
            ]
            # 基础市场数据 + 技术指标 + 账户状态
            obs_dim = 6 + len(self.indicator_columns) + 4
            
            self.observation_space = spaces.Box(
                low=0, high=1, shape=(obs_dim,), dtype=np.float32
            )
        else:
            self.indicator_columns = []
    
    def _get_observation(self):
        """获取增强的观察（包含技术指标）"""
        if self.current_step >= len(self.df):
            self.current_step = len(self.df) - 1
            
        row = self.df.iloc[self.current_step]
        
        # 基础市场数据
        obs = [
            row['open'] / 5000,
            row['high'] / 5000,
            row['low'] / 5000,
            row['close'] / 5000,
            row['volume'] / 1e9,
            row['amount'] / 1e10,
        ]
        
        # 技术指标
        if self.use_technical_indicators:
            for col in self.indicator_columns:
                if col in row and not pd.isna(row[col]):
                    if col == 'rsi':
                        obs.append(row[col] / 100)  # RSI归一化到0-1
                    elif col in ['bb_position', 'bb_width']:
                        obs.append(np.clip(row[col], 0, 1))
                    elif col in ['stoch_k', 'stoch_d']:
                        obs.append(row[col] / 100)
                    elif col == 'volume_ratio':
                        obs.append(np.clip(row[col] / 5, 0, 1))  # 成交量比率
                    elif col == 'volatility':
                        obs.append(np.clip(row[col] * 100, 0, 1))  # 波动率
                    else:
                        # 价格相关指标，归一化
                        obs.append(row[col] / 5000)
                else:
                    obs.append(0.5)  # 默认值
        
        # 账户状态
        obs.extend([
            self.balance / 2147480,
            self.net_worth / 2147480,
            self.shares_held / 2147480,
            self.cost_basis / 5000,
        ])
        
        return np.array(obs, dtype=np.float32)
    
    def _take_action(self, action):
        """执行交易动作（包含交易成本）"""
        current_price = np.random.uniform(
            self.df.iloc[self.current_step]['low'],
            self.df.iloc[self.current_step]['high']
        )
        
        buy_signal = action[0]
        sell_signal = action[1]
        
        # 买入逻辑
        if buy_signal > 0.1 and self.balance > current_price:
            buy_amount = buy_signal * self.balance
            shares_to_buy = int(buy_amount / current_price)
            
            if shares_to_buy > 0:
                cost = shares_to_buy * current_price
                transaction_fee = cost * self.transaction_cost
                total_cost = cost + transaction_fee
                
                if total_cost <= self.balance:
                    prev_cost = self.cost_basis * self.shares_held
                    self.balance -= total_cost
                    self.cost_basis = (prev_cost + cost) / (self.shares_held + shares_to_buy)
                    self.shares_held += shares_to_buy
        
        # 卖出逻辑
        elif sell_signal > 0.1 and self.shares_held > 0:
            shares_to_sell = int(sell_signal * self.shares_held)
            
            if shares_to_sell > 0:
                revenue = shares_to_sell * current_price
                transaction_fee = revenue * self.transaction_cost
                net_revenue = revenue - transaction_fee
                
                self.balance += net_revenue
                self.shares_held -= shares_to_sell
                self.total_shares_sold += shares_to_sell
                self.total_sales_value += net_revenue
                
                if self.shares_held == 0:
                    self.cost_basis = 0
        
        # 更新净值
        self.net_worth = self.balance + self.shares_held * current_price
        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth

class TradingSystem:
    """完整的交易系统"""
    
    def __init__(self, config: Dict = None):
        self.config = config or self._default_config()
        self.processor = DataProcessor()
        self.results = {}
        
        # 创建保存目录
        os.makedirs('./models', exist_ok=True)
        os.makedirs('./results', exist_ok=True)
    
    def _default_config(self) -> Dict:
        """默认配置"""
        return {
            'data': {
                'symbol': 'DEMO',
                'length': 2000,
                'train_ratio': 0.7,
                'val_ratio': 0.15
            },
            'env': {
                'initial_balance': 100000,
                'use_technical_indicators': True,
                'transaction_cost': 0.001
            },
            'training': {
                'episodes': 1000,
                'batch_size': 64,
                'memory_size': 100000,
                'eval_episodes': 10,
                'save_freq': 100
            },
            'model': {
                'actor_lr': 3e-4,
                'critic_lr': 3e-4,
                'gamma': 0.99,
                'tau': 0.005,
                'alpha': 0.2
            }
        }
    
    def prepare_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """准备数据"""
        print("准备数据...")
        
        # 生成数据
        df = self.processor.generate_realistic_data(
            symbol=self.config['data']['symbol'],
            length=self.config['data']['length']
        )
        
        # 分割数据
        train_df, val_df, test_df = self.processor.split_data(
            df, 
            self.config['data']['train_ratio'],
            self.config['data']['val_ratio']
        )
        
        print(f"数据准备完成: 训练集{len(train_df)}, 验证集{len(val_df)}, 测试集{len(test_df)}")
        return train_df, val_df, test_df
    
    def create_environments(self, train_df: pd.DataFrame, 
                          val_df: pd.DataFrame, 
                          test_df: pd.DataFrame) -> Tuple:
        """创建环境"""
        print("创建交易环境...")
        
        train_env = EnhancedStockTradingEnv(
            train_df, 
            **self.config['env']
        )
        
        val_env = EnhancedStockTradingEnv(
            val_df.reset_index(drop=True),
            **self.config['env']
        )
        
        test_env = EnhancedStockTradingEnv(
            test_df.reset_index(drop=True),
            **self.config['env']
        )
        
        print(f"环境创建完成，观察空间维度: {train_env.observation_space.shape[0]}")
        return train_env, val_env, test_env
    
    def create_agent(self, env) -> SAC:
        """创建智能体"""
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"创建SAC智能体，使用设备: {device}")
        
        state_dim = env.observation_space.shape[0]
        action_dim = env.action_space.shape[0]
        
        agent = SAC(state_dim, action_dim, device)
        return agent
    
    def train(self, agent: SAC, train_env, val_env) -> Dict:
        """训练智能体"""
        print("开始训练...")
        
        replay_buffer = ReplayBuffer(self.config['training']['memory_size'])
        train_scores = []
        val_scores = []
        best_val_score = -np.inf
        
        for episode in range(self.config['training']['episodes']):
            # 训练一轮
            state, _ = train_env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                action = agent.select_action(state)
                next_state, reward, done, truncated, info = train_env.step(action)
                done = done or truncated
                
                replay_buffer.push(state, action, reward, next_state, done)
                
                if len(replay_buffer) > self.config['training']['batch_size']:
                    agent.update(replay_buffer, self.config['training']['batch_size'])
                
                state = next_state
                episode_reward += reward
            
            train_scores.append(episode_reward)
            
            # 定期验证
            if episode % 50 == 0:
                val_score = self._evaluate(agent, val_env, episodes=5)
                val_scores.append(val_score)
                
                avg_train = np.mean(train_scores[-100:])
                print(f"Episode {episode}: Train={avg_train:.4f}, Val={val_score:.4f}, "
                      f"Net Worth=${info['net_worth']:.2f}")
                
                # 保存最佳模型
                if val_score > best_val_score:
                    best_val_score = val_score
                    agent.save('./models/best_model.pth')
                    print(f"保存最佳模型，验证得分: {val_score:.4f}")
            
            # 定期保存
            if episode % self.config['training']['save_freq'] == 0:
                agent.save(f'./models/model_episode_{episode}.pth')
        
        return {
            'train_scores': train_scores,
            'val_scores': val_scores,
            'best_val_score': best_val_score
        }
    
    def _evaluate(self, agent: SAC, env, episodes: int = 10) -> float:
        """评估智能体"""
        total_rewards = []
        
        for _ in range(episodes):
            state, _ = env.reset()
            episode_reward = 0
            done = False
            
            while not done:
                action = agent.select_action(state, evaluate=True)
                state, reward, done, truncated, info = env.step(action)
                done = done or truncated
                episode_reward += reward
            
            total_rewards.append(episode_reward)
        
        return np.mean(total_rewards)
    
    def test(self, agent: SAC, test_env) -> Dict:
        """测试智能体"""
        print("开始测试...")
        
        test_results = []
        
        for episode in range(self.config['training']['eval_episodes']):
            state, _ = test_env.reset()
            episode_reward = 0
            done = False
            actions_taken = []
            net_worths = []
            
            while not done:
                action = agent.select_action(state, evaluate=True)
                state, reward, done, truncated, info = test_env.step(action)
                done = done or truncated
                
                episode_reward += reward
                actions_taken.append(action.copy())
                net_worths.append(info['net_worth'])
            
            test_results.append({
                'episode': episode,
                'reward': episode_reward,
                'final_net_worth': info['net_worth'],
                'profit_rate': info['profit_rate'],
                'actions': actions_taken,
                'net_worths': net_worths
            })
            
            print(f"测试轮次 {episode + 1}: 奖励={episode_reward:.4f}, "
                  f"净值=${info['net_worth']:.2f}, "
                  f"收益率={info['profit_rate']*100:.2f}%")
        
        avg_reward = np.mean([r['reward'] for r in test_results])
        avg_profit_rate = np.mean([r['profit_rate'] for r in test_results])
        
        print(f"\n测试完成:")
        print(f"平均奖励: {avg_reward:.4f}")
        print(f"平均收益率: {avg_profit_rate*100:.2f}%")
        
        return {
            'results': test_results,
            'avg_reward': avg_reward,
            'avg_profit_rate': avg_profit_rate
        }
    
    def save_results(self, train_results: Dict, test_results: Dict):
        """保存结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存配置
        with open(f'./results/config_{timestamp}.json', 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # 保存训练结果
        train_df = pd.DataFrame({
            'episode': range(len(train_results['train_scores'])),
            'train_score': train_results['train_scores']
        })
        train_df.to_csv(f'./results/train_scores_{timestamp}.csv', index=False)
        
        # 保存测试结果
        test_df = pd.DataFrame([
            {
                'episode': r['episode'],
                'reward': r['reward'],
                'final_net_worth': r['final_net_worth'],
                'profit_rate': r['profit_rate']
            }
            for r in test_results['results']
        ])
        test_df.to_csv(f'./results/test_results_{timestamp}.csv', index=False)
        
        print(f"结果已保存到 ./results/ 目录，时间戳: {timestamp}")
    
    def plot_results(self, train_results: Dict, test_results: Dict):
        """绘制结果"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 训练曲线
        axes[0, 0].plot(train_results['train_scores'])
        axes[0, 0].set_title('Training Scores')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Score')
        
        # 验证曲线
        if train_results['val_scores']:
            val_episodes = np.arange(0, len(train_results['train_scores']), 50)[:len(train_results['val_scores'])]
            axes[0, 1].plot(val_episodes, train_results['val_scores'])
            axes[0, 1].set_title('Validation Scores')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Score')
        
        # 测试收益率分布
        profit_rates = [r['profit_rate'] * 100 for r in test_results['results']]
        axes[1, 0].hist(profit_rates, bins=10, alpha=0.7)
        axes[1, 0].set_title('Test Profit Rate Distribution')
        axes[1, 0].set_xlabel('Profit Rate (%)')
        axes[1, 0].set_ylabel('Frequency')
        
        # 净值曲线（第一个测试轮次）
        if test_results['results']:
            net_worths = test_results['results'][0]['net_worths']
            axes[1, 1].plot(net_worths)
            axes[1, 1].set_title('Net Worth Over Time (Test Episode 1)')
            axes[1, 1].set_xlabel('Step')
            axes[1, 1].set_ylabel('Net Worth ($)')
        
        plt.tight_layout()
        plt.savefig('./results/training_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def run_complete_pipeline(self):
        """运行完整的训练和测试流程"""
        print("=== 启动完整交易系统 ===")
        
        # 设置随机种子
        set_seed(42)
        
        # 准备数据
        train_df, val_df, test_df = self.prepare_data()
        
        # 创建环境
        train_env, val_env, test_env = self.create_environments(train_df, val_df, test_df)
        
        # 创建智能体
        agent = self.create_agent(train_env)
        
        # 训练
        train_results = self.train(agent, train_env, val_env)
        
        # 加载最佳模型进行测试
        try:
            agent.load('./models/best_model.pth')
            print("加载最佳模型进行测试")
        except:
            print("使用当前模型进行测试")
        
        # 测试
        test_results = self.test(agent, test_env)
        
        # 保存结果
        self.save_results(train_results, test_results)
        
        # 绘制结果
        self.plot_results(train_results, test_results)
        
        print("=== 完整流程结束 ===")
        return train_results, test_results

if __name__ == "__main__":
    # 创建交易系统
    system = TradingSystem()
    
    # 运行完整流程
    train_results, test_results = system.run_complete_pipeline()