"""
高级评估指标计算器 - 实现更多性能指标和评估方法
包括风险调整收益、信息比率、最大回撤、卡尔马比率等高级指标
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import warnings

# 尝试导入可选依赖
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    import scipy.stats as stats
    from scipy.optimize import minimize
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False


class RiskMetric(Enum):
    """风险指标类型"""
    VOLATILITY = "volatility"
    VAR = "var"
    CVAR = "cvar"
    MAX_DRAWDOWN = "max_drawdown"
    DOWNSIDE_DEVIATION = "downside_deviation"
    BETA = "beta"
    TRACKING_ERROR = "tracking_error"


class PerformanceMetric(Enum):
    """性能指标类型"""
    TOTAL_RETURN = "total_return"
    ANNUALIZED_RETURN = "annualized_return"
    SHARPE_RATIO = "sharpe_ratio"
    SORTINO_RATIO = "sortino_ratio"
    CALMAR_RATIO = "calmar_ratio"
    INFORMATION_RATIO = "information_ratio"
    TREYNOR_RATIO = "treynor_ratio"
    ALPHA = "alpha"
    WIN_RATE = "win_rate"
    PROFIT_FACTOR = "profit_factor"


@dataclass
class EvaluationResult:
    """评估结果"""
    timestamp: str
    total_samples: int
    performance_metrics: Dict[str, float]
    risk_metrics: Dict[str, float]
    trade_statistics: Dict[str, Any]
    period_analysis: Dict[str, Dict[str, float]]
    benchmark_comparison: Optional[Dict[str, float]] = None
    confidence_intervals: Optional[Dict[str, Tuple[float, float]]] = None


class AdvancedEvaluator:
    """高级评估器"""
    
    def __init__(self, risk_free_rate: float = 0.02, benchmark_returns: Optional[List[float]] = None):
        self.risk_free_rate = risk_free_rate
        self.benchmark_returns = benchmark_returns
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def evaluate_performance(self, returns: List[float], prices: Optional[List[float]] = None,
                           trades: Optional[List[Dict[str, Any]]] = None,
                           timestamps: Optional[List[datetime]] = None) -> EvaluationResult:
        """综合性能评估"""
        try:
            returns_array = np.array(returns)
            
            # 计算性能指标
            performance_metrics = self._calculate_performance_metrics(returns_array, timestamps)
            
            # 计算风险指标
            risk_metrics = self._calculate_risk_metrics(returns_array, prices)
            
            # 计算交易统计
            trade_statistics = self._calculate_trade_statistics(trades, returns_array)
            
            # 周期分析
            period_analysis = self._calculate_period_analysis(returns_array, timestamps)
            
            # 基准比较
            benchmark_comparison = None
            if self.benchmark_returns:
                benchmark_comparison = self._calculate_benchmark_comparison(returns_array)
            
            # 置信区间
            confidence_intervals = self._calculate_confidence_intervals(returns_array)
            
            return EvaluationResult(
                timestamp=datetime.now().isoformat(),
                total_samples=len(returns),
                performance_metrics=performance_metrics,
                risk_metrics=risk_metrics,
                trade_statistics=trade_statistics,
                period_analysis=period_analysis,
                benchmark_comparison=benchmark_comparison,
                confidence_intervals=confidence_intervals
            )
            
        except Exception as e:
            self.logger.error(f"性能评估失败: {str(e)}")
            raise
    
    def _calculate_performance_metrics(self, returns: np.ndarray, 
                                     timestamps: Optional[List[datetime]] = None) -> Dict[str, float]:
        """计算性能指标"""
        metrics = {}
        
        if len(returns) == 0:
            return metrics
        
        # 基础收益指标
        total_return = np.prod(1 + returns) - 1
        metrics['total_return'] = total_return
        
        # 年化收益率
        if timestamps and len(timestamps) > 1:
            days = (timestamps[-1] - timestamps[0]).days
            if days > 0:
                annualized_return = (1 + total_return) ** (365.25 / days) - 1
                metrics['annualized_return'] = annualized_return
        else:
            # 假设日频数据
            annualized_return = (1 + np.mean(returns)) ** 252 - 1
            metrics['annualized_return'] = annualized_return
        
        # 夏普比率
        excess_returns = returns - self.risk_free_rate / 252
        if np.std(excess_returns) > 0:
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            metrics['sharpe_ratio'] = sharpe_ratio
        else:
            metrics['sharpe_ratio'] = 0.0
        
        # 索提诺比率
        downside_returns = excess_returns[excess_returns < 0]
        if len(downside_returns) > 0:
            downside_deviation = np.std(downside_returns)
            if downside_deviation > 0:
                sortino_ratio = np.mean(excess_returns) / downside_deviation * np.sqrt(252)
                metrics['sortino_ratio'] = sortino_ratio
            else:
                metrics['sortino_ratio'] = float('inf')
        else:
            metrics['sortino_ratio'] = float('inf')
        
        # 胜率
        win_rate = np.sum(returns > 0) / len(returns)
        metrics['win_rate'] = win_rate
        
        # 盈亏比
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        if len(positive_returns) > 0 and len(negative_returns) > 0:
            avg_win = np.mean(positive_returns)
            avg_loss = np.mean(np.abs(negative_returns))
            profit_factor = avg_win / avg_loss
            metrics['profit_factor'] = profit_factor
        else:
            metrics['profit_factor'] = 0.0
        
        return metrics
    
    def _calculate_risk_metrics(self, returns: np.ndarray, 
                              prices: Optional[List[float]] = None) -> Dict[str, float]:
        """计算风险指标"""
        metrics = {}
        
        if len(returns) == 0:
            return metrics
        
        # 波动率
        volatility = np.std(returns) * np.sqrt(252)
        metrics['volatility'] = volatility
        
        # VaR (95%)
        var_95 = np.percentile(returns, 5)
        metrics['var_95'] = var_95
        
        # CVaR (95%)
        cvar_95 = np.mean(returns[returns <= var_95])
        metrics['cvar_95'] = cvar_95
        
        # 最大回撤
        if prices:
            cumulative_returns = np.cumprod(1 + returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)
            metrics['max_drawdown'] = max_drawdown
            
            # 卡尔马比率
            if abs(max_drawdown) > 0:
                calmar_ratio = metrics.get('annualized_return', 0) / abs(max_drawdown)
                metrics['calmar_ratio'] = calmar_ratio
            else:
                metrics['calmar_ratio'] = float('inf')
        else:
            # 使用累积收益计算回撤
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = cumulative_returns - running_max
            max_drawdown = np.min(drawdowns)
            metrics['max_drawdown'] = max_drawdown
        
        # 下行偏差
        downside_returns = returns[returns < 0]
        if len(downside_returns) > 0:
            downside_deviation = np.std(downside_returns) * np.sqrt(252)
            metrics['downside_deviation'] = downside_deviation
        else:
            metrics['downside_deviation'] = 0.0
        
        # Beta（如果有基准）
        if self.benchmark_returns and len(self.benchmark_returns) == len(returns):
            benchmark_array = np.array(self.benchmark_returns)
            covariance = np.cov(returns, benchmark_array)[0, 1]
            benchmark_variance = np.var(benchmark_array)
            if benchmark_variance > 0:
                beta = covariance / benchmark_variance
                metrics['beta'] = beta
                
                # Alpha
                benchmark_return = np.mean(benchmark_array) * 252
                alpha = metrics.get('annualized_return', 0) - (self.risk_free_rate + beta * (benchmark_return - self.risk_free_rate))
                metrics['alpha'] = alpha
                
                # 跟踪误差
                tracking_error = np.std(returns - benchmark_array) * np.sqrt(252)
                metrics['tracking_error'] = tracking_error
                
                # 信息比率
                if tracking_error > 0:
                    information_ratio = alpha / tracking_error
                    metrics['information_ratio'] = information_ratio
                else:
                    metrics['information_ratio'] = 0.0
        
        return metrics
    
    def _calculate_trade_statistics(self, trades: Optional[List[Dict[str, Any]]], 
                                  returns: np.ndarray) -> Dict[str, Any]:
        """计算交易统计"""
        stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'average_trade_return': 0.0,
            'average_winning_trade': 0.0,
            'average_losing_trade': 0.0,
            'largest_win': 0.0,
            'largest_loss': 0.0,
            'consecutive_wins': 0,
            'consecutive_losses': 0,
            'max_consecutive_wins': 0,
            'max_consecutive_losses': 0
        }
        
        if not trades:
            return stats
        
        trade_returns = []
        for trade in trades:
            if 'return' in trade:
                trade_returns.append(trade['return'])
            elif 'pnl' in trade and 'entry_price' in trade:
                trade_return = trade['pnl'] / trade['entry_price']
                trade_returns.append(trade_return)
        
        if not trade_returns:
            return stats
        
        trade_returns = np.array(trade_returns)
        
        stats['total_trades'] = len(trade_returns)
        stats['winning_trades'] = np.sum(trade_returns > 0)
        stats['losing_trades'] = np.sum(trade_returns < 0)
        stats['average_trade_return'] = np.mean(trade_returns)
        
        winning_trades = trade_returns[trade_returns > 0]
        losing_trades = trade_returns[trade_returns < 0]
        
        if len(winning_trades) > 0:
            stats['average_winning_trade'] = np.mean(winning_trades)
            stats['largest_win'] = np.max(winning_trades)
        
        if len(losing_trades) > 0:
            stats['average_losing_trade'] = np.mean(losing_trades)
            stats['largest_loss'] = np.min(losing_trades)
        
        # 连续盈亏统计
        current_wins = 0
        current_losses = 0
        max_wins = 0
        max_losses = 0
        
        for ret in trade_returns:
            if ret > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            elif ret < 0:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)
            else:
                current_wins = 0
                current_losses = 0
        
        stats['max_consecutive_wins'] = max_wins
        stats['max_consecutive_losses'] = max_losses

        return stats

    def _calculate_period_analysis(self, returns: np.ndarray,
                                 timestamps: Optional[List[datetime]] = None) -> Dict[str, Dict[str, float]]:
        """计算周期分析"""
        analysis = {}

        if not timestamps or len(timestamps) != len(returns):
            return analysis

        try:
            if PANDAS_AVAILABLE:
                # 使用pandas进行周期分析
                df = pd.DataFrame({
                    'returns': returns,
                    'timestamp': timestamps
                })
                df.set_index('timestamp', inplace=True)

                # 月度分析
                monthly_returns = df.resample('M')['returns'].apply(lambda x: np.prod(1 + x) - 1)
                if len(monthly_returns) > 0:
                    analysis['monthly'] = {
                        'mean': monthly_returns.mean(),
                        'std': monthly_returns.std(),
                        'min': monthly_returns.min(),
                        'max': monthly_returns.max(),
                        'positive_months': (monthly_returns > 0).sum() / len(monthly_returns)
                    }

                # 季度分析
                quarterly_returns = df.resample('Q')['returns'].apply(lambda x: np.prod(1 + x) - 1)
                if len(quarterly_returns) > 0:
                    analysis['quarterly'] = {
                        'mean': quarterly_returns.mean(),
                        'std': quarterly_returns.std(),
                        'min': quarterly_returns.min(),
                        'max': quarterly_returns.max(),
                        'positive_quarters': (quarterly_returns > 0).sum() / len(quarterly_returns)
                    }

                # 年度分析
                yearly_returns = df.resample('Y')['returns'].apply(lambda x: np.prod(1 + x) - 1)
                if len(yearly_returns) > 0:
                    analysis['yearly'] = {
                        'mean': yearly_returns.mean(),
                        'std': yearly_returns.std(),
                        'min': yearly_returns.min(),
                        'max': yearly_returns.max(),
                        'positive_years': (yearly_returns > 0).sum() / len(yearly_returns)
                    }

        except Exception as e:
            self.logger.warning(f"周期分析失败: {str(e)}")

        return analysis

    def _calculate_benchmark_comparison(self, returns: np.ndarray) -> Dict[str, float]:
        """计算基准比较"""
        comparison = {}

        if not self.benchmark_returns or len(self.benchmark_returns) != len(returns):
            return comparison

        benchmark_array = np.array(self.benchmark_returns)

        # 累积收益比较
        portfolio_cumulative = np.prod(1 + returns) - 1
        benchmark_cumulative = np.prod(1 + benchmark_array) - 1

        comparison['portfolio_total_return'] = portfolio_cumulative
        comparison['benchmark_total_return'] = benchmark_cumulative
        comparison['excess_return'] = portfolio_cumulative - benchmark_cumulative

        # 年化收益比较
        portfolio_annual = (1 + np.mean(returns)) ** 252 - 1
        benchmark_annual = (1 + np.mean(benchmark_array)) ** 252 - 1

        comparison['portfolio_annual_return'] = portfolio_annual
        comparison['benchmark_annual_return'] = benchmark_annual
        comparison['annual_excess_return'] = portfolio_annual - benchmark_annual

        # 波动率比较
        portfolio_vol = np.std(returns) * np.sqrt(252)
        benchmark_vol = np.std(benchmark_array) * np.sqrt(252)

        comparison['portfolio_volatility'] = portfolio_vol
        comparison['benchmark_volatility'] = benchmark_vol

        # 夏普比率比较
        portfolio_sharpe = (portfolio_annual - self.risk_free_rate) / portfolio_vol if portfolio_vol > 0 else 0
        benchmark_sharpe = (benchmark_annual - self.risk_free_rate) / benchmark_vol if benchmark_vol > 0 else 0

        comparison['portfolio_sharpe'] = portfolio_sharpe
        comparison['benchmark_sharpe'] = benchmark_sharpe

        # 相关性
        correlation = np.corrcoef(returns, benchmark_array)[0, 1]
        comparison['correlation'] = correlation if not np.isnan(correlation) else 0.0

        return comparison

    def _calculate_confidence_intervals(self, returns: np.ndarray,
                                      confidence_level: float = 0.95) -> Dict[str, Tuple[float, float]]:
        """计算置信区间"""
        intervals = {}

        if len(returns) < 2:
            return intervals

        try:
            # 收益率置信区间
            mean_return = np.mean(returns)
            std_return = np.std(returns, ddof=1)
            n = len(returns)

            if SCIPY_AVAILABLE:
                t_value = stats.t.ppf((1 + confidence_level) / 2, n - 1)
            else:
                t_value = 1.96  # 近似值

            margin_error = t_value * std_return / np.sqrt(n)
            intervals['mean_return'] = (mean_return - margin_error, mean_return + margin_error)

            # 夏普比率置信区间（简化计算）
            excess_returns = returns - self.risk_free_rate / 252
            sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0

            # 使用bootstrap方法估计夏普比率的标准误差
            bootstrap_sharpes = []
            for _ in range(1000):
                bootstrap_sample = np.random.choice(excess_returns, size=len(excess_returns), replace=True)
                if np.std(bootstrap_sample) > 0:
                    bootstrap_sharpe = np.mean(bootstrap_sample) / np.std(bootstrap_sample) * np.sqrt(252)
                    bootstrap_sharpes.append(bootstrap_sharpe)

            if bootstrap_sharpes:
                sharpe_lower = np.percentile(bootstrap_sharpes, (1 - confidence_level) / 2 * 100)
                sharpe_upper = np.percentile(bootstrap_sharpes, (1 + confidence_level) / 2 * 100)
                intervals['sharpe_ratio'] = (sharpe_lower, sharpe_upper)

        except Exception as e:
            self.logger.warning(f"置信区间计算失败: {str(e)}")

        return intervals

    def calculate_rolling_metrics(self, returns: List[float], window: int = 252) -> Dict[str, List[float]]:
        """计算滚动指标"""
        rolling_metrics = {
            'rolling_return': [],
            'rolling_volatility': [],
            'rolling_sharpe': [],
            'rolling_max_drawdown': []
        }

        returns_array = np.array(returns)

        for i in range(window, len(returns_array) + 1):
            window_returns = returns_array[i-window:i]

            # 滚动收益
            rolling_return = np.prod(1 + window_returns) - 1
            rolling_metrics['rolling_return'].append(rolling_return)

            # 滚动波动率
            rolling_vol = np.std(window_returns) * np.sqrt(252)
            rolling_metrics['rolling_volatility'].append(rolling_vol)

            # 滚动夏普比率
            excess_returns = window_returns - self.risk_free_rate / 252
            if np.std(excess_returns) > 0:
                rolling_sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            else:
                rolling_sharpe = 0.0
            rolling_metrics['rolling_sharpe'].append(rolling_sharpe)

            # 滚动最大回撤
            cumulative = np.cumprod(1 + window_returns)
            running_max = np.maximum.accumulate(cumulative)
            drawdowns = (cumulative - running_max) / running_max
            max_dd = np.min(drawdowns)
            rolling_metrics['rolling_max_drawdown'].append(max_dd)

        return rolling_metrics

    def generate_performance_summary(self, evaluation_result: EvaluationResult) -> str:
        """生成性能摘要报告"""
        summary = []
        summary.append("=== 性能评估摘要 ===")
        summary.append(f"评估时间: {evaluation_result.timestamp}")
        summary.append(f"样本数量: {evaluation_result.total_samples}")
        summary.append("")

        # 性能指标
        summary.append("--- 性能指标 ---")
        perf = evaluation_result.performance_metrics
        summary.append(f"总收益率: {perf.get('total_return', 0):.2%}")
        summary.append(f"年化收益率: {perf.get('annualized_return', 0):.2%}")
        summary.append(f"夏普比率: {perf.get('sharpe_ratio', 0):.3f}")
        summary.append(f"索提诺比率: {perf.get('sortino_ratio', 0):.3f}")
        summary.append(f"胜率: {perf.get('win_rate', 0):.2%}")
        summary.append("")

        # 风险指标
        summary.append("--- 风险指标 ---")
        risk = evaluation_result.risk_metrics
        summary.append(f"年化波动率: {risk.get('volatility', 0):.2%}")
        summary.append(f"最大回撤: {risk.get('max_drawdown', 0):.2%}")
        summary.append(f"VaR (95%): {risk.get('var_95', 0):.2%}")
        summary.append(f"CVaR (95%): {risk.get('cvar_95', 0):.2%}")
        if 'calmar_ratio' in risk:
            summary.append(f"卡尔马比率: {risk.get('calmar_ratio', 0):.3f}")
        summary.append("")

        # 交易统计
        summary.append("--- 交易统计 ---")
        trades = evaluation_result.trade_statistics
        summary.append(f"总交易次数: {trades.get('total_trades', 0)}")
        summary.append(f"盈利交易: {trades.get('winning_trades', 0)}")
        summary.append(f"亏损交易: {trades.get('losing_trades', 0)}")
        summary.append(f"平均交易收益: {trades.get('average_trade_return', 0):.2%}")
        summary.append("")

        # 基准比较
        if evaluation_result.benchmark_comparison:
            summary.append("--- 基准比较 ---")
            bench = evaluation_result.benchmark_comparison
            summary.append(f"组合收益: {bench.get('portfolio_total_return', 0):.2%}")
            summary.append(f"基准收益: {bench.get('benchmark_total_return', 0):.2%}")
            summary.append(f"超额收益: {bench.get('excess_return', 0):.2%}")
            summary.append(f"相关性: {bench.get('correlation', 0):.3f}")

        return "\n".join(summary)
