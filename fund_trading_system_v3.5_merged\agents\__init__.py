"""
Unified Agent Module for Trading System v3.5

This module provides a standardized interface for all trading agents:
- RLAgent: Reinforcement Learning based quantitative analysis
- LLMAgent: Large Language Model based semantic analysis  
- CZSCAgent: CZSC (Chan Theory) based technical analysis
"""

from .base_agent import BaseAgent, TradingSignal, MarketData
from .rl.rl_agent import RLAgent
from .llm.llm_agent import LLMAgent
from .czsc.czsc_agent import CZSCAgent

__all__ = [
    'BaseAgent',
    'TradingSignal', 
    'MarketData',
    'RLAgent',
    'LLMAgent',
    'CZSCAgent'
]

__version__ = "3.5.0" 