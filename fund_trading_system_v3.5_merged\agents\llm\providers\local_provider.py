"""
Local LLM Provider

Implements LLM provider interface for local/offline models.
Supports Hugging Face transformers and other local model formats.
"""

import json
import time
from typing import Dict, Any, Optional
import logging
import re

from .base_provider import LL<PERSON>roviderBase, LLMResponse, LLMConfig

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    pipeline = None
    AutoTokenizer = None
    AutoModelForCausalLM = None
    torch = None


class LocalLLMProvider(LLMProviderBase):
    """Local LLM provider implementation using Hugging Face transformers"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.model = None
        self.tokenizer = None
        self.sentiment_pipeline = None
        
        if not TRANSFORMERS_AVAILABLE:
            self.logger.warning("Transformers library not available. Install with: pip install transformers torch")
    
    def initialize(self) -> bool:
        """Initialize local model"""
        if not TRANS<PERSON>ORMERS_AVAILABLE:
            self.logger.error("Transformers library not installed")
            return False
        
        try:
            # Initialize sentiment analysis pipeline
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model="cardiffnlp/twitter-roberta-base-sentiment-latest",
                device=0 if torch.cuda.is_available() else -1
            )
            
            # For text generation, use a smaller model suitable for local inference
            model_name = self.config.model_name or "microsoft/DialoGPT-medium"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.is_initialized = True
            self.logger.info(f"Local LLM provider initialized with model {model_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize local LLM provider: {e}")
            return False
    
    def analyze_market_sentiment(self, text: str, context: Dict[str, Any] = None) -> LLMResponse:
        """Analyze market sentiment using local models"""
        if not self.is_initialized:
            return self._create_error_response("Provider not initialized")
        
        self._increment_call_count()
        
        try:
            # Use sentiment analysis pipeline
            sentiment_result = self.sentiment_pipeline(text[:512])  # Limit text length
            
            # Map sentiment labels to our format
            label_mapping = {
                'LABEL_0': 'negative',  # Negative
                'LABEL_1': 'neutral',   # Neutral  
                'LABEL_2': 'positive',  # Positive
                'NEGATIVE': 'negative',
                'NEUTRAL': 'neutral',
                'POSITIVE': 'positive'
            }
            
            sentiment_label = sentiment_result[0]['label']
            sentiment_score = sentiment_result[0]['score']
            
            mapped_sentiment = label_mapping.get(sentiment_label, 'neutral')
            
            # Create sentiment scores
            sentiment_scores = {'positive': 0.0, 'negative': 0.0, 'neutral': 0.0}
            sentiment_scores[mapped_sentiment] = sentiment_score
            
            # Fill in other scores with lower values
            remaining_score = (1.0 - sentiment_score) / 2
            for key in sentiment_scores:
                if sentiment_scores[key] == 0.0:
                    sentiment_scores[key] = remaining_score
            
            # Generate reasoning using simple heuristics
            reasoning = self._generate_sentiment_reasoning(text, mapped_sentiment, sentiment_score)
            
            return LLMResponse(
                content=f"Local sentiment analysis: {mapped_sentiment} ({sentiment_score:.3f})",
                confidence=sentiment_score,
                sentiment_scores=sentiment_scores,
                reasoning=reasoning,
                metadata={
                    'model': self.config.model_name,
                    'original_label': sentiment_label,
                    'local_model': True
                },
                provider=self.config.provider_name,
                model=self.config.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"Local sentiment analysis failed: {e}")
            return self._create_error_response(str(e))
    
    def generate_market_insight(self, market_data: Dict[str, Any]) -> LLMResponse:
        """Generate market insights using local models"""
        if not self.is_initialized:
            return self._create_error_response("Provider not initialized")
        
        self._increment_call_count()
        
        try:
            # Extract key market data
            symbol = market_data.get('symbol', 'Unknown')
            price_change = market_data.get('price_change', 0)
            volume = market_data.get('volume', 0)
            technical_indicators = market_data.get('technical_indicators', {})
            
            # Generate insight using rule-based approach
            insight = self._generate_rule_based_insight(symbol, price_change, volume, technical_indicators)
            
            # Determine sentiment based on price change and indicators
            if price_change > 0.02:
                sentiment = "bullish"
                positive_score = min(0.8, 0.5 + price_change * 10)
                negative_score = 0.1
                neutral_score = 1.0 - positive_score - negative_score
                confidence = 0.7
            elif price_change < -0.02:
                sentiment = "bearish"
                negative_score = min(0.8, 0.5 + abs(price_change) * 10)
                positive_score = 0.1
                neutral_score = 1.0 - positive_score - negative_score
                confidence = 0.7
            else:
                sentiment = "neutral"
                neutral_score = 0.6
                positive_score = 0.2
                negative_score = 0.2
                confidence = 0.5
            
            sentiment_scores = {
                'positive': positive_score,
                'negative': negative_score,
                'neutral': neutral_score
            }
            
            return LLMResponse(
                content=insight,
                confidence=confidence,
                sentiment_scores=sentiment_scores,
                reasoning=f"Local analysis based on price change {price_change:.2%} and technical indicators",
                metadata={
                    'model': self.config.model_name,
                    'sentiment': sentiment,
                    'local_model': True,
                    'rule_based': True
                },
                provider=self.config.provider_name,
                model=self.config.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"Local market insight generation failed: {e}")
            return self._create_error_response(str(e))
    
    def is_available(self) -> bool:
        """Check if local provider is available"""
        return TRANSFORMERS_AVAILABLE and self.is_initialized
    
    def _generate_sentiment_reasoning(self, text: str, sentiment: str, score: float) -> str:
        """Generate reasoning for sentiment analysis"""
        # Simple keyword-based reasoning
        positive_keywords = ['up', 'rise', 'gain', 'bull', 'growth', 'profit', 'strong', 'positive']
        negative_keywords = ['down', 'fall', 'loss', 'bear', 'decline', 'risk', 'weak', 'negative']
        
        text_lower = text.lower()
        pos_matches = [word for word in positive_keywords if word in text_lower]
        neg_matches = [word for word in negative_keywords if word in text_lower]
        
        reasoning = f"Sentiment classified as {sentiment} with confidence {score:.3f}. "
        
        if pos_matches:
            reasoning += f"Positive indicators: {', '.join(pos_matches)}. "
        if neg_matches:
            reasoning += f"Negative indicators: {', '.join(neg_matches)}. "
        
        if not pos_matches and not neg_matches:
            reasoning += "No strong sentiment indicators found. "
        
        return reasoning
    
    def _generate_rule_based_insight(self, symbol: str, price_change: float, volume: int, 
                                   technical_indicators: Dict[str, float]) -> str:
        """Generate market insight using rule-based approach"""
        insights = []
        
        # Price change analysis
        if price_change > 0.05:
            insights.append(f"{symbol} shows strong bullish momentum with {price_change:.2%} gain")
        elif price_change > 0.02:
            insights.append(f"{symbol} displays moderate upward movement at {price_change:.2%}")
        elif price_change < -0.05:
            insights.append(f"{symbol} exhibits significant bearish pressure with {price_change:.2%} decline")
        elif price_change < -0.02:
            insights.append(f"{symbol} shows moderate downward pressure at {price_change:.2%}")
        else:
            insights.append(f"{symbol} trading in neutral range with {price_change:.2%} change")
        
        # Volume analysis
        if volume > 1000000:
            insights.append("High trading volume indicates strong market interest")
        elif volume < 100000:
            insights.append("Low trading volume suggests limited market participation")
        
        # Technical indicators analysis
        if technical_indicators:
            rsi = technical_indicators.get('rsi', 50)
            if rsi > 70:
                insights.append("RSI indicates overbought conditions")
            elif rsi < 30:
                insights.append("RSI suggests oversold conditions")
            
            macd = technical_indicators.get('macd', 0)
            if macd > 0:
                insights.append("MACD shows bullish momentum")
            elif macd < 0:
                insights.append("MACD indicates bearish momentum")
        
        # Combine insights
        if insights:
            return ". ".join(insights) + "."
        else:
            return f"Local analysis of {symbol}: Limited data available for comprehensive insight."


def create_local_provider(model_name: str = "microsoft/DialoGPT-medium") -> LocalLLMProvider:
    """Factory function to create local LLM provider
    
    Args:
        model_name: Local model name to use
        
    Returns:
        LocalLLMProvider: Configured provider instance
    """
    config = LLMConfig(
        provider_name="local",
        model_name=model_name,
        max_tokens=500,
        temperature=0.7,
        timeout=60.0,
        retry_attempts=2
    )
    
    provider = LocalLLMProvider(config)
    provider.initialize()
    return provider
