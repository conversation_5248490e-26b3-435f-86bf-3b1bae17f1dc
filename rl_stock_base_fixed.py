import argparse
import os
import gym
import random
from gym import spaces
import numpy as np
import pandas as pd
import paddle
import paddle.nn as nn
import paddle.nn.functional as F
from paddle import inference
import parl
from parl.algorithms import SAC
from parl.utils import logger, tensorboard, ReplayMemory

# 简化的数据获取函数，避免QUANTAXIS依赖
def get_kline_simple(symbol='000001', freq='15min', length=2000):
    """
    生成模拟股票数据，替代czsc_func.get_kline
    """
    np.random.seed(42)
    
    # 生成基础价格序列
    base_price = 10.0
    returns = np.random.normal(0.001, 0.02, length)
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1.0))
        
    prices = np.array(prices[1:])
    
    # 生成完整的OHLCV数据
    df = pd.DataFrame({
        'open': prices * (1 + np.random.normal(0, 0.005, length)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, length))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, length))),
        'close': prices,
        'vol': np.random.lognormal(10, 1, length),
        'amount': prices * np.random.lognormal(10, 1, length),
    })
    
    # 确保OHLC逻辑正确
    df['high'] = np.maximum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['low'] = np.minimum.reduce([df['open'], df['high'], df['low'], df['close']])
    
    # 添加时间戳
    dates = pd.date_range(start='2020-01-01', periods=length, freq='15min')
    df['dt'] = dates.strftime('%Y-%m-%d %H:%M:%S')
    
    return df

# 默认的一些数据，用于归一化属性值
MAX_ACCOUNT_BALANCE = 2147480        # 最大的账户财产
MAX_NUM_SHARES = 2147480             # 最大的手数
MAX_SHARE_PRICE = 5000              # 最大的单手价格
MAX_VOLUME = 1e9                 # 最大的成交量
MAX_AMOUNT = 1e10                    # 最大的成交额
MAX_OPEN_POSITIONS = 5              # 最大的持仓头寸
MAX_STEPS = 1000                     # 最大的交互次数
MAX_DAY_CHANGE = 1                  # 最大的日期改变
max_loss = -50000                    # 最大的损失
max_predict_rate = 3                # 最大的预测率
INITIAL_ACCOUNT_BALANCE = 100000    # 初始的金钱


class StockTradingEnv(gym.Env):  # 先修改成最简单的形式
    """A stock trading environment for OpenAI gym"""
    metadata = {'render.modes': ['human']}

    def __init__(self, df):
        super(StockTradingEnv, self).__init__()

        self.df = df
        # self.reward_range = (0, MAX_ACCOUNT_BALANCE)

        # 动作的可能情况：买入x%, 卖出x%, 观望
        self.action_space = spaces.Box(
            low=np.array([-1, -1]), high=np.array([1, 1]), dtype=np.float32)

        # 环境状态的维度
        # self.observation_space = spaces.Box(
        #     low=0, high=1, shape=(20,), dtype=np.float32)
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(13,), dtype=np.float32)

        self.current_step = 0

    def seed(self, seed):
        random.seed(seed)
        np.random.seed(seed)

    # 处理状态
    def _next_observation(self):
        # 有些股票数据缺失一些数据，处理一下
        # d10 = self.df.loc[self.current_step, 'peTTM'] / 100
        # d11 = self.df.loc[self.current_step, 'pbMRQ'] / 100
        # d12 = self.df.loc[self.current_step, 'psTTM'] / 100
        # if np.isnan(d10):  # 某些数据是0.00000000e+00，如果是nan会报错
        #     d10 = d11 = d12 = 0.00000000e+00
        obs = np.array([
            self.df.loc[self.current_step, 'open'] / MAX_SHARE_PRICE,
            self.df.loc[self.current_step, 'high'] / MAX_SHARE_PRICE,
            self.df.loc[self.current_step, 'low'] / MAX_SHARE_PRICE,
            self.df.loc[self.current_step, 'close'] / MAX_SHARE_PRICE,
            self.df.loc[self.current_step, 'vol'] / MAX_VOLUME,
            self.df.loc[self.current_step, 'amount'] / MAX_AMOUNT,
            # self.df.loc[self.current_step, 'adjustflag'],
            # self.df.loc[self.current_step, 'tradestatus'] / 1,
            # self.df.loc[self.current_step, 'pctChg'] / 100,
            # d10,
            # d11,
            # d12,
            # self.df.loc[self.current_step, 'pcfNcfTTM'] / 100,
            self.balance / MAX_ACCOUNT_BALANCE,
            self.max_net_worth / MAX_ACCOUNT_BALANCE,
            self.net_worth / MAX_ACCOUNT_BALANCE,
            self.shares_held / MAX_NUM_SHARES,
            self.cost_basis / MAX_SHARE_PRICE,
            self.total_shares_sold / MAX_NUM_SHARES,
            self.total_sales_value / (MAX_NUM_SHARES * MAX_SHARE_PRICE),
        ])
        return obs

    # 执行当前动作，并计算出当前的数据（如：资产等）
    def _take_action(self, action):
        # 随机设置当前的价格，其范围上界为当前时间点的价格
        current_price = random.uniform(
            self.df.loc[self.current_step, "low"], self.df.loc[self.current_step, "high"])
        action_type = action[0]
        amount = action[1]
        if action_type < 1 / 3 and self.balance >= current_price:  # 买入amount%
            total_possible = int(self.balance / current_price)
            shares_bought = int(total_possible * amount)
            if shares_bought != 0.:
                prev_cost = self.cost_basis * self.shares_held
                additional_cost = shares_bought * current_price

                self.balance -= additional_cost
                self.cost_basis = (
                                          prev_cost + additional_cost) / (self.shares_held + shares_bought)
                self.shares_held += shares_bought

        elif action_type > 2 / 3 and self.shares_held != 0:  # 卖出amount%
            shares_sold = int(self.shares_held * amount)
            self.balance += shares_sold * current_price
            self.shares_held -= shares_sold
            self.total_shares_sold += shares_sold
            self.total_sales_value += shares_sold * current_price

        else:
            pass

        # 计算出执行动作后的资产净值
        self.net_worth = self.balance + self.shares_held * current_price

        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth

        if self.shares_held == 0:
            self.cost_basis = 0

    # 与环境交互
    def step(self, action):
        # 在环境内执行动作
        self._take_action(action)
        done = False
        status = None

        reward = 0

        # 判断是否终止
        self.current_step += 1

        # delay_modifier = (self.current_step / MAX_STEPS)

        # reward += delay_modifier

        if self.net_worth >= INITIAL_ACCOUNT_BALANCE * max_predict_rate:
            reward += max_predict_rate
            status = f'[ENV] success at step {self.current_step}! Get {max_predict_rate} times worth.'
            # self.current_step = 0
            done = True
        if self.current_step > len(self.df.loc[:, 'open'].values) - 1:
            status = f'[ENV] Loop training. Max worth was {self.max_net_worth}, final worth is {self.net_worth}.'
            # reward += (self.net_worth / INITIAL_ACCOUNT_BALANCE - max_predict_rate) / max_predict_rate
            reward += self.net_worth / INITIAL_ACCOUNT_BALANCE
            self.current_step = 0  # loop training
            done = True

        if self.net_worth <= 0:
            status = f'[ENV] Failure at step {self.current_step}. Loss all worth. Max worth was {self.max_net_worth}'
            reward += -1
            # self.current_step = 0
            done = True

        else:
            # 计算相对收益比，并据此来计算奖励
            profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
            # profit = self.net_worth - self.balance
            profit_percent = profit / INITIAL_ACCOUNT_BALANCE
            if profit_percent > 0:
                reward += profit_percent / max_predict_rate
            elif profit_percent == 0:
                reward += -0.1
            else:
                reward += -0.1

        obs = self._next_observation()

        return obs, reward, done, {
            'profit': self.net_worth,
            'current_step': self.current_step,
            'status': status
        }

    # 重置环境
    def reset(self, new_df=None):
        # 重置环境的变量为初始值
        self.balance = INITIAL_ACCOUNT_BALANCE
        self.net_worth = INITIAL_ACCOUNT_BALANCE
        self.max_net_worth = INITIAL_ACCOUNT_BALANCE
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0

        # 传入环境数据集
        if new_df is not None:
            self.df = new_df
        # if self.current_step > len(self.df.loc[:, 'open'].values) - 1:
        self.current_step = 0

        return self._next_observation()

    def get_obs(self, current_step):
        # d10 = self.df.loc[current_step, 'peTTM'] / 100
        # d11 = self.df.loc[current_step, 'pbMRQ'] / 100
        # d12 = self.df.loc[current_step, 'psTTM'] / 100
        # if np.isnan(d10):  # 某些数据是0.00000000e+00，如果是nan会报错
        #     d10 = d11 = d12 = 0.00000000e+00
        obs = np.array([
            self.df.loc[current_step, 'open'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'high'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'low'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'close'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'vol'] / MAX_VOLUME,
            self.df.loc[current_step, 'amount'] / MAX_AMOUNT,
            # self.df.loc[current_step, 'adjustflag'],
            # self.df.loc[current_step, 'tradestatus'] / 1,
            # self.df.loc[current_step, 'pctChg'] / 100,
            # d10,
            # d11,
            # d12,
            # self.df.loc[current_step, 'pcfNcfTTM'] / 100,
            self.balance / MAX_ACCOUNT_BALANCE,
            self.max_net_worth / MAX_ACCOUNT_BALANCE,
            self.net_worth / MAX_ACCOUNT_BALANCE,
            self.shares_held / MAX_NUM_SHARES,
            self.cost_basis / MAX_SHARE_PRICE,
            self.total_shares_sold / MAX_NUM_SHARES,
            self.total_sales_value / (MAX_NUM_SHARES * MAX_SHARE_PRICE),
        ])
        return obs

    # 显示环境至屏幕
    def render(self, mode='human'):
        # 打印环境信息
        profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
        print('-' * 30)
        print(f'Step: {self.current_step}')
        print(f'Balance: {self.balance}')
        print(f'Shares held: {self.shares_held} (Total sold: {self.total_shares_sold})')
        print(f'Avg cost for held shares: {self.cost_basis} (Total sales value: {self.total_sales_value})')
        print(f'Net worth: {self.net_worth} (Max net worth: {self.max_net_worth})')
        print(f'Profit: {profit}')
        return profit


class StockAgent(parl.Agent):
    def __init__(self, algorithm):
        super(StockAgent, self).__init__(algorithm)

        self.alg.sync_target(decay=0)

    def predict(self, obs):
        obs = paddle.to_tensor(obs.reshape(1, -1), dtype='float32')
        action = self.alg.predict(obs)
        action_numpy = action.cpu().numpy()[0]
        return action_numpy

    def sample(self, obs):
        obs = paddle.to_tensor(obs.reshape(1, -1), dtype='float32')
        action, _ = self.alg.sample(obs)
        action_numpy = action.cpu().numpy()[0]
        return action_numpy

    def learn(self, obs, action, reward, next_obs, terminal):
        terminal = np.expand_dims(terminal, -1)
        reward = np.expand_dims(reward, -1)

        obs = paddle.to_tensor(obs, dtype='float32')
        action = paddle.to_tensor(action, dtype='float32')
        reward = paddle.to_tensor(reward, dtype='float32')
        next_obs = paddle.to_tensor(next_obs, dtype='float32')
        terminal = paddle.to_tensor(terminal, dtype='float32')
        critic_loss, actor_loss = self.alg.learn(obs, action, reward, next_obs,
                                                 terminal)
        return critic_loss, actor_loss


LOG_SIG_MAX = 1.0
LOG_SIG_MIN = -1e9


class StockModel(parl.Model):
    def __init__(self, obs_dim, action_dim):
        super(StockModel, self).__init__()
        self.actor_model = Actor(obs_dim, action_dim)
        self.critic_model = Critic(obs_dim, action_dim)

    def policy(self, obs):
        return self.actor_model(obs)

    def value(self, obs, action):
        return self.critic_model(obs, action)

    def get_actor_params(self):
        return self.actor_model.parameters()

    def get_critic_params(self):
        return self.critic_model.parameters()


class Actor(parl.Model):
    def __init__(self, obs_dim, action_dim):
        super(Actor, self).__init__()

        self.l1 = nn.Linear(obs_dim, 256)
        self.l2 = nn.Linear(256, 256)
        self.mean_linear = nn.Linear(256, action_dim)
        self.std_linear = nn.Linear(256, action_dim)

    def forward(self, obs):
        x = F.relu(self.l1(obs))
        x = F.relu(self.l2(x))

        act_mean = self.mean_linear(x)
        act_std = self.std_linear(x)
        act_log_std = paddle.clip(act_std, min=LOG_SIG_MIN, max=LOG_SIG_MAX)
        return act_mean, act_log_std


class Critic(parl.Model):
    def __init__(self, obs_dim, action_dim):
        super(Critic, self).__init__()

        # Q1 network
        self.l1 = nn.Linear(obs_dim + action_dim, 256)
        self.l2 = nn.Linear(256, 256)
        self.l3 = nn.Linear(256, 1)

        # Q2 network
        self.l4 = nn.Linear(obs_dim + action_dim, 256)
        self.l5 = nn.Linear(256, 256)
        self.l6 = nn.Linear(256, 1)

    def forward(self, obs, action):
        x = paddle.concat([obs, action], 1)

        # Q1
        q1 = F.relu(self.l1(x))
        q1 = F.relu(self.l2(q1))
        q1 = self.l3(q1)

        # Q2
        q2 = F.relu(self.l4(x))
        q2 = F.relu(self.l5(q2))
        q2 = self.l6(q2)
        return q1, q2

# Runs policy for 5 episodes by default and returns average reward
# A fixed seed is used for the eval environment
eval_seed = [0, 53, 47, 99, 107, 1, 17, 57, 97, 179, 777]
@paddle.no_grad()
def run_evaluate_episodes(agent, env, eval_episodes):
    avg_reward = 0.
    for epi in range(eval_episodes):
        obs = env.reset()
        env.seed(eval_seed[epi])
        done = False
        while not done:
            action = agent.predict(obs)
            obs, reward, done, _ = env.step(action)
            avg_reward += reward
    avg_reward /= eval_episodes
    print(f'Evaluator: the average reward is {avg_reward:.3f} over {eval_episodes} episodes.')
    return avg_reward


# Run episode for training
def run_train_episode(agent, env, rpm, episode_num):
    action_dim = env.action_space.shape[0]
    obs = env.reset()
    env.seed(SEED)
    done = False
    episode_reward = 0
    episode_steps = 0
    while not done:
        episode_steps += 1
        # Select action randomly or according to policy
        if rpm.size() < WARMUP_STEPS:
            action = np.random.uniform(-1, 1, size=action_dim)
        else:
            action = agent.sample(obs)
        # action = agent.sample(obs)
        action = (action+1.0)/2.0
        next_obs, reward, done, info = env.step(action)
        terminal = float(done)

        # Store data in replay memory
        rpm.append(obs, action, reward, next_obs, terminal)

        obs = next_obs
        episode_reward += reward

        # Train agent after collecting sufficient data
        if rpm.size() >= WARMUP_STEPS:
            batch_obs, batch_action, batch_reward, batch_next_obs, batch_terminal = rpm.sample_batch(
                BATCH_SIZE)
            agent.learn(batch_obs, batch_action, batch_reward, batch_next_obs,
                        batch_terminal)
    # print(f'Learner: Episode {episode_steps+1} done. The reward is {episode_reward:.3f}.')
    # 打印信息
    current_step = info['current_step']
    print(f'Learner: Episode {episode_num} done. The reward is {episode_reward:.3f}.')
    print(info['status'])
    return episode_reward, episode_steps


def do_train(agent, env, eval_env, rpm):
    save_freq = 1
    total_steps = 0
    train_total_steps = 2.8e4
    episode_num = 0
    best_award = -1e9
    while total_steps < train_total_steps:
        episode_num +=1
        # Train episode
        episode_reward, episode_steps = run_train_episode(agent, env, rpm, episode_num)
        total_steps += episode_steps
        if(episode_num%save_freq==0):
            avg_reward = run_evaluate_episodes(agent, eval_env, EVAL_EPISODES)
            if(best_award < avg_reward): # 原假设，做空胜率明显更高
                best_award = avg_reward
                print(f'Saving best model!')
                agent.save(f"./models/{file_name}.ckpt")


def run_test_episodes(agent, env, eval_episodes, max_action_step = 200):
    avg_reward = 0.
    avg_worth = 0.
    for _ in range(eval_episodes):
        obs = env.reset()
        env.seed(0)
        done = False
        t = 0
        while not done:
            action = agent.predict(obs)
            obs, reward, done, info = env.step(action)
            avg_reward += reward
            t+=1
            if(t==max_action_step):
                # eval_env.render()
                print('over')
                break
        avg_worth += info['profit']
    avg_reward /= eval_episodes
    avg_worth /= eval_episodes
    print(f'Evaluator: The average reward is {avg_reward:.3f} over {eval_episodes} episodes.')
    print(f'Evaluator: The average worth is {avg_worth:.3f} over {eval_episodes} episodes.')

    return avg_reward


class Predictor(object):
    def __init__(self,
                 model_dir,
                 device="gpu",
                 batch_size=32,
                 use_tensorrt=False,
                 precision="fp32",
                 cpu_threads=10,
                 enable_mkldnn=False):
        self.batch_size = batch_size

        model_file = model_dir + "/inference_model.pdmodel"
        params_file = model_dir + "/inference_model.pdiparams"
        if not os.path.exists(model_file):
            raise ValueError("not find model file path {}".format(model_file))
        if not os.path.exists(params_file):
            raise ValueError("not find params file path {}".format(params_file))
        config = paddle.inference.Config(model_file, params_file)
        if device == "gpu":
            # set GPU configs accordingly
            # such as intialize the gpu memory, enable tensorrt
            config.enable_use_gpu(100, 0)
            precision_map = {
                "fp16": inference.PrecisionType.Half,
                "fp32": inference.PrecisionType.Float32,
                "int8": inference.PrecisionType.Int8
            }
            precision_mode = precision_map[precision]

            if use_tensorrt:
                config.enable_tensorrt_engine(max_batch_size=batch_size,
                                              min_subgraph_size=30,
                                              precision_mode=precision_mode)
        # elif device == "cpu":
        #     # set CPU configs accordingly,
        #     # such as enable_mkldnn, set_cpu_math_library_num_threads
        #     config.disable_gpu()
        #     if args.enable_mkldnn:
        #         # cache 10 different shapes for mkldnn to avoid memory leak
        #         config.set_mkldnn_cache_capacity(10)
        #         config.enable_mkldnn()
        #     config.set_cpu_math_library_num_threads(args.cpu_threads)
        elif device == "xpu":
            # set XPU configs accordingly
            config.enable_xpu(100)

        config.switch_use_feed_fetch_ops(False)
        self.predictor = paddle.inference.create_predictor(config)
        self.input_handles = [
            self.predictor.get_input_handle(name)
            for name in self.predictor.get_input_names()
        ]
        # self.output_handle = self.predictor.get_output_handle(
        #     self.predictor.get_output_names()[0])
        self.output_handle = [self.predictor.get_output_handle(name)
                              for name in self.predictor.get_output_names()]
        # 重置环境的变量为初始值
        self.balance = INITIAL_ACCOUNT_BALANCE
        self.net_worth = INITIAL_ACCOUNT_BALANCE
        self.max_net_worth = INITIAL_ACCOUNT_BALANCE
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0

    def predict(self, df):
        """
        Predicts the data labels.

        Args:
            data (obj:`List(str)`): The batch data whose each element is a raw text.
            tokenizer(obj:`PretrainedTokenizer`): This tokenizer inherits from :class:`~paddlenlp.transformers.PretrainedTokenizer`
                which contains most of the methods. Users should refer to the superclass for more information regarding methods.

        Returns:
            results(obj:`dict`): All the predictions probs.
        """
        obs = self.get_obs(df, 0)
        print(obs)
        self.input_handles[0].copy_from_cpu(obs.reshape(1, -1).astype('float32'))
        self.predictor.run()
        action = self.output_handle[0].copy_to_cpu()
        std = self.output_handle[1].copy_to_cpu()

        return [action, std]

    def get_obs(self, df, current_step):
        self.df = df
        # d10 = self.df.loc[current_step, 'peTTM'] / 100
        # d11 = self.df.loc[current_step, 'pbMRQ'] / 100
        # d12 = self.df.loc[current_step, 'psTTM'] / 100
        # if np.isnan(d10):  # 某些数据是0.00000000e+00，如果是nan会报错
        #     d10 = d11 = d12 = 0.00000000e+00
        obs = np.array([
            self.df.loc[current_step, 'open'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'high'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'low'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'close'] / MAX_SHARE_PRICE,
            self.df.loc[current_step, 'vol'] / MAX_VOLUME,
            self.df.loc[current_step, 'amount'] / MAX_AMOUNT,
            # self.df.loc[current_step, 'adjustflag'],
            # self.df.loc[current_step, 'tradestatus'] / 1,
            # self.df.loc[current_step, 'pctChg'] / 100,
            # d10,
            # d11,
            # d12,
            # self.df.loc[current_step, 'pcfNcfTTM'] / 100,
            self.balance / MAX_ACCOUNT_BALANCE,
            self.max_net_worth / MAX_ACCOUNT_BALANCE,
            self.net_worth / MAX_ACCOUNT_BALANCE,
            self.shares_held / MAX_NUM_SHARES,
            self.cost_basis / MAX_SHARE_PRICE,
            self.total_shares_sold / MAX_NUM_SHARES,
            self.total_sales_value / (MAX_NUM_SHARES * MAX_SHARE_PRICE),
        ])
        return obs


if __name__ == '__main__':
    SEED = 0  # 随机种子
    WARMUP_STEPS = 640
    EVAL_EPISODES = 1  # 评估的轮数
    MEMORY_SIZE = int(1e5)  # 经验池的大小
    BATCH_SIZE = 64  # 批次的大小
    GAMMA = 0.995  # 折扣因子
    TAU = 0.005  # 当前网络参数比例，用于更新目标网络
    ACTOR_LR = 1e-4  # actor网络的参数
    CRITIC_LR = 1e-4  # critic网络的参数
    alpha = 0.2  # 熵正则化系数, SAC的参数
    MAX_REWARD = -1e9  # 最大奖励
    file_name = f'sac_Stock'  # 模型保存的名字

    # 获得数据 - 使用简化的数据生成函数
    print("生成模拟股票数据...")
    df = get_kline_simple('518880', freq='15min', length=2000)
    df['volume'] = df['vol']
    df['amount'] = df['close'] * df['volume']
    df['date'] = pd.to_datetime(df['dt'], format='%Y-%m-%d %H:%M:%S')
    print(f"数据生成完成，共 {len(df)} 条记录")
    
    # 根据数据集设置环境
    env = StockTradingEnv(df[:1300])
    # 得到环境的参数信息（如：状态和动作的维度）
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]

    max_action = float(env.action_space.high[1])
    max_step = len(df.loc[:, 'open'].values)
    print(f'state: {state_dim}, action: {action_dim}, action max value: {max_action}, max step:{max_step}')

    # 获得数据
    # eval_df = pd.read_csv('test_v1.csv')
    eval_df = df[1300:]
    eval_df = eval_df.reset_index(drop=True)
    # 根据数据集设置环境
    eval_env = StockTradingEnv(eval_df)

    # 创建模型目录
    os.makedirs('./models', exist_ok=True)

    model = StockModel(state_dim, action_dim)
    algorithm = SAC(
        model,
        gamma=GAMMA,
        tau=TAU,
        alpha=alpha,
        actor_lr=ACTOR_LR,
        critic_lr=CRITIC_LR,
    )
    agent = StockAgent(algorithm)
    rpm = ReplayMemory(
        max_size=MEMORY_SIZE, obs_dim=state_dim, act_dim=action_dim)

    print("开始训练...")
    do_train(agent, env, eval_env, rpm)

    # agent.restore('./models/sac_Stock_base.ckpt')
    print("训练完成，开始测试...")
    try:
        agent.restore(f'./models/{file_name}.ckpt')
        print("模型加载成功")
    except:
        print("模型加载失败，使用当前模型进行测试")
    
    # 设置的最大执行的天数，每一个step表示一天
    max_action_step = 400
    # avg_reward = run_test_episodes(agent, env, EVAL_EPISODES, max_action_step)
    avg_reward = run_test_episodes(agent, eval_env, EVAL_EPISODES, max_action_step)
    print(f'avg_reward:{avg_reward}')