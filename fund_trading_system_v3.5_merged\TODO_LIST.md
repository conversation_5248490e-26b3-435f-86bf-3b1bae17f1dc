# TODO List: Merging v3.3 and v3.4 into System v3.5

This document outlines the technical tasks required to merge `fund_trading_system_v3.3_rl_llm` and `fund_trading_system_v3.4_backtest_czsc` into a single, unified, and production-ready system.

---

### Phase 0: Project Setup & Scaffolding

- [ ] Create a new project directory: `fund_trading_system_v3.5_merged`.
- [ ] Copy the entire `fund_trading_system_v3.4_backtest_czsc` directory into `v3.5` as the starting baseline.
- [ ] Initialize a new Git repository for version control.

### Phase 1: Core Architecture Unification

- [ ] **Refactor `AgentCollaborationManager`**:
    - [ ] Modify the input interface to accept a third signal from the `CZSCAgent` (or `CZSCCoordinator`).
    - [ ] Update the internal decision fusion logic to incorporate the CZSC signal. This is the most critical task. The new logic should be able to weigh RL, LLM, and CZSC signals based on configuration.
    - [ ] Add unit tests for the new three-signal fusion logic.

- [ ] **Standardize Agent & Coordinator Structure**:
    - [ ] Verify that `RLCoordinator`, `LLMCoordinator`, and `CZSCCoordinator` all follow a consistent interface.
    - [ ] Ensure all three agents (`RLA<PERSON>`, `LLMAgent`, `CZSCAgent`) are correctly initialized and managed by the main system.

- [ ] **Centralize Configuration (`config.py`)**:
    - [ ] Merge all configuration parameters from both systems into a single, unified configuration file.
    - [ ] Add a new section for `CZSCAgent` parameters.
    - [ ] Ensure backtest-specific parameters (date ranges, fees, etc.) and trade-specific parameters (broker endpoints, keys) are clearly separated.

### Phase 2: Unify Execution Modes (Backtest vs. Trade)

- [ ] **Implement CLI Entrypoint (`main.py`)**:
    - [ ] Use Python's `argparse` library to create a robust command-line interface.
    - [ ] Implement `--mode` flag to switch between `backtest` and `trade`.
    - [ ] `python main.py --mode backtest --config my_strategy.py`: Runs the `BacktestEngine`.
    - [ ] `python main.py --mode trade --config my_strategy.py`: Runs the `EnhancedFundTradingSystem` for live/paper trading.

- [ ] **Refactor Engine Initialization**:
    - [ ] The `main()` function should be clean, primarily responsible for parsing args and instantiating the correct engine (`BacktestEngine` or `TradingSystem`).
    - [ ] Ensure both engines consume the same configuration object and initialize the unified agent structure correctly.

### Phase 3: Data and Model Management

- [ ] **Define Unified Directory Structure**:
    - [ ] Create a top-level `/data` directory for all historical and incoming market data.
    - [ ] Create a top-level `/models` directory to store trained RL model checkpoints (`.ckpt`), LLM model paths, and any CZSC-related artifacts.
- [ ] **Update Data Handlers**:
    - [ ] Ensure the `DataManager` can provide data seamlessly to both the backtesting engine and the live trading engine.

### Phase 4: Testing and Validation

- [ ] **Create Integration Tests**:
    - [ ] Write a test that runs a simple backtest end-to-end and checks the output report.
    - [ ] Write a test that runs the trading system for a few steps in a simulated "trade" mode.
- [ ] **"Golden" Backtest**:
    - [ ] Run a standardized backtest with the `v3.4` system and save the results.
    - [ ] After the merge, run the exact same backtest with the `v3.5` system and ensure the results are identical, proving the integration did not break existing logic.

### Phase 5: Documentation & Cleanup

- [ ] **Update `README.md`**:
    - [ ] Write a new `README.md` for the `v3.5` system.
    - [ ] Clearly explain the unified architecture.
    - [ ] Provide clear instructions on how to run both `backtest` and `trade` modes.
    - [ ] Document the configuration file structure.
- [ ] **Code Cleanup**:
    - [ ] Remove any redundant files or code snippets from the `v3.3` logic that are now superseded by `v3.4` structures.
    - [ ] Add comments to the `AgentCollaborationManager`'s fusion logic to explain how the three signals are combined.
