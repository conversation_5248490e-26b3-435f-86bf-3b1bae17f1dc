absl-py @ file:///C:/b/abs_5babsu7y5x/croot/absl-py_1666362945682/work
addict==2.4.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal @ file:///tmp/build/80754af9/aiosignal_1637843061372/work
akshare==1.16.90
alabaster @ file:///home/<USER>/src/ci/alabaster_1611921544520/work
ale-py==0.11.2
alembic==1.13.2
aliyun-python-sdk-core==2.15.1
aliyun-python-sdk-kms==2.16.2
altair==4.2.2
anaconda-client @ file:///C:/ci/anaconda-client_1635342725944/work
anaconda-navigator==2.1.1
anaconda-project @ file:///tmp/build/80754af9/anaconda-project_1626085644852/work
annotated-types==0.6.0
anyio==4.3.0
appdirs==1.4.4
APScheduler==3.9.1.post1
argh==0.26.2
argon2-cffi @ file:///C:/ci/argon2-cffi_1613037869401/work
arrow @ file:///C:/ci/arrow_1617738834352/work
asn1crypto @ file:///tmp/build/80754af9/asn1crypto_1596577642040/work
astor==0.8.1
astroid @ file:///C:/ci/astroid_1628063282661/work
astropy @ file:///C:/ci/astropy_1629829318700/work
async-generator @ file:///home/<USER>/src/ci/async_generator_1611927993394/work
async-timeout==4.0.3
atomicwrites==1.4.0
attrs @ file:///tmp/build/80754af9/attrs_1620827162558/work
Automat==22.10.0
autopep8 @ file:///tmp/build/80754af9/autopep8_1620866417880/work
AutoROM==0.4.2
AutoROM.accept-rom-license==0.5.4
Babel @ file:///tmp/build/80754af9/babel_1620871417480/work
backcall @ file:///home/<USER>/src/ci/backcall_1611930011877/work
backports.functools-lru-cache @ file:///tmp/build/80754af9/backports.functools_lru_cache_1618170165463/work
backports.shutil-get-terminal-size @ file:///tmp/build/80754af9/backports.shutil_get_terminal_size_1608222128777/work
backports.tempfile @ file:///home/<USER>/recipes/ci/backports.tempfile_1610991236607/work
backports.weakref==1.0.post1
baidu-aip==4.16.8
bcrypt @ file:///C:/ci/bcrypt_1607022693089/work
beautifulsoup4==4.13.4
bidict==0.22.0
binaryornot @ file:///tmp/build/80754af9/binaryornot_1617751525010/work
bitarray @ file:///C:/ci/bitarray_1629133068652/work
bkcharts==0.2
black==19.10b0
bleach @ file:///tmp/build/80754af9/bleach_1628110601003/work
blinker==1.4
bokeh @ file:///C:/ci/bokeh_1635306491714/work
boto==2.49.0
Bottleneck @ file:///C:/ci/bottleneck_1607557040328/work
brotlipy==0.7.0
bs4==0.0.1
cached-property @ file:///tmp/build/80754af9/cached-property_1600785575025/work
cachetools @ file:///tmp/build/80754af9/cachetools_1619597386817/work
certifi==2021.10.8
cffi @ file:///C:/ci/cffi_1625831756778/work
chardet @ file:///C:/ci/chardet_1607706937985/work
charset-normalizer @ file:///tmp/build/80754af9/charset-normalizer_1630003229654/work
click==8.0.3
cloudpickle==1.3.0
clyent==1.2.2
cmake==3.25.2
colorama @ file:///tmp/build/80754af9/colorama_1607707115595/work
colorlog==6.7.0
comtypes==1.1.10
conda==22.9.0
conda-build==3.23.3
conda-content-trust @ file:///tmp/build/80754af9/conda-content-trust_1617045594566/work
conda-pack @ file:///tmp/build/80754af9/conda-pack_1611163042455/work
conda-package-handling @ file:///C:/b/abs_fcga8w0uem/croot/conda-package-handling_1672865024290/work
conda-repo-cli==1.0.5
conda-token @ file:///tmp/build/80754af9/conda-token_1620076980546/work
conda-verify==3.4.2
conda_package_streaming @ file:///C:/b/abs_0e5n5hdal3/croot/conda-package-streaming_1670508162902/work
constantly==15.1.0
contextlib2 @ file:///Users/<USER>/demo/mc3/conda-bld/contextlib2_1630668244042/work
cookiecutter @ file:///tmp/build/80754af9/cookiecutter_1617748928239/work
crcmod==1.7
cryptography @ file:///C:/ci/cryptography_1633520531101/work
cssselect==1.2.0
cycler==0.10.0
Cython @ file:///C:/ci/cython_1636018292912/work
cytoolz==0.11.0
czsc==0.9.57
daal4py==2021.3.0
dask==2021.10.0
dataclasses-json==0.6.4
datasets==2.18.0
deap==1.4.1
debugpy @ file:///C:/ci/debugpy_1629222819322/work
decorator @ file:///tmp/build/80754af9/decorator_1632776554403/work
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
delegator.py==0.1.1
demjson==2.2.4
Deprecated==1.2.14
diff-match-patch @ file:///Users/<USER>/demo/mc3/conda-bld/diff-match-patch_1630511840874/work
dill==0.3.8
distributed @ file:///C:/ci/distributed_1635968318313/work
distro==1.9.0
dnspython==2.2.1
docutils @ file:///C:/ci/docutils_1620828264669/work
easytrader==0.23.7
easyutils==0.1.7
einops==0.7.0
entrypoints==0.3
et-xmlfile==1.1.0
exceptiongroup==1.1.0
Farama-Notifications==0.0.4
fastcache @ file:///C:/ci/fastcache_1607571310570/work
filelock @ file:///tmp/build/80754af9/filelock_1635402558181/work
flake8 @ file:///tmp/build/80754af9/flake8_1620776156532/work
Flask @ file:///home/<USER>/src/ci/flask_1611932660458/work
Flask-Cors==3.0.10
Flask-SocketIO==5.3.2
fonttools==4.25.0
frozendict==2.4.6
frozenlist @ file:///C:/b/abs_2bb5uzghsi/croot/frozenlist_1670004511812/work
fsspec==2024.2.0
func-timeout==4.3.5
future @ file:///C:/ci/future_1607568713721/work
gast==0.5.4
gevent @ file:///C:/ci/gevent_1628273776273/work
gevent-websocket==0.10.1
gitdb==4.0.11
GitPython==3.1.43
glob2 @ file:///home/<USER>/recipes/ci/glob2_1610991677669/work
google-auth @ file:///opt/conda/conda-bld/google-auth_1646735974934/work
google-auth-oauthlib @ file:///tmp/build/80754af9/google-auth-oauthlib_1617120569401/work
greenlet @ file:///C:/ci/greenlet_1628888275363/work
groq==0.18.0
grpcio @ file:///C:/ci/grpcio_1637590978642/work
gym @ file:///D:/bld/gym_1673322362287/work
gym-anytrading==1.3.2
gym-notices @ file:///home/<USER>/feedstock_root/build_artifacts/gym-notices_1671541680412/work
gymnasium==1.1.1
h11==0.14.0
h5py @ file:///C:/ci/h5py_1622088609188/work
HeapDict @ file:///Users/<USER>/demo/mc3/conda-bld/heapdict_1630598515714/work
html5lib @ file:///Users/<USER>/demo/mc3/conda-bld/html5lib_1629144453894/work
httpcore==1.0.5
httpx==0.27.0
huggingface-hub==0.22.2
hyperlink==21.0.0
idna @ file:///tmp/build/80754af9/idna_1622654382723/work
imagecodecs @ file:///C:/ci/imagecodecs_1635511087451/work
imageio @ file:///tmp/build/80754af9/imageio_1617700267927/work
imagesize @ file:///Users/<USER>/demo/mc3/conda-bld/imagesize_1628863108022/work
importlib-metadata==6.0.0
importlib-resources==5.10.2
incremental==22.10.0
inflection==0.5.1
iniconfig @ file:///home/<USER>/recipes/ci/iniconfig_1610983019677/work
intervaltree @ file:///Users/<USER>/demo/mc3/conda-bld/intervaltree_1630511889664/work
ipykernel @ file:///C:/ci/ipykernel_1633545585502/work/dist/ipykernel-6.4.1-py3-none-any.whl
ipython @ file:///C:/ci/ipython_1635944283918/work
ipython-genutils @ file:///tmp/build/80754af9/ipython_genutils_1606773439826/work
ipywidgets @ file:///tmp/build/80754af9/ipywidgets_1634143127070/work
isort @ file:///tmp/build/80754af9/isort_1628603791788/work
itemadapter==0.7.0
itemloaders==1.0.6
itsdangerous @ file:///tmp/build/80754af9/itsdangerous_1621432558163/work
janus==0.4.0
jdcal @ file:///Users/<USER>/demo/mc3/conda-bld/jdcal_1630584345063/work
jedi @ file:///C:/ci/jedi_1611341083684/work
jieba==0.42.1
Jinja2 @ file:///tmp/build/80754af9/jinja2_1612213139570/work
jinja2-time @ file:///tmp/build/80754af9/jinja2-time_1617751524098/work
jmespath==0.10.0
joblib @ file:///tmp/build/80754af9/joblib_1635411271373/work
jqdatasdk==1.8.11
json5 @ file:///tmp/build/80754af9/json5_1624432770122/work
jsonpatch==1.33
jsonpath==0.82
jsonpointer==2.4
jsonschema @ file:///Users/<USER>/demo/mc3/conda-bld/jsonschema_1630511932244/work
jupyter @ file:///C:/ci/jupyter_1607685287094/work
jupyter-client @ file:///tmp/build/80754af9/jupyter_client_1616770841739/work
jupyter-console @ file:///tmp/build/80754af9/jupyter_console_1616615302928/work
jupyter-core @ file:///C:/ci/jupyter_core_1633420716440/work
jupyter-pip==0.3.1
jupyter-server @ file:///C:/ci/jupyter_server_1616084298419/work
jupyterlab @ file:///tmp/build/80754af9/jupyterlab_1635799997693/work
jupyterlab-pygments @ file:///tmp/build/80754af9/jupyterlab_pygments_1601490720602/work
jupyterlab-server @ file:///tmp/build/80754af9/jupyterlab_server_1633419203660/work
jupyterlab-widgets @ file:///tmp/build/80754af9/jupyterlab_widgets_1609884341231/work
keyboard==0.13.5
keyring @ file:///C:/ci/keyring_1629321702436/work
kiwisolver @ file:///C:/ci/kiwisolver_1612282555033/work
langchain==0.1.16
langchain-community==0.0.34
langchain-core==0.1.46
langchain-text-splitters==0.0.1
langsmith==0.1.51
lazy-object-proxy @ file:///C:/ci/lazy-object-proxy_1616529288960/work
libarchive-c @ file:///tmp/build/80754af9/python-libarchive-c_1617780486945/work
libtorrent==2.0.7
lightgbm==4.5.0
llvmlite==0.37.0
locket==0.2.1
loguru==0.7.2
lxml @ file:///C:/ci/lxml_1616443418777/work
Mako==1.3.5
Markdown @ file:///C:/b/abs_98lv_ucina/croot/markdown_1671541919225/work
markdown-it-py==3.0.0
MarkupSafe @ file:///C:/ci/markupsafe_1607027406824/work
marshmallow==3.21.1
matplotlib @ file:///C:/ci/matplotlib-suite_1634667159685/work
matplotlib-inline @ file:///tmp/build/80754af9/matplotlib-inline_1628242447089/work
mccabe==0.6.1
mdurl==0.1.2
menuinst @ file:///C:/ci/menuinst_1631733438520/work
mini-racer==0.12.4
mistune @ file:///C:/ci/mistune_1607359457024/work
mkl-fft==1.3.1
mkl-random @ file:///C:/ci/mkl_random_1626186184308/work
mkl-service==2.4.0
mock @ file:///tmp/build/80754af9/mock_1607622725907/work
modelscope==1.14.0
more-itertools @ file:///tmp/build/80754af9/more-itertools_1635423142362/work
motor==2.0.0
mpmath==1.2.1
msgpack @ file:///C:/ci/msgpack-python_1612287350784/work
multidict @ file:///C:/b/abs_6cx_8w3cv2/croot/multidict_1665674238352/work
multipledispatch @ file:///C:/ci/multipledispatch_1607574329826/work
multiprocess==0.70.16
multitasking==0.0.11
munkres==1.1.4
mypy-extensions==0.4.3
narwhals==1.3.0
navigator-updater==0.2.1
nbclassic @ file:///tmp/build/80754af9/nbclassic_1616085367084/work
nbclient @ file:///tmp/build/80754af9/nbclient_1614364831625/work
nbconvert @ file:///C:/ci/nbconvert_1624479160025/work
nbformat @ file:///tmp/build/80754af9/nbformat_1617383369282/work
nest-asyncio==1.6.0
networkx @ file:///tmp/build/80754af9/networkx_1633639043937/work
nltk==3.6.5
nose @ file:///tmp/build/80754af9/nose_1606773131901/work
notebook @ file:///C:/ci/notebook_1635393701545/work
numba @ file:///C:/ci/numba_1635186062888/work
numexpr @ file:///C:/ci/numexpr_1618856728739/work
numpy @ file:///C:/ci/numpy_and_numpy_base_1626271900803/work
numpydoc @ file:///tmp/build/80754af9/numpydoc_1605117425582/work
oauthlib @ file:///C:/b/abs_2eoymqc2ow/croot/oauthlib_1665490906043/work
olefile @ file:///Users/<USER>/demo/mc3/conda-bld/olefile_1629805411829/work
openai==1.16.2
opencv-python==********
opencv-python-headless==4.6.0.66
openpyxl @ file:///tmp/build/80754af9/openpyxl_1632777717936/work
opt-einsum==3.3.0
optuna==3.6.1
orjson==3.10.1
oss2==2.18.4
outcome==1.2.0
packaging==23.2
paddle-bfloat==0.1.7
paddlepaddle-gpu==2.4.1.post117
pandas @ file:///C:/ci/pandas_1635506685681/work
pandocfilters @ file:///C:/ci/pandocfilters_1605114832805/work
paramiko @ file:///tmp/build/80754af9/paramiko_1598886428689/work
parl==2.0.4
parse==1.20.2
parsel==1.7.0
parso @ file:///tmp/build/80754af9/parso_1617223946239/work
partd @ file:///tmp/build/80754af9/partd_1618000087440/work
path @ file:///C:/ci/path_1624287837534/work
pathlib @ file:///Users/<USER>/demo/mc3/conda-bld/pathlib_1629713961906/work
pathlib2 @ file:///C:/ci/pathlib2_1625585796814/work
pathspec==0.7.0
patsy==0.5.2
peewee==3.17.0
pep8==1.7.1
pexpect @ file:///tmp/build/80754af9/pexpect_1605563209008/work
pickleshare @ file:///tmp/build/80754af9/pickleshare_1606932040724/work
Pillow==8.4.0
pkginfo==1.7.1
platformdirs==4.3.7
plotly==5.13.0
plotly-express==0.4.1
pluggy @ file:///C:/ci/pluggy_1615976440052/work
ply==3.11
poyo @ file:///tmp/build/80754af9/poyo_1617751526755/work
prettytable==3.5.0
prometheus-client @ file:///tmp/build/80754af9/prometheus_client_1623189609245/work
prompt-toolkit @ file:///tmp/build/80754af9/prompt-toolkit_1633440160888/work
propcache==0.3.1
Protego==0.2.1
protobuf==3.19.1
psutil @ file:///C:/ci/psutil_1612298199233/work
ptan==0.4
ptyprocess @ file:///tmp/build/80754af9/ptyprocess_1609355006118/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
-e git+https://github.com/Raytone-D/puppet.git@6212d4306d520c60f5f168aec3b79ee56071ee6b#egg=puppet
py @ file:///tmp/build/80754af9/py_1607971587848/work
py-mini-racer==0.6.0
pyarrow==16.0.0
pyarrow-hotfix==0.6
pyasn1 @ file:///Users/<USER>/demo/mc3/conda-bld/pyasn1_1629708007385/work
pyasn1-modules==0.2.8
pycodestyle @ file:///tmp/build/80754af9/pycodestyle_1615748559966/work
pyconvert==0.6.3
pycosat==0.6.3
pycparser @ file:///tmp/build/80754af9/pycparser_1594388511720/work
pycryptodome==3.20.0
pycurl==7.44.1
pydantic==2.6.4
pydantic_core==2.16.3
pydeck==0.9.1
PyDirectInput==1.0.4
PyDispatcher==2.0.6
pydocstyle @ file:///tmp/build/80754af9/pydocstyle_1621600989141/work
pyecharts==1.9.1
pyecharts-snapshot==0.2.0
pyee==8.2.2
pyerfa @ file:///C:/ci/pyerfa_1621560974055/work
pyflakes @ file:///tmp/build/80754af9/pyflakes_1617200973297/work
pygame==2.6.1
pyglet==1.5.0
Pygments==2.18.0
PyJWT @ file:///C:/ci/pyjwt_1657511236979/work
pylint @ file:///C:/ci/pylint_1627536884966/work
pyls-spyder==0.4.0
pymongo==3.13.0
Pympler==1.1
PyMySQL==1.0.2
PyNaCl @ file:///C:/ci/pynacl_1607612759007/work
pyodbc===4.0.0-unsupported
PyOpenGL==3.1.6
pyOpenSSL @ file:///tmp/build/80754af9/pyopenssl_1635333100036/work
pyparsing @ file:///tmp/build/80754af9/pyparsing_1635766073266/work
pyppeteer==1.0.2
pyqtgraph==0.13.3
pyquery==2.0.1
pyreadline==2.1
pyrsistent @ file:///C:/ci/pyrsistent_1636093225342/work
PySide6==6.6.1
PySide6-Addons==6.6.1
PySide6-Essentials==6.6.1
PySocks @ file:///C:/ci/pysocks_1605307512533/work
pytdx==1.72
pytesseract==0.3.10
pytest==6.2.4
python-dateutil @ file:///tmp/build/80754af9/python-dateutil_1626374649649/work
python-docx==1.1.2
python-dotenv==1.1.0
python-engineio==4.3.4
python-lsp-black @ file:///tmp/build/80754af9/python-lsp-black_1634232156041/work
python-lsp-jsonrpc==1.0.0
python-lsp-server==1.2.4
python-slugify @ file:///tmp/build/80754af9/python-slugify_1620405669636/work
python-socketio==5.7.2
pytorch-ignite==0.4.10
pytz==2025.2
pytz-deprecation-shim==0.1.0.post0
PyWavelets @ file:///C:/ci/pywavelets_1607645631519/work
pywin32==228
pywin32-ctypes @ file:///C:/ci/pywin32-ctypes_1607553594546/work
pywinauto==0.6.6
pywinpty @ file:///C:/ci/pywinpty_1607419945780/work
PyYAML==6.0
pyzmq==22.3.0
QDarkStyle @ file:///tmp/build/80754af9/qdarkstyle_1617386714626/work
qstock @ file:///C:/Users/<USER>/Desktop/qstock-vip-1.3.3.tar.gz
qstylizer @ file:///tmp/build/80754af9/qstylizer_1617713584600/work/dist/qstylizer-0.1.10-py2.py3-none-any.whl
QtAwesome @ file:///tmp/build/80754af9/qtawesome_1615991616277/work
qtconsole @ file:///tmp/build/80754af9/qtconsole_1632739723211/work
QtPy @ file:///tmp/build/80754af9/qtpy_1629397026935/work
quantaxis==1.10.19
queuelib==1.6.2
redis==5.0.8
regex==2024.4.16
requests==2.32.3
requests-file==1.5.1
requests-oauthlib==1.3.0
requests-toolbelt==1.0.0
retrying==1.3.4
rich==13.7.1
rope @ file:///tmp/build/80754af9/rope_1623703006312/work
rsa @ file:///tmp/build/80754af9/rsa_1614366226499/work
Rtree @ file:///C:/ci/rtree_1618421015405/work
ruamel-yaml-conda @ file:///C:/ci/ruamel_yaml_1616016898638/work
safetensors==0.4.3
scikit-image==0.18.3
scikit-learn @ file:///C:/ci/scikit-learn_1622739499047/work
scikit-learn-intelex==2021.20210714.120553
scipy @ file:///C:/ci/scipy_1630606917240/work
Scrapy==2.7.1
seaborn @ file:///tmp/build/80754af9/seaborn_1629307859561/work
selenium==4.7.2
semver==3.0.2
Send2Trash @ file:///tmp/build/80754af9/send2trash_1632406701022/work
service-identity==21.1.0
shiboken6==6.6.1
simplegeneric==0.8.1
simplejson @ file:///C:/ci/simplejson_1639140968209/work
singledispatch @ file:///tmp/build/80754af9/singledispatch_1629321204894/work
sip==4.19.13
six @ file:///tmp/build/80754af9/six_1623709665295/work
smmap==5.0.1
sniffio @ file:///C:/ci/sniffio_1614030527509/work
snowballstemmer @ file:///tmp/build/80754af9/snowballstemmer_1611258885636/work
snownlp==0.12.3
sortedcollections @ file:///tmp/build/80754af9/sortedcollections_1611172717284/work
sortedcontainers @ file:///tmp/build/80754af9/sortedcontainers_1623949099177/work
soupsieve @ file:///tmp/build/80754af9/soupsieve_1616183228191/work
Sphinx==4.2.0
sphinxcontrib-applehelp @ file:///home/<USER>/src/ci/sphinxcontrib-applehelp_1611920841464/work
sphinxcontrib-devhelp @ file:///home/<USER>/src/ci/sphinxcontrib-devhelp_1611920923094/work
sphinxcontrib-htmlhelp @ file:///tmp/build/80754af9/sphinxcontrib-htmlhelp_1623945626792/work
sphinxcontrib-jsmath @ file:///home/<USER>/src/ci/sphinxcontrib-jsmath_1611920942228/work
sphinxcontrib-qthelp @ file:///home/<USER>/src/ci/sphinxcontrib-qthelp_1611921055322/work
sphinxcontrib-serializinghtml @ file:///tmp/build/80754af9/sphinxcontrib-serializinghtml_1624451540180/work
sphinxcontrib-websupport @ file:///tmp/build/80754af9/sphinxcontrib-websupport_1597081412696/work
spyder @ file:///C:/ci/spyder_1636480369575/work
spyder-kernels @ file:///C:/ci/spyder-kernels_1634237096710/work
SQLAlchemy @ file:///C:/ci/sqlalchemy_1626948551162/work
statsmodels==0.12.2
streamlit==1.12.0
sympy==1.14.0
TA-Lib @ file:///C:/Users/<USER>/Desktop/TA_Lib-0.4.24-cp39-cp39-win_amd64.whl
tables==3.6.1
tabulate==0.9.0
tb-nightly==1.15.0a20190801
TBB==0.2
tblib @ file:///Users/<USER>/demo/mc3/conda-bld/tblib_1629402031467/work
tenacity==8.2.1
termcolor==2.1.1
terminado==0.9.4
testpath @ file:///tmp/build/80754af9/testpath_1624638946665/work
text-unidecode @ file:///Users/<USER>/demo/mc3/conda-bld/text-unidecode_1629401354553/work
textdistance @ file:///tmp/build/80754af9/textdistance_1612461398012/work
threadpoolctl @ file:///Users/<USER>/demo/mc3/conda-bld/threadpoolctl_1629802263681/work
three-merge @ file:///tmp/build/80754af9/three-merge_1607553261110/work
thriftpy2==0.4.16
tifffile @ file:///tmp/build/80754af9/tifffile_1627275862826/work
tiktoken==0.6.0
tinycss @ file:///tmp/build/80754af9/tinycss_1617713798712/work
tldextract==3.4.0
tokenizers==0.19.1
toml @ file:///tmp/build/80754af9/toml_1616166611790/work
toolz @ file:///home/<USER>/recipes/ci/toolz_1610987900194/work
torch==2.7.1
torchaudio==0.13.1
torchvision==0.14.1
tornado @ file:///C:/ci/tornado_1606924294691/work
tqdm==4.66.2
traitlets @ file:///tmp/build/80754af9/traitlets_1632522747050/work
transformers==4.40.1
trio==0.22.0
trio-websocket==0.9.2
tushare==1.2.89
Twisted==22.10.0
twisted-iocpsupport==1.0.2
typed-ast @ file:///C:/ci/typed-ast_1624953797214/work
typing-inspect==0.9.0
typing_extensions==4.11.0
tzdata==2022.7
tzlocal==4.2
ujson @ file:///C:/ci/ujson_1611259568517/work
unicodecsv==0.14.1
Unidecode @ file:///tmp/build/80754af9/unidecode_1614712377438/work
urllib3==1.26.7
validators==0.33.0
vnpy @ file:///C:/Users/<USER>/Desktop/vnpy-3.9.0
vnpy-ctastrategy==1.1.7
vnpy-portfoliostrategy==1.0.8
vnpy-sqlite==1.0.1
w3lib==2.1.1
watchdog @ file:///C:/ci/watchdog_1624955113064/work
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
webencodings==0.5.1
websocket-client==0.57.0
websockets==10.4
Werkzeug @ file:///tmp/build/80754af9/werkzeug_1635505089296/work
whichcraft @ file:///tmp/build/80754af9/whichcraft_1617751293875/work
widgetsnbextension @ file:///C:/ci/widgetsnbextension_1607531582688/work
win-inet-pton @ file:///C:/ci/win_inet_pton_1605306162074/work
win-unicode-console==0.5
win32-setctime==1.1.0
wincertstore==0.2
wrapt @ file:///C:/ci/wrapt_1607574570428/work
wsproto==1.2.0
wxPython @ file:///C:/Qbot/dev/wxPython-4.2.1-cp39-cp39-win_amd64.whl
xlrd @ file:///tmp/build/80754af9/xlrd_1608072521494/work
XlsxWriter @ file:///tmp/build/80754af9/xlsxwriter_1628603415431/work
xlwings==0.24.9
xlwt==1.3.0
xmltodict @ file:///Users/<USER>/demo/mc3/conda-bld/xmltodict_1629301980723/work
xxhash==3.4.1
yapf @ file:///tmp/build/80754af9/yapf_1615749224965/work
yarl==1.20.0
yfinance==0.2.55
zenlog==1.1
zict==2.0.0
zipp @ file:///tmp/build/80754af9/zipp_1633618647012/work
zope.event==4.5.0
zope.interface @ file:///C:/ci/zope.interface_1625036252485/work
zstandard==0.18.0
