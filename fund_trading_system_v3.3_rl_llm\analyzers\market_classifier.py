"""
多维度市场分类器
基于六大维度评估结果对市场进行智能分类
"""

import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult


class MultiDimensionalMarketClassifier:
    """
    @class MultiDimensionalMarketClassifier
    @brief 多维度市场分类器
    @details 基于六大维度评估结果对市场进行智能分类
    """
    
    def __init__(self):
        self.name = "MultiDimensionalMarketClassifier"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

        # 基金类型特定的分类调整
        self.fund_type_adjustments = {
            'gold_etf': {
                'volatility_threshold': 0.6,  # 黄金ETF波动性阈值调整
                'trend_sensitivity': 0.8      # 趋势敏感度调整
            },
            'bond_fund': {
                'volatility_threshold': 0.3,  # 债券基金波动性阈值更低
                'trend_sensitivity': 1.2      # 趋势敏感度更高
            },
            'bank_stock': {
                'volatility_threshold': 0.5,  # 银行股波动性中等
                'trend_sensitivity': 0.9      # 趋势敏感度略低
            }
        }
        
        # 市场分类规则 - 增强版，覆盖更多场景
        self.classification_rules = {
            "强势突破": {
                "conditions": {"趋势": (">", 0.7), "结构": (">", 0.6), "流动性": (">", 0.5)},
                "priority": 1,
                "description": "强劲上涨趋势，结构良好"
            },
            "趋势确认": {
                "conditions": {"趋势": (">", 0.5), "情绪": (">", 0.3), "波动性": ("<", 0.7)},
                "priority": 2,
                "description": "明确上涨趋势，情绪积极"
            },
            "温和上涨": {
                "conditions": {"趋势": ("between", 0.3, 0.5), "波动性": ("<", 0.6), "流动性": (">", 0.3)},
                "priority": 3,
                "description": "温和上涨，波动较小"
            },
            "震荡整理": {
                "conditions": {"趋势": ("between", -0.3, 0.3), "波动性": ("<", 0.5)},
                "priority": 4,
                "description": "横盘整理，趋势不明"
            },
            "弱势整理": {
                "conditions": {"趋势": ("between", -0.5, -0.3), "波动性": ("<", 0.6)},
                "priority": 4,
                "description": "轻微下跌，整理状态"
            },
            "下跌趋势": {
                "conditions": {"趋势": ("<", -0.5), "情绪": ("<", -0.3)},
                "priority": 2,
                "description": "明确下跌趋势"
            },
            "高波动": {
                "conditions": {"波动性": (">", 0.7)},
                "priority": 3,
                "description": "高波动环境"
            },
            "风险警示": {
                "conditions": {"波动性": (">", 0.8), "情绪": ("<", -0.5)},
                "priority": 1,
                "description": "高风险，情绪恐慌"
            },
            "流动性不足": {
                "conditions": {"流动性": ("<", 0.2)},
                "priority": 2,
                "description": "流动性严重不足"
            }
        }
        
    def classify_market(self, evaluations: Dict[str, DimensionEvaluationResult],
                       fund_code: str = None, fund_category: str = None, debug: bool = False) -> Dict[str, Any]:
        """
        @brief 对市场进行分类
        @param evaluations: 各维度评估结果
        @return: 市场分类结果
        """
        try:
            # 提取评分
            scores = {dim: eval_result.score for dim, eval_result in evaluations.items()}

            # 应用基金类型调整
            adjusted_scores = self._apply_fund_type_adjustments(scores, fund_category)

            # 匹配分类规则
            matched_classifications = []
            debug_info = []

            for class_name, rule in self.classification_rules.items():
                is_match = self._check_conditions(adjusted_scores, rule["conditions"])
                confidence = self._calculate_match_confidence(adjusted_scores, rule["conditions"])

                if debug:
                    debug_info.append({
                        'rule': class_name,
                        'conditions': rule["conditions"],
                        'is_match': is_match,
                        'confidence': confidence,
                        'description': rule.get("description", "")
                    })

                if is_match:
                    matched_classifications.append({
                        'classification': class_name,
                        'priority': rule["priority"],
                        'confidence': confidence,
                        'description': rule.get("description", "")
                    })

            # 排序并选择最佳分类
            matched_classifications.sort(key=lambda x: (x['priority'], -x['confidence']))

            # 如果没有匹配的分类，尝试兜底分类
            if not matched_classifications:
                fallback_classification = self._get_fallback_classification(adjusted_scores, fund_category)
                primary_classification = fallback_classification
            else:
                primary_classification = matched_classifications[0]
            
            result = {
                'primary_classification': primary_classification['classification'],
                'classification_confidence': primary_classification['confidence'],
                'classification_description': primary_classification.get('description', ''),
                'all_matches': matched_classifications,
                'market_characteristics': self._extract_characteristics(evaluations),
                'classification_time': datetime.now().isoformat(),
                'fund_code': fund_code,
                'fund_category': fund_category,
                'original_scores': scores,
                'adjusted_scores': adjusted_scores if fund_category else scores
            }

            if debug:
                result['debug_info'] = debug_info
                result['classification_reason'] = self._explain_classification(
                    primary_classification, adjusted_scores, debug_info
                )

            return result
            
        except Exception as e:
            self.logger.error(f"Market classification failed: {str(e)}")
            return {
                'primary_classification': '分类错误',
                'classification_confidence': 0.0,
                'error': str(e)
            }
    
    def _check_conditions(self, scores: Dict[str, float], conditions: Dict[str, tuple]) -> bool:
        """检查分类条件"""
        for dimension, condition in conditions.items():
            if dimension not in scores:
                continue
                
            score = scores[dimension]
            operator, *params = condition
            
            if operator == ">":
                if not (score > params[0]):
                    return False
            elif operator == "<":
                if not (score < params[0]):
                    return False
            elif operator == "between":
                if not (params[0] <= score <= params[1]):
                    return False
        
        return True
    
    def _calculate_match_confidence(self, scores: Dict[str, float], conditions: Dict[str, tuple]) -> float:
        """计算匹配置信度"""
        total_confidence = 0.0
        condition_count = 0
        
        for dimension, condition in conditions.items():
            if dimension not in scores:
                continue
                
            score = scores[dimension]
            operator, *params = condition
            
            if operator == ">":
                confidence = min(1.0, max(0.0, (score - params[0]) / (1.0 - params[0])))
            elif operator == "<":
                confidence = min(1.0, max(0.0, (params[0] - score) / (params[0] + 1.0)))
            elif operator == "between":
                mid_point = (params[0] + params[1]) / 2
                confidence = 1.0 - abs(score - mid_point) / ((params[1] - params[0]) / 2)
            else:
                confidence = 0.5
                
            total_confidence += confidence
            condition_count += 1
        
        return total_confidence / max(1, condition_count)
    
    def _extract_characteristics(self, evaluations: Dict[str, DimensionEvaluationResult]) -> List[str]:
        """提取市场特征"""
        characteristics = []
        
        for dim_name, eval_result in evaluations.items():
            score = eval_result.score
            
            if score >= 0.7:
                characteristics.append(f"{dim_name}强势")
            elif score >= 0.3:
                characteristics.append(f"{dim_name}偏强")
            elif score <= -0.7:
                characteristics.append(f"{dim_name}弱势")
            elif score <= -0.3:
                characteristics.append(f"{dim_name}偏弱")
            else:
                characteristics.append(f"{dim_name}中性")
        
        return characteristics

    def _apply_fund_type_adjustments(self, scores: Dict[str, float], fund_category: str = None) -> Dict[str, float]:
        """应用基金类型特定的调整"""
        if not fund_category or fund_category not in self.fund_type_adjustments:
            return scores.copy()

        adjusted_scores = scores.copy()
        adjustments = self.fund_type_adjustments[fund_category]

        # 应用波动性阈值调整
        if 'volatility_threshold' in adjustments and '波动性' in adjusted_scores:
            volatility_adj = adjustments['volatility_threshold']
            adjusted_scores['波动性'] = adjusted_scores['波动性'] * volatility_adj

        # 应用趋势敏感度调整
        if 'trend_sensitivity' in adjustments and '趋势' in adjusted_scores:
            trend_adj = adjustments['trend_sensitivity']
            adjusted_scores['趋势'] = adjusted_scores['趋势'] * trend_adj

        return adjusted_scores

    def _get_fallback_classification(self, scores: Dict[str, float], fund_category: str = None) -> Dict[str, Any]:
        """获取兜底分类"""
        # 基于主要维度评分进行兜底分类
        trend_score = scores.get('趋势', 0)
        volatility_score = scores.get('波动性', 0)
        liquidity_score = scores.get('流动性', 0)

        # 基于趋势的基础分类
        if trend_score > 0.6:
            classification = "上涨趋势"
            confidence = min(0.8, trend_score)
        elif trend_score < -0.6:
            classification = "下跌趋势"
            confidence = min(0.8, abs(trend_score))
        elif volatility_score > 0.6:
            classification = "高波动整理"
            confidence = min(0.7, volatility_score)
        elif liquidity_score < 0.3:
            classification = "低流动性"
            confidence = 0.6
        else:
            classification = "中性整理"
            confidence = 0.5

        # 基金类型特定的兜底分类
        if fund_category:
            if fund_category == 'gold_etf':
                classification = f"黄金{classification}"
            elif fund_category == 'bond_fund':
                classification = f"债券{classification}"
            elif fund_category == 'bank_stock':
                classification = f"银行{classification}"

        return {
            'classification': classification,
            'confidence': confidence,
            'priority': 5,
            'description': f"兜底分类：{classification}"
        }

    def _explain_classification(self, classification: Dict[str, Any],
                               scores: Dict[str, float], debug_info: List[Dict]) -> str:
        """解释分类原因"""
        explanation = f"分类为'{classification['classification']}'的原因：\n"

        # 显示关键评分
        explanation += f"关键评分：趋势={scores.get('趋势', 0):.2f}, "
        explanation += f"波动性={scores.get('波动性', 0):.2f}, "
        explanation += f"流动性={scores.get('流动性', 0):.2f}\n"

        # 显示匹配的规则
        if debug_info:
            matched_rules = [info for info in debug_info if info['is_match']]
            if matched_rules:
                explanation += f"匹配的规则：{', '.join([rule['rule'] for rule in matched_rules])}\n"
            else:
                explanation += "没有匹配的规则，使用兜底分类\n"

        return explanation
