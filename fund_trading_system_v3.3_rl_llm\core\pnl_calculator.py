"""
盈亏计算器
计算基金持仓的各种盈亏指标和风险指标
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import asdict

from .data_structures import PositionInfo, Transaction, PerformanceMetrics


class PnLCalculator:
    """
    @class PnLCalculator
    @brief 盈亏计算器
    @details 计算持仓盈亏、风险指标和绩效指标
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def calculate_position_pnl(self, position: PositionInfo) -> Dict[str, float]:
        """
        @brief 计算单个持仓的盈亏
        @param position: 持仓信息
        @return: 盈亏计算结果
        """
        try:
            if position.shares <= 0 or position.average_cost <= 0:
                return self._get_zero_pnl()
            
            # 基础盈亏计算
            position_value = position.shares * position.current_price
            position_cost = position.shares * position.average_cost
            unrealized_pnl = position_value - position_cost
            unrealized_pnl_pct = (position.current_price / position.average_cost - 1) * 100
            
            # 日收益率计算（简化处理）
            daily_return = 0.0
            if position.holding_days > 0:
                daily_return = (unrealized_pnl_pct / 100) / position.holding_days
            
            # 年化收益率
            annualized_return = daily_return * 252 if daily_return != 0 else 0
            
            return {
                'position_value': position_value,
                'position_cost': position_cost,
                'unrealized_pnl': unrealized_pnl,
                'unrealized_pnl_pct': unrealized_pnl_pct,
                'daily_return': daily_return,
                'annualized_return': annualized_return,
                'holding_days': position.holding_days,
                'max_profit_pct': position.max_profit_pct * 100,
                'current_drawdown_pct': position.current_drawdown_pct * 100
            }
            
        except Exception as e:
            self.logger.error(f"持仓盈亏计算失败 {position.fund_code}: {str(e)}")
            return self._get_zero_pnl()
    
    def calculate_portfolio_pnl(self, positions: Dict[str, PositionInfo]) -> Dict[str, Any]:
        """
        @brief 计算组合盈亏
        @param positions: 所有持仓信息
        @return: 组合盈亏结果
        """
        try:
            if not positions:
                return {
                    'total_value': 0.0,
                    'total_cost': 0.0,
                    'total_pnl': 0.0,
                    'total_pnl_pct': 0.0,
                    'position_count': 0,
                    'profitable_positions': 0,
                    'losing_positions': 0,
                    'win_rate': 0.0,
                    'average_return': 0.0,
                    'best_performer': None,
                    'worst_performer': None
                }
            
            total_value = 0.0
            total_cost = 0.0
            total_pnl = 0.0
            profitable_count = 0
            losing_count = 0
            returns = []
            position_performances = []
            
            for fund_code, position in positions.items():
                pnl_data = self.calculate_position_pnl(position)
                
                total_value += pnl_data['position_value']
                total_cost += pnl_data['position_cost']
                total_pnl += pnl_data['unrealized_pnl']
                
                if pnl_data['unrealized_pnl'] > 0:
                    profitable_count += 1
                elif pnl_data['unrealized_pnl'] < 0:
                    losing_count += 1
                
                returns.append(pnl_data['unrealized_pnl_pct'])
                position_performances.append({
                    'fund_code': fund_code,
                    'pnl_pct': pnl_data['unrealized_pnl_pct'],
                    'pnl': pnl_data['unrealized_pnl']
                })
            
            # 计算组合指标
            total_pnl_pct = (total_value / total_cost - 1) * 100 if total_cost > 0 else 0
            win_rate = profitable_count / len(positions) if positions else 0
            average_return = np.mean(returns) if returns else 0
            
            # 找出最佳和最差表现
            position_performances.sort(key=lambda x: x['pnl_pct'], reverse=True)
            best_performer = position_performances[0] if position_performances else None
            worst_performer = position_performances[-1] if position_performances else None
            
            return {
                'total_value': total_value,
                'total_cost': total_cost,
                'total_pnl': total_pnl,
                'total_pnl_pct': total_pnl_pct,
                'position_count': len(positions),
                'profitable_positions': profitable_count,
                'losing_positions': losing_count,
                'win_rate': win_rate,
                'average_return': average_return,
                'return_std': np.std(returns) if len(returns) > 1 else 0,
                'best_performer': best_performer,
                'worst_performer': worst_performer,
                'calculation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"组合盈亏计算失败: {str(e)}")
            return {'error': str(e)}
    
    def calculate_risk_metrics(self, positions: Dict[str, PositionInfo], 
                             price_history: Dict[str, List[float]] = None) -> Dict[str, Any]:
        """
        @brief 计算风险指标
        @param positions: 持仓信息
        @param price_history: 价格历史数据（可选）
        @return: 风险指标
        """
        try:
            if not positions:
                return {'message': '无持仓数据'}
            
            # 基础风险指标
            portfolio_pnl = self.calculate_portfolio_pnl(positions)
            
            # 计算VaR（简化版本）
            returns = []
            position_weights = []
            total_value = portfolio_pnl.get('total_value', 0)
            
            for position in positions.values():
                pnl_data = self.calculate_position_pnl(position)
                returns.append(pnl_data['unrealized_pnl_pct'] / 100)
                
                if total_value > 0:
                    weight = (position.shares * position.current_price) / total_value
                    position_weights.append(weight)
                else:
                    position_weights.append(0)
            
            # 计算组合风险指标
            if len(returns) > 1:
                portfolio_volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
                var_95 = np.percentile(returns, 5) * 100  # 95% VaR
                var_99 = np.percentile(returns, 1) * 100  # 99% VaR
            else:
                portfolio_volatility = 0
                var_95 = 0
                var_99 = 0
            
            # 最大回撤
            max_drawdown = 0
            max_drawdown_position = None
            for fund_code, position in positions.items():
                if position.current_drawdown_pct > max_drawdown:
                    max_drawdown = position.current_drawdown_pct
                    max_drawdown_position = fund_code
            
            # 集中度风险
            concentration_risk = self._calculate_concentration_risk(position_weights)
            
            # 夏普比率（简化计算）
            avg_return = np.mean(returns) if returns else 0
            sharpe_ratio = (avg_return * 252) / portfolio_volatility if portfolio_volatility > 0 else 0
            
            return {
                'portfolio_volatility': portfolio_volatility,
                'var_95': var_95,
                'var_99': var_99,
                'max_drawdown': max_drawdown * 100,
                'max_drawdown_position': max_drawdown_position,
                'concentration_risk': concentration_risk,
                'sharpe_ratio': sharpe_ratio,
                'position_count': len(positions),
                'diversification_ratio': 1 / max(concentration_risk, 0.01),
                'risk_calculation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"风险指标计算失败: {str(e)}")
            return {'error': str(e)}
    
    def calculate_performance_metrics(self, positions: Dict[str, PositionInfo],
                                    transactions: List[Transaction] = None,
                                    benchmark_return: float = 0.0) -> PerformanceMetrics:
        """
        @brief 计算绩效指标
        @param positions: 持仓信息
        @param transactions: 交易历史
        @param benchmark_return: 基准收益率
        @return: 绩效指标
        """
        try:
            if not positions:
                return self._get_empty_performance_metrics()
            
            # 基础计算
            portfolio_pnl = self.calculate_portfolio_pnl(positions)
            risk_metrics = self.calculate_risk_metrics(positions)
            
            # 交易统计
            total_trades = len(transactions) if transactions else 0
            profitable_trades = 0
            if transactions:
                # 简化的盈利交易计算
                profitable_trades = sum(1 for t in transactions if t.transaction_type == 'sell')
            
            # 平均持仓天数
            holding_days = [pos.holding_days for pos in positions.values()]
            avg_holding_days = np.mean(holding_days) if holding_days else 0
            
            # 计算年化收益率
            total_return = portfolio_pnl.get('total_pnl_pct', 0) / 100
            avg_days = avg_holding_days if avg_holding_days > 0 else 1
            annualized_return = (1 + total_return) ** (252 / avg_days) - 1 if avg_days > 0 else 0
            
            # 计算其他指标
            volatility = risk_metrics.get('portfolio_volatility', 0)
            max_drawdown = risk_metrics.get('max_drawdown', 0) / 100
            sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
            win_rate = portfolio_pnl.get('win_rate', 0)
            
            # 盈亏比
            if portfolio_pnl.get('losing_positions', 0) > 0:
                avg_win = portfolio_pnl.get('total_pnl', 0) / max(portfolio_pnl.get('profitable_positions', 1), 1)
                avg_loss = abs(portfolio_pnl.get('total_pnl', 0)) / portfolio_pnl.get('losing_positions', 1)
                profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
            else:
                profit_factor = float('inf') if portfolio_pnl.get('total_pnl', 0) > 0 else 0
            
            return PerformanceMetrics(
                fund_code='PORTFOLIO',
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=total_trades,
                profitable_trades=profitable_trades,
                average_holding_days=avg_holding_days,
                calculation_period=int(avg_days)
            )
            
        except Exception as e:
            self.logger.error(f"绩效指标计算失败: {str(e)}")
            return self._get_empty_performance_metrics()
    
    def calculate_drawdown_analysis(self, position: PositionInfo) -> Dict[str, Any]:
        """
        @brief 计算回撤分析
        @param position: 持仓信息
        @return: 回撤分析结果
        """
        try:
            if position.max_profit_pct <= 0:
                return {
                    'status': 'no_profit',
                    'message': '尚未盈利，无回撤分析',
                    'current_pnl_pct': position.unrealized_pnl_pct * 100
                }
            
            # 回撤程度分析
            drawdown_pct = position.current_drawdown_pct * 100
            max_profit_pct = position.max_profit_pct * 100
            current_pnl_pct = position.unrealized_pnl_pct * 100
            
            # 回撤等级
            if drawdown_pct < 5:
                drawdown_level = 'minimal'
                risk_level = 'low'
            elif drawdown_pct < 10:
                drawdown_level = 'light'
                risk_level = 'low'
            elif drawdown_pct < 20:
                drawdown_level = 'moderate'
                risk_level = 'medium'
            elif drawdown_pct < 30:
                drawdown_level = 'significant'
                risk_level = 'high'
            else:
                drawdown_level = 'severe'
                risk_level = 'critical'
            
            # 建议行动
            if drawdown_pct >= 20:
                recommended_action = 'consider_sell'
                action_reason = '回撤超过20%，建议考虑卖出'
            elif drawdown_pct >= 15:
                recommended_action = 'monitor_closely'
                action_reason = '回撤接近警戒线，密切监控'
            elif drawdown_pct >= 10:
                recommended_action = 'watch'
                action_reason = '回撤适中，保持关注'
            else:
                recommended_action = 'hold'
                action_reason = '回撤较小，继续持有'
            
            return {
                'fund_code': position.fund_code,
                'max_profit_pct': max_profit_pct,
                'current_pnl_pct': current_pnl_pct,
                'drawdown_pct': drawdown_pct,
                'drawdown_level': drawdown_level,
                'risk_level': risk_level,
                'recommended_action': recommended_action,
                'action_reason': action_reason,
                'holding_days': position.holding_days,
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"回撤分析失败 {position.fund_code}: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_concentration_risk(self, weights: List[float]) -> float:
        """计算集中度风险（HHI指数）"""
        try:
            if not weights:
                return 0.0
            
            # 赫芬达尔指数
            hhi = sum(w**2 for w in weights)
            return hhi
            
        except Exception:
            return 0.0
    
    def _get_zero_pnl(self) -> Dict[str, float]:
        """获取零盈亏结果"""
        return {
            'position_value': 0.0,
            'position_cost': 0.0,
            'unrealized_pnl': 0.0,
            'unrealized_pnl_pct': 0.0,
            'daily_return': 0.0,
            'annualized_return': 0.0,
            'holding_days': 0,
            'max_profit_pct': 0.0,
            'current_drawdown_pct': 0.0
        }
    
    def _get_empty_performance_metrics(self) -> PerformanceMetrics:
        """获取空的绩效指标"""
        return PerformanceMetrics(
            fund_code='EMPTY',
            total_return=0.0,
            annualized_return=0.0,
            volatility=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            win_rate=0.0,
            profit_factor=0.0,
            total_trades=0,
            profitable_trades=0,
            average_holding_days=0.0,
            calculation_period=0
        )
    
    def calculate_position_sizing(self, available_capital: float, risk_per_trade: float,
                                 entry_price: float, stop_loss_price: float) -> Dict[str, Any]:
        """
        @brief 计算仓位大小
        @param available_capital: 可用资金
        @param risk_per_trade: 单笔交易风险比例
        @param entry_price: 入场价格
        @param stop_loss_price: 止损价格
        @return: 仓位计算结果
        """
        try:
            if entry_price <= 0 or stop_loss_price <= 0 or available_capital <= 0:
                return {'error': '参数无效'}
            
            # 计算风险金额
            risk_amount = available_capital * risk_per_trade
            
            # 计算每股风险
            risk_per_share = abs(entry_price - stop_loss_price)
            
            if risk_per_share <= 0:
                return {'error': '止损价格设置无效'}
            
            # 计算建议份额
            suggested_shares = risk_amount / risk_per_share
            
            # 计算建议投资金额
            suggested_investment = suggested_shares * entry_price
            
            # 计算仓位比例
            position_ratio = suggested_investment / available_capital
            
            return {
                'suggested_shares': suggested_shares,
                'suggested_investment': suggested_investment,
                'position_ratio': position_ratio,
                'risk_amount': risk_amount,
                'risk_per_share': risk_per_share,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'calculation_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"仓位计算失败: {str(e)}")
            return {'error': str(e)}
