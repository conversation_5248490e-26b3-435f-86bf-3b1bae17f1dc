#!/usr/bin/env python3
"""
测试增强版强化学习股票交易环境
"""

import numpy as np
import pandas as pd
import torch
import gymnasium as gym
from stable_baselines3 import SAC
from stable_baselines3.common.env_checker import check_env

# 简化版的环境测试
def generate_test_data(length: int = 100) -> pd.DataFrame:
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成基础价格序列
    base_price = 100
    returns = np.random.normal(0.001, 0.02, length)
    prices = [base_price]
    
    for ret in returns:
        new_price = prices[-1] * (1 + ret)
        prices.append(max(new_price, 1))
        
    prices = np.array(prices[1:])
    
    # 生成OHLC数据
    df = pd.DataFrame({
        'close': prices,
        'open': prices * (1 + np.random.normal(0, 0.005, length)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.01, length))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.01, length))),
        'volume': np.random.lognormal(10, 1, length),
    })
    
    # 确保OHLC逻辑正确
    df['high'] = np.maximum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['low'] = np.minimum.reduce([df['open'], df['high'], df['low'], df['close']])
    df['amount'] = df['close'] * df['volume']
    
    return df

def test_basic_imports():
    """测试基础导入"""
    print("=== 测试基础导入 ===")
    
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        
        import stable_baselines3
        print(f"✓ Stable-Baselines3版本: {stable_baselines3.__version__}")
        
        import tushare as ts
        print(f"✓ TuShare导入成功")
        
        import pandas as pd
        print(f"✓ Pandas版本: {pd.__version__}")
        
        import numpy as np
        print(f"✓ NumPy版本: {np.__version__}")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_data_generation():
    """测试数据生成"""
    print("\n=== 测试数据生成 ===")
    
    try:
        df = generate_test_data(50)
        print(f"✓ 生成数据形状: {df.shape}")
        print(f"✓ 数据列: {list(df.columns)}")
        print(f"✓ 价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
        
        return df
        
    except Exception as e:
        print(f"✗ 数据生成失败: {e}")
        return None

def test_simple_environment():
    """测试简单环境"""
    print("\n=== 测试简单环境 ===")
    
    try:
        # 创建一个最简单的交易环境
        class SimpleStockEnv(gym.Env):
            def __init__(self, df):
                super().__init__()
                self.df = df
                self.current_step = 0
                self.balance = 10000
                self.shares = 0
                
                # 定义观测和动作空间
                self.observation_space = gym.spaces.Box(
                    low=0, high=1, shape=(5,), dtype=np.float32
                )
                self.action_space = gym.spaces.Discrete(3)  # 0:hold, 1:buy, 2:sell
                
            def reset(self, seed=None, options=None):
                super().reset(seed=seed)
                self.current_step = 0
                self.balance = 10000
                self.shares = 0
                obs = self._get_observation()
                return obs, {}
                
            def step(self, action):
                if self.current_step >= len(self.df) - 1:
                    return self._get_observation(), 0, True, False, {}
                    
                current_price = self.df.loc[self.current_step, 'close']
                
                # 执行动作
                if action == 1 and self.balance >= current_price:  # buy
                    shares_to_buy = int(self.balance / current_price)
                    self.balance -= shares_to_buy * current_price
                    self.shares += shares_to_buy
                elif action == 2 and self.shares > 0:  # sell
                    self.balance += self.shares * current_price
                    self.shares = 0
                
                self.current_step += 1
                
                # 计算奖励
                net_worth = self.balance + self.shares * current_price
                reward = (net_worth - 10000) / 10000
                
                done = self.current_step >= len(self.df) - 1
                obs = self._get_observation()
                
                return obs, reward, done, False, {'net_worth': net_worth}
                
            def _get_observation(self):
                if self.current_step >= len(self.df):
                    self.current_step = len(self.df) - 1
                    
                row = self.df.iloc[self.current_step]
                obs = np.array([
                    row['close'] / 200,  # 归一化价格
                    row['volume'] / 1e6,  # 归一化成交量
                    self.balance / 20000,  # 归一化余额
                    self.shares / 100,     # 归一化持股
                    self.current_step / len(self.df)  # 进度
                ], dtype=np.float32)
                
                return np.clip(obs, 0, 1)
        
        # 测试环境
        df = generate_test_data(50)
        env = SimpleStockEnv(df)
        
        # 检查环境
        check_env(env)
        print("✓ 环境检查通过")
        
        # 测试重置和步进
        obs, info = env.reset()
        print(f"✓ 初始观测形状: {obs.shape}")
        
        action = env.action_space.sample()
        obs, reward, done, truncated, info = env.step(action)
        print(f"✓ 步进成功，奖励: {reward:.4f}")
        
        return env
        
    except Exception as e:
        print(f"✗ 环境测试失败: {e}")
        return None

def test_sac_training():
    """测试SAC训练"""
    print("\n=== 测试SAC训练 ===")
    
    try:
        # 创建环境
        df = generate_test_data(100)
        
        class SimpleStockEnv(gym.Env):
            def __init__(self, df):
                super().__init__()
                self.df = df
                self.current_step = 0
                self.balance = 10000
                self.shares = 0
                
                self.observation_space = gym.spaces.Box(
                    low=0, high=1, shape=(5,), dtype=np.float32
                )
                self.action_space = gym.spaces.Discrete(3)
                
            def reset(self, seed=None, options=None):
                super().reset(seed=seed)
                self.current_step = 0
                self.balance = 10000
                self.shares = 0
                obs = self._get_observation()
                return obs, {}
                
            def step(self, action):
                if self.current_step >= len(self.df) - 1:
                    return self._get_observation(), 0, True, False, {}
                    
                current_price = self.df.loc[self.current_step, 'close']
                
                if action == 1 and self.balance >= current_price:
                    shares_to_buy = int(self.balance / current_price)
                    self.balance -= shares_to_buy * current_price
                    self.shares += shares_to_buy
                elif action == 2 and self.shares > 0:
                    self.balance += self.shares * current_price
                    self.shares = 0
                
                self.current_step += 1
                net_worth = self.balance + self.shares * current_price
                reward = (net_worth - 10000) / 10000
                done = self.current_step >= len(self.df) - 1
                obs = self._get_observation()
                
                return obs, reward, done, False, {'net_worth': net_worth}
                
            def _get_observation(self):
                if self.current_step >= len(self.df):
                    self.current_step = len(self.df) - 1
                    
                row = self.df.iloc[self.current_step]
                obs = np.array([
                    row['close'] / 200,
                    row['volume'] / 1e6,
                    self.balance / 20000,
                    self.shares / 100,
                    self.current_step / len(self.df)
                ], dtype=np.float32)
                
                return np.clip(obs, 0, 1)
        
        env = SimpleStockEnv(df)
        
        # 创建SAC模型（使用DQN因为动作空间是离散的）
        from stable_baselines3 import DQN
        model = DQN('MlpPolicy', env, verbose=1, learning_rate=1e-4)
        
        print("✓ SAC模型创建成功")
        
        # 短时间训练测试
        print("开始短时间训练测试...")
        model.learn(total_timesteps=1000)
        print("✓ 训练完成")
        
        # 测试预测
        obs, _ = env.reset()
        action, _ = model.predict(obs)
        print(f"✓ 预测动作: {action}")
        
        return model
        
    except Exception as e:
        print(f"✗ SAC训练失败: {e}")
        return None

def main():
    """主测试函数"""
    print("开始测试增强版RL股票交易环境...")
    
    # 测试基础导入
    if not test_basic_imports():
        return
    
    # 测试数据生成
    df = test_data_generation()
    if df is None:
        return
    
    # 测试简单环境
    env = test_simple_environment()
    if env is None:
        return
    
    # 测试SAC训练
    model = test_sac_training()
    if model is None:
        return
    
    print("\n" + "="*50)
    print("🎉 所有测试通过！环境配置成功！")
    print("现在可以运行完整的增强版RL代码了。")
    print("="*50)

if __name__ == "__main__":
    main()