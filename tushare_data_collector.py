#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TuShare数据收集器
按照多频率数据需求，从TuShare获取数据并保存为CSV文件
"""

import pandas as pd
import numpy as np
import tushare as ts
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import time
import warnings
warnings.filterwarnings('ignore')

class TuShareDataCollector:
    """TuShare数据收集器"""
    
    def __init__(self, data_dir: str = './data'):
        self.data_dir = data_dir
        self.logger = self._setup_logger()
        
        # 创建数据目录结构
        self._create_data_directories()
        
        # 数据获取配置
        self.retry_count = 3
        self.retry_delay = 2
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('TuShareCollector')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _create_data_directories(self):
        """创建数据目录结构"""
        directories = [
            'high_freq',      # 高频数据 (15分钟K线)
            'medium_freq',    # 中频数据 (日度)
            'low_freq',       # 低频数据 (月度/季度)
            'macro',          # 宏观数据
            'fundamental',    # 基本面数据
            'news',           # 新闻数据
            'reference'       # 参考数据
        ]
        
        for directory in directories:
            dir_path = os.path.join(self.data_dir, directory)
            os.makedirs(dir_path, exist_ok=True)
            
        self.logger.info(f"数据目录创建完成: {self.data_dir}")
    
    def collect_all_data(self, fund_codes: List[str], start_date: str, end_date: str):
        """收集所有类型的数据"""
        
        self.logger.info(f"开始收集数据: {fund_codes}, {start_date} to {end_date}")
        
        try:
            # 1. 收集高频数据 (15分钟K线)
            self.collect_high_freq_data(fund_codes, start_date, end_date)
            
            # 2. 收集中频数据 (日度)
            self.collect_medium_freq_data(fund_codes, start_date, end_date)
            
            # 3. 收集低频数据 (月度/季度)
            self.collect_low_freq_data(start_date, end_date)
            
            # 4. 收集宏观数据
            self.collect_macro_data(start_date, end_date)
            
            # 5. 收集基本面数据
            self.collect_fundamental_data(fund_codes, start_date, end_date)
            
            # 6. 收集新闻数据
            self.collect_news_data()
            
            # 7. 收集参考数据
            self.collect_reference_data()
            
            self.logger.info("所有数据收集完成")
            
        except Exception as e:
            self.logger.error(f"数据收集失败: {e}")
            raise
    
    def collect_high_freq_data(self, fund_codes: List[str], start_date: str, end_date: str):
        """收集高频数据 (15分钟K线)"""
        
        self.logger.info("开始收集高频数据...")
        
        for fund_code in fund_codes:
            try:
                # 获取15分钟K线数据
                df_15min = self._get_kline_data(fund_code, '15', start_date, end_date)
                if df_15min is not None and not df_15min.empty:
                    filename = f"{fund_code}_15min.csv"
                    filepath = os.path.join(self.data_dir, 'high_freq', filename)
                    df_15min.to_csv(filepath, encoding='utf-8-sig')
                    self.logger.info(f"保存15分钟数据: {filename}")
                
                # 获取5分钟K线数据 (备用)
                df_5min = self._get_kline_data(fund_code, '5', start_date, end_date)
                if df_5min is not None and not df_5min.empty:
                    filename = f"{fund_code}_5min.csv"
                    filepath = os.path.join(self.data_dir, 'high_freq', filename)
                    df_5min.to_csv(filepath, encoding='utf-8-sig')
                    self.logger.info(f"保存5分钟数据: {filename}")
                
                # 获取30分钟K线数据 (备用)
                df_30min = self._get_kline_data(fund_code, '30', start_date, end_date)
                if df_30min is not None and not df_30min.empty:
                    filename = f"{fund_code}_30min.csv"
                    filepath = os.path.join(self.data_dir, 'high_freq', filename)
                    df_30min.to_csv(filepath, encoding='utf-8-sig')
                    self.logger.info(f"保存30分钟数据: {filename}")
                
                # 延迟避免频率限制
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"获取{fund_code}高频数据失败: {e}")
                continue
    
    def collect_medium_freq_data(self, fund_codes: List[str], start_date: str, end_date: str):
        """收集中频数据 (日度)"""
        
        self.logger.info("开始收集中频数据...")
        
        for fund_code in fund_codes:
            try:
                # 获取日度K线数据
                df_daily = self._get_hist_data(fund_code, start_date, end_date)
                if df_daily is not None and not df_daily.empty:
                    filename = f"{fund_code}_daily.csv"
                    filepath = os.path.join(self.data_dir, 'medium_freq', filename)
                    df_daily.to_csv(filepath, encoding='utf-8-sig')
                    self.logger.info(f"保存日度数据: {filename}")
                
                # 获取复权数据
                df_adj = self._get_h_data(fund_code, start_date, end_date)
                if df_adj is not None and not df_adj.empty:
                    filename = f"{fund_code}_daily_adj.csv"
                    filepath = os.path.join(self.data_dir, 'medium_freq', filename)
                    df_adj.to_csv(filepath, encoding='utf-8-sig')
                    self.logger.info(f"保存复权数据: {filename}")
                
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"获取{fund_code}中频数据失败: {e}")
                continue
        
        # 获取融资融券数据
        try:
            self._collect_margin_data(start_date, end_date)
        except Exception as e:
            self.logger.error(f"获取融资融券数据失败: {e}")
        
        # 获取龙虎榜数据
        try:
            self._collect_dragon_tiger_data(start_date, end_date)
        except Exception as e:
            self.logger.error(f"获取龙虎榜数据失败: {e}")
    
    def collect_low_freq_data(self, start_date: str, end_date: str):
        """收集低频数据 (月度/季度)"""
        
        self.logger.info("开始收集低频数据...")
        
        # 获取股票基本信息
        try:
            df_basics = ts.get_stock_basics()
            if df_basics is not None and not df_basics.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'stock_basics.csv')
                df_basics.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存股票基本信息")
        except Exception as e:
            self.logger.error(f"获取股票基本信息失败: {e}")
        
        # 获取行业分类
        try:
            df_industry = ts.get_industry_classified()
            if df_industry is not None and not df_industry.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'industry_classified.csv')
                df_industry.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存行业分类数据")
        except Exception as e:
            self.logger.error(f"获取行业分类失败: {e}")
        
        # 获取概念分类
        try:
            df_concept = ts.get_concept_classified()
            if df_concept is not None and not df_concept.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'concept_classified.csv')
                df_concept.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存概念分类数据")
        except Exception as e:
            self.logger.error(f"获取概念分类失败: {e}")
        
        # 获取指数成分股
        try:
            self._collect_index_components()
        except Exception as e:
            self.logger.error(f"获取指数成分股失败: {e}")
    
    def collect_macro_data(self, start_date: str, end_date: str):
        """收集宏观数据"""
        
        self.logger.info("开始收集宏观数据...")
        
        # GDP数据
        try:
            df_gdp_year = ts.get_gdp_year()
            if df_gdp_year is not None and not df_gdp_year.empty:
                filepath = os.path.join(self.data_dir, 'macro', 'gdp_year.csv')
                df_gdp_year.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存年度GDP数据")
        except Exception as e:
            self.logger.error(f"获取年度GDP数据失败: {e}")
        
        try:
            df_gdp_quarter = ts.get_gdp_quarter()
            if df_gdp_quarter is not None and not df_gdp_quarter.empty:
                filepath = os.path.join(self.data_dir, 'macro', 'gdp_quarter.csv')
                df_gdp_quarter.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存季度GDP数据")
        except Exception as e:
            self.logger.error(f"获取季度GDP数据失败: {e}")
        
        # CPI数据
        try:
            df_cpi = ts.get_cpi()
            if df_cpi is not None and not df_cpi.empty:
                filepath = os.path.join(self.data_dir, 'macro', 'cpi.csv')
                df_cpi.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存CPI数据")
        except Exception as e:
            self.logger.error(f"获取CPI数据失败: {e}")
        
        # PPI数据
        try:
            df_ppi = ts.get_ppi()
            if df_ppi is not None and not df_ppi.empty:
                filepath = os.path.join(self.data_dir, 'macro', 'ppi.csv')
                df_ppi.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存PPI数据")
        except Exception as e:
            self.logger.error(f"获取PPI数据失败: {e}")
        
        # Shibor数据
        try:
            self._collect_shibor_data()
        except Exception as e:
            self.logger.error(f"获取Shibor数据失败: {e}")
        
        # LPR数据
        try:
            self._collect_lpr_data()
        except Exception as e:
            self.logger.error(f"获取LPR数据失败: {e}")
    
    def collect_fundamental_data(self, fund_codes: List[str], start_date: str, end_date: str):
        """收集基本面数据"""
        
        self.logger.info("开始收集基本面数据...")
        
        # 获取最近几个季度的数据
        current_year = datetime.now().year
        start_year = datetime.strptime(start_date, '%Y-%m-%d').year
        
        for year in range(start_year, current_year + 1):
            for quarter in [1, 2, 3, 4]:
                try:
                    # 业绩报告数据
                    df_report = ts.get_report_data(year, quarter)
                    if df_report is not None and not df_report.empty:
                        filename = f"report_data_{year}Q{quarter}.csv"
                        filepath = os.path.join(self.data_dir, 'fundamental', filename)
                        df_report.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存业绩报告: {filename}")
                    
                    time.sleep(2)  # 避免频率限制
                    
                    # 盈利能力数据
                    df_profit = ts.get_profit_data(year, quarter)
                    if df_profit is not None and not df_profit.empty:
                        filename = f"profit_data_{year}Q{quarter}.csv"
                        filepath = os.path.join(self.data_dir, 'fundamental', filename)
                        df_profit.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存盈利能力: {filename}")
                    
                    time.sleep(2)
                    
                    # 营运能力数据
                    df_operation = ts.get_operation_data(year, quarter)
                    if df_operation is not None and not df_operation.empty:
                        filename = f"operation_data_{year}Q{quarter}.csv"
                        filepath = os.path.join(self.data_dir, 'fundamental', filename)
                        df_operation.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存营运能力: {filename}")
                    
                    time.sleep(2)
                    
                    # 成长能力数据
                    df_growth = ts.get_growth_data(year, quarter)
                    if df_growth is not None and not df_growth.empty:
                        filename = f"growth_data_{year}Q{quarter}.csv"
                        filepath = os.path.join(self.data_dir, 'fundamental', filename)
                        df_growth.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存成长能力: {filename}")
                    
                    time.sleep(2)
                    
                    # 偿债能力数据
                    df_debtpaying = ts.get_debtpaying_data(year, quarter)
                    if df_debtpaying is not None and not df_debtpaying.empty:
                        filename = f"debtpaying_data_{year}Q{quarter}.csv"
                        filepath = os.path.join(self.data_dir, 'fundamental', filename)
                        df_debtpaying.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存偿债能力: {filename}")
                    
                    time.sleep(2)
                    
                    # 现金流量数据
                    df_cashflow = ts.get_cashflow_data(year, quarter)
                    if df_cashflow is not None and not df_cashflow.empty:
                        filename = f"cashflow_data_{year}Q{quarter}.csv"
                        filepath = os.path.join(self.data_dir, 'fundamental', filename)
                        df_cashflow.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存现金流量: {filename}")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    self.logger.error(f"获取{year}Q{quarter}基本面数据失败: {e}")
                    continue
    
    def collect_news_data(self):
        """收集新闻数据"""
        
        self.logger.info("开始收集新闻数据...")
        
        try:
            # 获取最新新闻
            df_news = ts.get_latest_news(top=100)
            if df_news is not None and not df_news.empty:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"latest_news_{timestamp}.csv"
                filepath = os.path.join(self.data_dir, 'news', filename)
                df_news.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info(f"保存最新新闻: {filename}")
        except Exception as e:
            self.logger.error(f"获取最新新闻失败: {e}")
        
        try:
            # 获取股吧消息
            df_guba = ts.guba_sina()
            if df_guba is not None and not df_guba.empty:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"guba_sina_{timestamp}.csv"
                filepath = os.path.join(self.data_dir, 'news', filename)
                df_guba.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info(f"保存股吧消息: {filename}")
        except Exception as e:
            self.logger.error(f"获取股吧消息失败: {e}")
    
    def collect_reference_data(self):
        """收集参考数据"""
        
        self.logger.info("开始收集参考数据...")
        
        try:
            # 获取新股数据
            df_new_stocks = ts.new_stocks()
            if df_new_stocks is not None and not df_new_stocks.empty:
                filepath = os.path.join(self.data_dir, 'reference', 'new_stocks.csv')
                df_new_stocks.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存新股数据")
        except Exception as e:
            self.logger.error(f"获取新股数据失败: {e}")
        
        try:
            # 获取分红送股数据
            df_profit = ts.profit_data(top=1000)
            if df_profit is not None and not df_profit.empty:
                filepath = os.path.join(self.data_dir, 'reference', 'profit_data.csv')
                df_profit.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存分红送股数据")
        except Exception as e:
            self.logger.error(f"获取分红送股数据失败: {e}")
    
    def _get_kline_data(self, code: str, ktype: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取K线数据"""
        
        for attempt in range(self.retry_count):
            try:
                df = ts.get_hist_data(code, ktype=ktype, start=start_date, end=end_date)
                if df is not None and not df.empty:
                    return df
                else:
                    self.logger.warning(f"获取{code} {ktype}分钟数据为空")
                    return None
                    
            except Exception as e:
                self.logger.warning(f"获取{code} {ktype}分钟数据失败 (尝试{attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    return None
        
        return None
    
    def _get_hist_data(self, code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取历史数据"""
        
        for attempt in range(self.retry_count):
            try:
                df = ts.get_hist_data(code, start=start_date, end=end_date)
                if df is not None and not df.empty:
                    return df
                else:
                    return None
                    
            except Exception as e:
                self.logger.warning(f"获取{code}历史数据失败 (尝试{attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    return None
        
        return None
    
    def _get_h_data(self, code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """获取复权数据"""
        
        for attempt in range(self.retry_count):
            try:
                df = ts.get_h_data(code, start=start_date, end=end_date)
                if df is not None and not df.empty:
                    return df
                else:
                    return None
                    
            except Exception as e:
                self.logger.warning(f"获取{code}复权数据失败 (尝试{attempt+1}/{self.retry_count}): {e}")
                if attempt < self.retry_count - 1:
                    time.sleep(self.retry_delay)
                else:
                    return None
        
        return None    

    def _collect_margin_data(self, start_date: str, end_date: str):
        """收集融资融券数据"""
        
        try:
            # 上海融资融券汇总数据
            df_sh_margins = ts.sh_margins(start=start_date, end=end_date)
            if df_sh_margins is not None and not df_sh_margins.empty:
                filepath = os.path.join(self.data_dir, 'medium_freq', 'sh_margins.csv')
                df_sh_margins.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存上海融资融券汇总数据")
            
            time.sleep(2)
            
            # 深圳融资融券汇总数据
            df_sz_margins = ts.sz_margins(start=start_date, end=end_date)
            if df_sz_margins is not None and not df_sz_margins.empty:
                filepath = os.path.join(self.data_dir, 'medium_freq', 'sz_margins.csv')
                df_sz_margins.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存深圳融资融券汇总数据")
            
        except Exception as e:
            self.logger.error(f"收集融资融券数据失败: {e}")
    
    def _collect_dragon_tiger_data(self, start_date: str, end_date: str):
        """收集龙虎榜数据"""
        
        try:
            # 生成日期范围
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            current_dt = start_dt
            all_dragon_tiger_data = []
            
            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y-%m-%d')
                
                try:
                    df_dragon_tiger = ts.top_list(date_str)
                    if df_dragon_tiger is not None and not df_dragon_tiger.empty:
                        df_dragon_tiger['date'] = date_str
                        all_dragon_tiger_data.append(df_dragon_tiger)
                        self.logger.info(f"获取龙虎榜数据: {date_str}")
                    
                    time.sleep(1)  # 避免频率限制
                    
                except Exception as e:
                    self.logger.warning(f"获取{date_str}龙虎榜数据失败: {e}")
                
                current_dt += timedelta(days=1)
            
            # 合并所有龙虎榜数据
            if all_dragon_tiger_data:
                df_all_dragon_tiger = pd.concat(all_dragon_tiger_data, ignore_index=True)
                filepath = os.path.join(self.data_dir, 'medium_freq', 'dragon_tiger_list.csv')
                df_all_dragon_tiger.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存龙虎榜数据")
            
            # 获取机构席位统计
            df_inst_tops = ts.inst_tops()
            if df_inst_tops is not None and not df_inst_tops.empty:
                filepath = os.path.join(self.data_dir, 'medium_freq', 'inst_tops.csv')
                df_inst_tops.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存机构席位统计")
            
            time.sleep(2)
            
            # 获取营业部统计
            df_broker_tops = ts.broker_tops()
            if df_broker_tops is not None and not df_broker_tops.empty:
                filepath = os.path.join(self.data_dir, 'medium_freq', 'broker_tops.csv')
                df_broker_tops.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存营业部统计")
            
            time.sleep(2)
            
            # 获取个股统计
            df_cap_tops = ts.cap_tops()
            if df_cap_tops is not None and not df_cap_tops.empty:
                filepath = os.path.join(self.data_dir, 'medium_freq', 'cap_tops.csv')
                df_cap_tops.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存个股统计")
            
        except Exception as e:
            self.logger.error(f"收集龙虎榜数据失败: {e}")
    
    def _collect_index_components(self):
        """收集指数成分股"""
        
        try:
            # 沪深300成分股
            df_hs300 = ts.get_hs300s()
            if df_hs300 is not None and not df_hs300.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'hs300_components.csv')
                df_hs300.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存沪深300成分股")
            
            time.sleep(1)
            
            # 上证50成分股
            df_sz50 = ts.get_sz50s()
            if df_sz50 is not None and not df_sz50.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'sz50_components.csv')
                df_sz50.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存上证50成分股")
            
            time.sleep(1)
            
            # 中证500成分股
            df_zz500 = ts.get_zz500s()
            if df_zz500 is not None and not df_zz500.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'zz500_components.csv')
                df_zz500.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存中证500成分股")
            
            time.sleep(1)
            
            # 中小板股票
            df_sme = ts.get_sme_classified()
            if df_sme is not None and not df_sme.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'sme_stocks.csv')
                df_sme.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存中小板股票")
            
            time.sleep(1)
            
            # 创业板股票
            df_gem = ts.get_gem_classified()
            if df_gem is not None and not df_gem.empty:
                filepath = os.path.join(self.data_dir, 'low_freq', 'gem_stocks.csv')
                df_gem.to_csv(filepath, encoding='utf-8-sig')
                self.logger.info("保存创业板股票")
            
        except Exception as e:
            self.logger.error(f"收集指数成分股失败: {e}")
    
    def _collect_shibor_data(self):
        """收集Shibor数据"""
        
        try:
            # 获取最近几年的Shibor数据
            current_year = datetime.now().year
            
            for year in range(current_year - 3, current_year + 1):
                try:
                    df_shibor = ts.shibor_data(year)
                    if df_shibor is not None and not df_shibor.empty:
                        filename = f"shibor_{year}.csv"
                        filepath = os.path.join(self.data_dir, 'macro', filename)
                        df_shibor.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存Shibor数据: {filename}")
                    
                    time.sleep(2)
                    
                    # Shibor报价数据
                    df_shibor_quote = ts.shibor_quote_data(year)
                    if df_shibor_quote is not None and not df_shibor_quote.empty:
                        filename = f"shibor_quote_{year}.csv"
                        filepath = os.path.join(self.data_dir, 'macro', filename)
                        df_shibor_quote.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存Shibor报价数据: {filename}")
                    
                    time.sleep(2)
                    
                    # Shibor均线数据
                    df_shibor_ma = ts.shibor_ma_data(year)
                    if df_shibor_ma is not None and not df_shibor_ma.empty:
                        filename = f"shibor_ma_{year}.csv"
                        filepath = os.path.join(self.data_dir, 'macro', filename)
                        df_shibor_ma.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存Shibor均线数据: {filename}")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    self.logger.error(f"获取{year}年Shibor数据失败: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"收集Shibor数据失败: {e}")
    
    def _collect_lpr_data(self):
        """收集LPR数据"""
        
        try:
            # 获取最近几年的LPR数据
            current_year = datetime.now().year
            
            for year in range(current_year - 3, current_year + 1):
                try:
                    df_lpr = ts.lpr_data(year)
                    if df_lpr is not None and not df_lpr.empty:
                        filename = f"lpr_{year}.csv"
                        filepath = os.path.join(self.data_dir, 'macro', filename)
                        df_lpr.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存LPR数据: {filename}")
                    
                    time.sleep(2)
                    
                    # LPR均线数据
                    df_lpr_ma = ts.lpr_ma_data(year)
                    if df_lpr_ma is not None and not df_lpr_ma.empty:
                        filename = f"lpr_ma_{year}.csv"
                        filepath = os.path.join(self.data_dir, 'macro', filename)
                        df_lpr_ma.to_csv(filepath, encoding='utf-8-sig')
                        self.logger.info(f"保存LPR均线数据: {filename}")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    self.logger.error(f"获取{year}年LPR数据失败: {e}")
                    continue
                    
        except Exception as e:
            self.logger.error(f"收集LPR数据失败: {e}")
    
    def load_data_from_csv(self, data_type: str, filename: str) -> Optional[pd.DataFrame]:
        """从CSV文件加载数据"""
        
        try:
            filepath = os.path.join(self.data_dir, data_type, filename)
            if os.path.exists(filepath):
                df = pd.read_csv(filepath, encoding='utf-8-sig', index_col=0)
                return df
            else:
                self.logger.warning(f"文件不存在: {filepath}")
                return None
                
        except Exception as e:
            self.logger.error(f"加载CSV文件失败: {e}")
            return None
    
    def get_available_data_files(self) -> Dict[str, List[str]]:
        """获取可用的数据文件列表"""
        
        available_files = {}
        
        for data_type in ['high_freq', 'medium_freq', 'low_freq', 'macro', 'fundamental', 'news', 'reference']:
            dir_path = os.path.join(self.data_dir, data_type)
            if os.path.exists(dir_path):
                files = [f for f in os.listdir(dir_path) if f.endswith('.csv')]
                available_files[data_type] = files
            else:
                available_files[data_type] = []
        
        return available_files
    
    def create_data_summary(self) -> pd.DataFrame:
        """创建数据摘要"""
        
        summary_data = []
        available_files = self.get_available_data_files()
        
        for data_type, files in available_files.items():
            for filename in files:
                try:
                    df = self.load_data_from_csv(data_type, filename)
                    if df is not None:
                        summary_data.append({
                            'data_type': data_type,
                            'filename': filename,
                            'rows': len(df),
                            'columns': len(df.columns),
                            'start_date': df.index.min() if hasattr(df.index, 'min') else 'N/A',
                            'end_date': df.index.max() if hasattr(df.index, 'max') else 'N/A',
                            'file_size_mb': round(os.path.getsize(os.path.join(self.data_dir, data_type, filename)) / 1024 / 1024, 2)
                        })
                except Exception as e:
                    self.logger.error(f"处理文件{filename}时出错: {e}")
                    continue
        
        summary_df = pd.DataFrame(summary_data)
        
        # 保存摘要
        summary_filepath = os.path.join(self.data_dir, 'data_summary.csv')
        summary_df.to_csv(summary_filepath, encoding='utf-8-sig', index=False)
        self.logger.info(f"数据摘要已保存: {summary_filepath}")
        
        return summary_df


def main():
    """主函数 - 数据收集示例"""
    
    # 创建数据收集器
    collector = TuShareDataCollector(data_dir='./tushare_data')
    
    # 配置参数
    fund_codes = ['518880', '159919', '510300', '510500']  # ETF基金代码
    start_date = '2022-01-01'
    end_date = '2024-01-01'
    
    print("开始收集TuShare数据...")
    print(f"基金代码: {fund_codes}")
    print(f"时间范围: {start_date} to {end_date}")
    print("-" * 50)
    
    try:
        # 收集所有数据
        collector.collect_all_data(fund_codes, start_date, end_date)
        
        # 创建数据摘要
        summary_df = collector.create_data_summary()
        print("\n数据收集完成！")
        print("\n数据摘要:")
        print(summary_df.to_string(index=False))
        
        # 显示可用文件
        available_files = collector.get_available_data_files()
        print("\n可用数据文件:")
        for data_type, files in available_files.items():
            print(f"\n{data_type}:")
            for filename in files[:5]:  # 只显示前5个文件
                print(f"  - {filename}")
            if len(files) > 5:
                print(f"  ... 还有 {len(files) - 5} 个文件")
        
    except Exception as e:
        print(f"数据收集失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()