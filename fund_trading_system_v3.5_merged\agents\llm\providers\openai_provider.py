"""
OpenAI LLM Provider

Implements LLM provider interface for OpenAI GPT models.
"""

import json
import time
from typing import Dict, Any, Optional
import logging

from .base_provider import <PERSON><PERSON><PERSON><PERSON>B<PERSON>, LLMResponse, LLMConfig

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    openai = None


class OpenAIProvider(LLMProviderBase):
    """OpenAI GPT provider implementation"""
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.client = None
        
        if not OPENAI_AVAILABLE:
            self.logger.warning("OpenAI library not available. Install with: pip install openai")
    
    def initialize(self) -> bool:
        """Initialize OpenAI client"""
        if not OPENAI_AVAILABLE:
            self.logger.error("OpenAI library not installed")
            return False
        
        if not self.config.api_key:
            self.logger.error("OpenAI API key not provided")
            return False
        
        try:
            # Initialize OpenAI client
            openai.api_key = self.config.api_key
            if self.config.api_base:
                openai.api_base = self.config.api_base
            
            # Test connection with a simple call
            response = openai.ChatCompletion.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                temperature=0.1
            )
            
            self.is_initialized = True
            self.logger.info(f"OpenAI provider initialized with model {self.config.model_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenAI provider: {e}")
            return False
    
    def analyze_market_sentiment(self, text: str, context: Dict[str, Any] = None) -> LLMResponse:
        """Analyze market sentiment using OpenAI"""
        if not self.is_initialized:
            return self._create_error_response("Provider not initialized")
        
        self._increment_call_count()
        
        try:
            # Prepare the prompt for sentiment analysis
            system_prompt = """You are a financial market sentiment analyst. Analyze the given text and provide:
1. Overall sentiment (positive/negative/neutral)
2. Confidence level (0-1)
3. Key reasoning points
4. Sentiment scores for positive, negative, and neutral

Respond in JSON format:
{
    "sentiment": "positive/negative/neutral",
    "confidence": 0.0-1.0,
    "reasoning": "explanation",
    "scores": {
        "positive": 0.0-1.0,
        "negative": 0.0-1.0,
        "neutral": 0.0-1.0
    }
}"""
            
            user_prompt = f"Analyze the market sentiment of this text:\n\n{text}"
            
            if context:
                user_prompt += f"\n\nAdditional context: {json.dumps(context)}"
            
            response = self._retry_with_backoff(
                openai.ChatCompletion.create,
                model=self.config.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )
            
            # Parse response
            content = response.choices[0].message.content
            self._add_tokens(response.usage.total_tokens)
            
            try:
                # Try to parse JSON response
                result = json.loads(content)
                sentiment_scores = result.get('scores', {})
                confidence = result.get('confidence', 0.5)
                reasoning = result.get('reasoning', 'OpenAI sentiment analysis')
                
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                sentiment_scores = {'positive': 0.5, 'negative': 0.3, 'neutral': 0.2}
                confidence = 0.5
                reasoning = "OpenAI analysis (JSON parse failed)"
            
            return LLMResponse(
                content=content,
                confidence=confidence,
                sentiment_scores=sentiment_scores,
                reasoning=reasoning,
                metadata={
                    'model': self.config.model_name,
                    'tokens_used': response.usage.total_tokens,
                    'finish_reason': response.choices[0].finish_reason
                },
                provider=self.config.provider_name,
                model=self.config.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"OpenAI sentiment analysis failed: {e}")
            return self._create_error_response(str(e))
    
    def generate_market_insight(self, market_data: Dict[str, Any]) -> LLMResponse:
        """Generate market insights using OpenAI"""
        if not self.is_initialized:
            return self._create_error_response("Provider not initialized")
        
        self._increment_call_count()
        
        try:
            # Prepare market data summary
            data_summary = {
                'symbol': market_data.get('symbol', 'Unknown'),
                'price_change': market_data.get('price_change', 0),
                'volume': market_data.get('volume', 0),
                'technical_indicators': market_data.get('technical_indicators', {}),
                'recent_news': market_data.get('news_data', [])[:3]  # Last 3 news items
            }
            
            system_prompt = """You are a professional financial analyst. Based on the provided market data, generate insights about:
1. Current market sentiment
2. Key technical signals
3. Potential trading opportunities
4. Risk factors to consider

Provide your analysis in JSON format:
{
    "insight": "main insight summary",
    "sentiment": "bullish/bearish/neutral",
    "confidence": 0.0-1.0,
    "key_points": ["point1", "point2", "point3"],
    "risk_level": "low/medium/high",
    "scores": {
        "positive": 0.0-1.0,
        "negative": 0.0-1.0,
        "neutral": 0.0-1.0
    }
}"""
            
            user_prompt = f"Analyze this market data and provide insights:\n\n{json.dumps(data_summary, indent=2)}"
            
            response = self._retry_with_backoff(
                openai.ChatCompletion.create,
                model=self.config.model_name,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                timeout=self.config.timeout
            )
            
            # Parse response
            content = response.choices[0].message.content
            self._add_tokens(response.usage.total_tokens)
            
            try:
                # Try to parse JSON response
                result = json.loads(content)
                sentiment_scores = result.get('scores', {})
                confidence = result.get('confidence', 0.5)
                reasoning = result.get('insight', 'OpenAI market analysis')
                
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                sentiment_scores = {'positive': 0.4, 'negative': 0.3, 'neutral': 0.3}
                confidence = 0.5
                reasoning = "OpenAI market analysis (JSON parse failed)"
            
            return LLMResponse(
                content=content,
                confidence=confidence,
                sentiment_scores=sentiment_scores,
                reasoning=reasoning,
                metadata={
                    'model': self.config.model_name,
                    'tokens_used': response.usage.total_tokens,
                    'finish_reason': response.choices[0].finish_reason,
                    'data_points': len(data_summary)
                },
                provider=self.config.provider_name,
                model=self.config.model_name,
                timestamp=time.time()
            )
            
        except Exception as e:
            self.logger.error(f"OpenAI market insight generation failed: {e}")
            return self._create_error_response(str(e))
    
    def is_available(self) -> bool:
        """Check if OpenAI provider is available"""
        if not OPENAI_AVAILABLE:
            return False
        
        if not self.is_initialized:
            return False
        
        try:
            # Quick health check
            response = openai.ChatCompletion.create(
                model=self.config.model_name,
                messages=[{"role": "user", "content": "ping"}],
                max_tokens=5,
                temperature=0.1
            )
            return True
        except Exception as e:
            self.logger.warning(f"OpenAI availability check failed: {e}")
            return False


def create_openai_provider(api_key: str, model_name: str = "gpt-3.5-turbo") -> OpenAIProvider:
    """Factory function to create OpenAI provider
    
    Args:
        api_key: OpenAI API key
        model_name: Model name to use
        
    Returns:
        OpenAIProvider: Configured provider instance
    """
    config = LLMConfig(
        provider_name="openai",
        model_name=model_name,
        api_key=api_key,
        max_tokens=1500,
        temperature=0.3,
        timeout=30.0,
        retry_attempts=3
    )
    
    provider = OpenAIProvider(config)
    provider.initialize()
    return provider
