"""
Enhanced Reinforcement Learning Agent Implementation

This agent uses trained RL models to generate quantitative trading signals
based on technical indicators and market microstructure data with optimized
model loading, caching, and performance monitoring.
"""

import numpy as np
import logging
import pickle
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
import threading

from agents.base_agent import BaseAgent, TradingSignal, MarketData

# Try to import RL libraries
try:
    import torch
    import torch.nn as nn
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None
    nn = None


class EnhancedModelManager:
    """Enhanced model manager with caching, versioning, and performance monitoring"""

    def __init__(self, cache_size: int = 3):
        self.cache_size = cache_size
        self.model_cache = {}  # {model_path: (model, metadata, last_used)}
        self.cache_lock = threading.Lock()
        self.logger = logging.getLogger("ModelManager")

        # Performance tracking
        self.load_times = {}
        self.prediction_times = {}
        self.cache_hits = 0
        self.cache_misses = 0

    def load_model(self, model_path: str, force_reload: bool = False) -> Tuple[Any, Dict]:
        """Load model with caching and performance tracking"""
        with self.cache_lock:
            # Check cache first
            if not force_reload and model_path in self.model_cache:
                model, metadata, _ = self.model_cache[model_path]
                self.model_cache[model_path] = (model, metadata, time.time())
                self.cache_hits += 1
                self.logger.debug(f"Model loaded from cache: {model_path}")
                return model, metadata

            # Load from disk
            start_time = time.time()
            try:
                model, metadata = self._load_model_from_disk(model_path)
                load_time = time.time() - start_time

                # Update cache
                self._update_cache(model_path, model, metadata)

                # Track performance
                self.load_times[model_path] = load_time
                self.cache_misses += 1

                self.logger.info(f"Model loaded from disk in {load_time:.3f}s: {model_path}")
                return model, metadata

            except Exception as e:
                self.logger.error(f"Failed to load model {model_path}: {e}")
                raise

    def _load_model_from_disk(self, model_path: str) -> Tuple[Any, Dict]:
        """Load model from disk with format detection"""
        path = Path(model_path)

        if not path.exists():
            raise FileNotFoundError(f"Model file not found: {model_path}")

        # Detect model format
        if path.suffix == '.pt' or path.suffix == '.pth':
            return self._load_pytorch_model(path)
        elif path.suffix == '.pkl':
            return self._load_pickle_model(path)
        elif path.suffix == '.json':
            return self._load_json_model(path)
        else:
            # Try to detect format by content
            return self._load_auto_detect(path)

    def _load_pytorch_model(self, path: Path) -> Tuple[Any, Dict]:
        """Load PyTorch model"""
        if not TORCH_AVAILABLE:
            raise ImportError("PyTorch not available for model loading")

        try:
            # Load model state dict or full model
            checkpoint = torch.load(path, map_location='cpu')

            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                # Checkpoint format
                model_state = checkpoint['model_state_dict']
                metadata = checkpoint.get('metadata', {})

                # Create model architecture (simplified)
                model = self._create_simple_model(metadata.get('input_dim', 50),
                                                metadata.get('output_dim', 3))
                model.load_state_dict(model_state)
                model.eval()

                return model, metadata
            else:
                # Direct model format
                model = checkpoint
                if hasattr(model, 'eval'):
                    model.eval()

                metadata = {
                    'model_type': 'pytorch',
                    'input_dim': getattr(model, 'input_dim', 50),
                    'output_dim': getattr(model, 'output_dim', 3),
                    'loaded_at': time.time()
                }

                return model, metadata

        except Exception as e:
            raise RuntimeError(f"Failed to load PyTorch model: {e}")

    def _load_pickle_model(self, path: Path) -> Tuple[Any, Dict]:
        """Load pickled model"""
        try:
            with open(path, 'rb') as f:
                data = pickle.load(f)

            if isinstance(data, dict):
                model = data.get('model')
                metadata = data.get('metadata', {})
            else:
                model = data
                metadata = {'model_type': 'pickle', 'loaded_at': time.time()}

            return model, metadata

        except Exception as e:
            raise RuntimeError(f"Failed to load pickle model: {e}")

    def _load_json_model(self, path: Path) -> Tuple[Any, Dict]:
        """Load JSON model configuration"""
        try:
            with open(path, 'r') as f:
                data = json.load(f)

            # Create simple rule-based model from JSON config
            model = SimpleRuleModel(data)
            metadata = data.get('metadata', {})
            metadata['model_type'] = 'json_rules'

            return model, metadata

        except Exception as e:
            raise RuntimeError(f"Failed to load JSON model: {e}")

    def _load_auto_detect(self, path: Path) -> Tuple[Any, Dict]:
        """Auto-detect model format and load"""
        # Try different formats
        for loader in [self._load_pytorch_model, self._load_pickle_model, self._load_json_model]:
            try:
                return loader(path)
            except Exception:
                continue

        raise RuntimeError(f"Could not detect model format for: {path}")

    def _create_simple_model(self, input_dim: int, output_dim: int):
        """Create simple neural network model"""
        if not TORCH_AVAILABLE:
            return None

        class SimpleModel(nn.Module):
            def __init__(self, input_dim, output_dim):
                super().__init__()
                self.input_dim = input_dim
                self.output_dim = output_dim
                self.fc1 = nn.Linear(input_dim, 128)
                self.fc2 = nn.Linear(128, 64)
                self.fc3 = nn.Linear(64, output_dim)
                self.relu = nn.ReLU()
                self.softmax = nn.Softmax(dim=-1)

            def forward(self, x):
                x = self.relu(self.fc1(x))
                x = self.relu(self.fc2(x))
                x = self.softmax(self.fc3(x))
                return x

        return SimpleModel(input_dim, output_dim)

    def _update_cache(self, model_path: str, model: Any, metadata: Dict):
        """Update model cache with LRU eviction"""
        current_time = time.time()

        # Add to cache
        self.model_cache[model_path] = (model, metadata, current_time)

        # Evict old entries if cache is full
        if len(self.model_cache) > self.cache_size:
            # Find least recently used
            lru_path = min(self.model_cache.keys(),
                          key=lambda k: self.model_cache[k][2])
            del self.model_cache[lru_path]
            self.logger.debug(f"Evicted model from cache: {lru_path}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / max(total_requests, 1)

        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate,
            'cached_models': len(self.model_cache),
            'avg_load_time': np.mean(list(self.load_times.values())) if self.load_times else 0
        }


class SimpleRuleModel:
    """Simple rule-based model for fallback when no trained model is available"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.rules = config.get('rules', {})
        self.thresholds = config.get('thresholds', {
            'buy_threshold': 0.7,
            'sell_threshold': 0.3,
            'confidence_base': 0.6
        })

    def predict(self, state_vector: np.ndarray) -> np.ndarray:
        """Predict action probabilities based on rules"""
        # Extract key features from state vector
        # Assuming first 20 are technical indicators
        tech_features = state_vector[:20] if len(state_vector) >= 20 else state_vector

        # Simple rule-based logic
        momentum = np.mean(tech_features[:5])  # First 5 features as momentum
        volatility = np.std(tech_features[5:10])  # Next 5 as volatility
        trend = np.mean(tech_features[10:15])  # Next 5 as trend

        # Calculate action probabilities
        if momentum > self.thresholds['buy_threshold'] and trend > 0:
            # Bullish signal
            probs = [0.7, 0.2, 0.1]  # [buy, hold, sell]
        elif momentum < self.thresholds['sell_threshold'] and trend < 0:
            # Bearish signal
            probs = [0.1, 0.2, 0.7]  # [buy, hold, sell]
        else:
            # Neutral signal
            probs = [0.2, 0.6, 0.2]  # [buy, hold, sell]

        return np.array(probs)

    def __call__(self, state_vector: np.ndarray) -> np.ndarray:
        """Make model callable"""
        return self.predict(state_vector)


class RLAgent(BaseAgent):
    """
    Enhanced Reinforcement Learning trading agent with optimized model management.

    Uses trained RL models (PPO/SAC/A3C) to analyze technical indicators
    and generate trading signals with improved loading, caching, and performance.
    """

    def __init__(self, name: str = "RLAgent", model_path: Optional[str] = None,
                 enable_caching: bool = True, cache_size: int = 3):
        """
        Initialize the Enhanced RL Agent.

        Args:
            name: Agent name
            model_path: Path to trained RL model
            enable_caching: Enable model caching
            cache_size: Maximum number of models to cache
        """
        super().__init__(name)

        # Initialize model manager
        self.model_manager = EnhancedModelManager(cache_size) if enable_caching else None
        self.model_path = model_path
        self.model = None
        self.model_metadata = {}

        # Model configuration
        self.state_dim = 50  # 20 technical + 20 llm + 10 czsc
        self.action_dim = 3  # buy, hold, sell

        # Enhanced RL configuration
        self.rl_config = {
            'algorithm': 'PPO',  # PPO, SAC, or A3C
            'state_normalization': True,
            'risk_adjustment': True,
            'confidence_threshold': 0.6,
            'prediction_timeout': 5.0,  # seconds
            'enable_model_warmup': True,
            'warmup_samples': 10
        }
        
        # Feature extraction weights for technical indicators (20-dim)
        self.technical_feature_weights = {
            # Trend indicators (6-dim)
            'ma_signal': 0.15,           # Moving average signal
            'macd_signal': 0.12,         # MACD signal  
            'adx_strength': 0.10,        # ADX trend strength
            'trend_consistency': 0.08,   # Trend consistency
            'price_position': 0.07,      # Price position
            'breakout_signal': 0.06,     # Breakout signal
            
            # Momentum indicators (5-dim)
            'rsi_signal': 0.09,          # RSI signal
            'kdj_signal': 0.08,          # KDJ signal
            'momentum_strength': 0.07,   # Momentum strength
            'williams_r': 0.05,          # Williams %R
            'cci_signal': 0.04,          # CCI signal
            
            # Volatility indicators (4-dim)
            'bollinger_position': 0.06,  # Bollinger position
            'atr_ratio': 0.05,           # ATR relative value
            'volatility_signal': 0.04,   # Volatility signal
            'vix_like': 0.03,            # VIX-like indicator
            
            # Volume-price indicators (3-dim)
            'volume_strength': 0.05,     # Volume strength
            'obv_signal': 0.04,          # OBV signal
            'price_volume_trend': 0.03,  # Price-volume trend
            
            # Support/Resistance indicators (2-dim)
            'support_resistance': 0.03,  # Support/resistance position
            'pivot_signal': 0.02         # Pivot point signal
        }
        
        self.current_confidence = 0.5
        
        # Performance tracking
        self.prediction_count = 0
        self.prediction_times = []
        self.model_warmup_done = False

        # Initialize current technical indicators storage
        self.technical_indicators = {}

    def initialize(self) -> bool:
        """Initialize the RL model and required resources with enhanced loading."""
        try:
            if self.model_path and Path(self.model_path).exists():
                # Use enhanced model manager if available
                if self.model_manager:
                    self.model, self.model_metadata = self.model_manager.load_model(self.model_path)
                else:
                    self.model = self._load_model_legacy(self.model_path)
                    self.model_metadata = {'model_type': 'legacy'}

                self.logger.info(f"RL model loaded: {self.model_path}")

                # Perform model warmup if enabled
                if self.rl_config.get('enable_model_warmup', True):
                    self._warmup_model()

            else:
                self.logger.warning("No model path provided or model not found, using rule-based fallback")
                self.model = SimpleRuleModel({'rules': {}, 'thresholds': {}})
                self.model_metadata = {'model_type': 'rule_based'}

            self.is_initialized = True
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize RL agent: {e}")
            return False

    def _warmup_model(self):
        """Warm up the model with dummy predictions to optimize performance"""
        if not self.model:
            return

        try:
            warmup_samples = self.rl_config.get('warmup_samples', 10)
            dummy_state = np.random.randn(self.state_dim)

            start_time = time.time()
            for _ in range(warmup_samples):
                if hasattr(self.model, 'predict'):
                    self.model.predict(dummy_state)
                elif callable(self.model):
                    self.model(dummy_state)

            warmup_time = time.time() - start_time
            self.model_warmup_done = True

            self.logger.info(f"Model warmup completed in {warmup_time:.3f}s ({warmup_samples} samples)")

        except Exception as e:
            self.logger.warning(f"Model warmup failed: {e}")

    def _load_model_legacy(self, model_path: str):
        """Legacy model loading method for backward compatibility"""
        try:
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            return model
        except Exception as e:
            self.logger.warning(f"Legacy model loading failed: {e}")
            return None
    
    def generate_signal(self, data: MarketData) -> TradingSignal:
        """
        Generate trading signal using RL model or rule-based fallback.
        
        Args:
            data: Market data including technical indicators
            
        Returns:
            TradingSignal with RL-based decision
        """
        try:
            # Extract state vector (focus on technical indicators for RL)
            state_vector = self._create_state_vector(data)
            
            if self.model is not None:
                # Use trained RL model
                action_probs = self._predict_with_model(state_vector)
                action, confidence = self._process_model_output(action_probs)
                reasoning = f"RL model prediction: {action} (confidence: {confidence:.3f})"
            else:
                # Fallback to rule-based logic
                action, confidence = self._rule_based_decision(data)
                reasoning = f"Rule-based RL decision: {action} (no trained model available)"
            
            # Update current confidence
            self.current_confidence = confidence
            
            signal = TradingSignal(
                agent_type=self.agent_type,
                action=action,
                strength=confidence,
                confidence=confidence,
                timestamp=datetime.now(),
                reasoning=reasoning,
                metadata={
                    'model_used': self.model is not None,
                    'state_vector_norm': float(np.linalg.norm(state_vector)),
                    'technical_features': self._extract_technical_features(data)
                }
            )
            
            self.logger.debug(f"Generated RL signal: {action} with confidence {confidence:.3f}")
            return signal
            
        except Exception as e:
            self.logger.error(f"Error generating RL signal: {e}")
            # Return conservative signal on error
            return TradingSignal(
                agent_type=self.agent_type,
                action="hold",
                strength=0.1,
                confidence=0.1,
                timestamp=datetime.now(),
                reasoning=f"Error in RL signal generation: {str(e)}",
                metadata={'error': str(e)}
            )
    
    def get_confidence(self) -> float:
        """Get current confidence level."""
        return self.current_confidence
    
    def get_explanation(self) -> str:
        """Get human-readable explanation of current RL analysis state"""
        if not self.model:
            return "RL Agent: 使用基于规则的回退逻辑进行决策（未加载训练模型）。"
        
        return f"RL Agent: 使用训练好的强化学习模型进行量化分析。当前置信度: {self.current_confidence:.2f}。"
    
    def _create_state_vector(self, data: MarketData) -> np.ndarray:
        """
        Create 50-dimensional state vector from market data.
        For RL agent, we focus on the technical indicators portion (20-dim).
        """
        # Extract technical features (20-dim)
        technical_features = self._extract_technical_features(data)
        
        # For RL agent, we'll use zeros for LLM and CZSC features initially
        # These will be filled by the coordination manager
        llm_features = np.zeros(20)
        czsc_features = np.zeros(10)
        
        # Combine into 50-dimensional state vector
        state_vector = np.concatenate([technical_features, llm_features, czsc_features])
        
        # Normalize if configured
        if self.rl_config['state_normalization']:
            state_vector = self._normalize_state(state_vector)
        
        return state_vector.astype(np.float32)
    
    def _extract_technical_features(self, data: MarketData) -> np.ndarray:
        """Extract 20-dimensional technical indicator features."""
        features = np.zeros(20)
        
        indicators = data.technical_indicators
        
        try:
            # Trend indicators (6-dim)
            features[0] = indicators.get('ma_signal', 0.0)
            features[1] = indicators.get('macd_signal', 0.0)
            features[2] = indicators.get('adx_strength', 0.0)
            features[3] = indicators.get('trend_consistency', 0.0)
            features[4] = indicators.get('price_position', 0.0)
            features[5] = indicators.get('breakout_signal', 0.0)
            
            # Momentum indicators (5-dim)
            features[6] = indicators.get('rsi_signal', 0.0)
            features[7] = indicators.get('kdj_signal', 0.0)
            features[8] = indicators.get('momentum_strength', 0.0)
            features[9] = indicators.get('williams_r', 0.0)
            features[10] = indicators.get('cci_signal', 0.0)
            
            # Volatility indicators (4-dim)
            features[11] = indicators.get('bollinger_position', 0.0)
            features[12] = indicators.get('atr_ratio', 0.0)
            features[13] = indicators.get('volatility_signal', 0.0)
            features[14] = indicators.get('vix_like', 0.0)
            
            # Volume-price indicators (3-dim)
            features[15] = indicators.get('volume_strength', 0.0)
            features[16] = indicators.get('obv_signal', 0.0)
            features[17] = indicators.get('price_volume_trend', 0.0)
            
            # Support/Resistance indicators (2-dim)
            features[18] = indicators.get('support_resistance', 0.0)
            features[19] = indicators.get('pivot_signal', 0.0)
            
        except Exception as e:
            self.logger.warning(f"Error extracting technical features: {e}")
            
        return features
    
    def _normalize_state(self, state_vector: np.ndarray) -> np.ndarray:
        """Normalize state vector to [-1, 1] range."""
        # Simple normalization - can be enhanced with learned statistics
        return np.tanh(state_vector)
    
    def _predict_with_model(self, state_vector: np.ndarray) -> np.ndarray:
        """Predict action probabilities using trained model with performance tracking."""
        if not self.model:
            return np.array([0.33, 0.34, 0.33])  # Neutral prediction

        start_time = time.time()
        try:
            # Normalize state if enabled
            if self.rl_config.get('state_normalization', True):
                state_vector = self._normalize_state(state_vector)

            # Make prediction based on model type
            if hasattr(self.model, 'predict'):
                # Standard predict method
                action_probs = self.model.predict(state_vector)
            elif callable(self.model):
                # Callable model
                action_probs = self.model(state_vector)
            elif TORCH_AVAILABLE and isinstance(self.model, torch.nn.Module):
                # PyTorch model
                with torch.no_grad():
                    state_tensor = torch.FloatTensor(state_vector).unsqueeze(0)
                    action_probs = self.model(state_tensor).squeeze().numpy()
            else:
                # Fallback to rule-based
                self.logger.warning("Unknown model type, using rule-based prediction")
                action_probs = np.array([0.33, 0.34, 0.33])

            # Ensure valid probabilities
            action_probs = np.clip(action_probs, 0.01, 0.99)
            action_probs = action_probs / np.sum(action_probs)  # Normalize

            # Track prediction time
            prediction_time = time.time() - start_time
            self.prediction_times.append(prediction_time)
            self.prediction_count += 1

            # Keep only recent prediction times
            if len(self.prediction_times) > 100:
                self.prediction_times = self.prediction_times[-50:]

            return action_probs

        except Exception as e:
            self.logger.error(f"Model prediction failed: {e}")
            return np.array([0.33, 0.34, 0.33])  # Safe fallback
    
    def _process_model_output(self, action_probs: np.ndarray) -> tuple[str, float]:
        """Process model output into action and confidence."""
        action_names = ['buy', 'hold', 'sell']
        
        # Get action with highest probability
        action_idx = np.argmax(action_probs)
        action = action_names[action_idx]
        
        # Confidence is the max probability
        confidence = float(action_probs[action_idx])
        
        # Apply confidence threshold
        if confidence < self.rl_config['confidence_threshold']:
            action = 'hold'
            confidence = max(0.1, confidence * 0.5)
        
        return action, confidence
    
    def _rule_based_decision(self, data: MarketData) -> tuple[str, float]:
        """Fallback rule-based decision when no model is available."""
        indicators = data.technical_indicators
        
        # Simple rule-based logic
        buy_signals = 0
        sell_signals = 0
        
        # Check trend indicators
        if indicators.get('ma_signal', 0) > 0.1:
            buy_signals += 1
        elif indicators.get('ma_signal', 0) < -0.1:
            sell_signals += 1
            
        # Check momentum
        if indicators.get('rsi_signal', 0) > 0.1:
            buy_signals += 1
        elif indicators.get('rsi_signal', 0) < -0.1:
            sell_signals += 1
        
        # Make decision
        if buy_signals > sell_signals and buy_signals >= 2:
            return 'buy', min(0.8, 0.4 + buy_signals * 0.1)
        elif sell_signals > buy_signals and sell_signals >= 2:
            return 'sell', min(0.8, 0.4 + sell_signals * 0.1)
        else:
            return 'hold', 0.3
    
    def _load_model(self, model_path: str):
        """Load trained RL model."""
        # Placeholder for model loading logic
        # In real implementation, this would load PyTorch/TensorFlow model
        self.logger.info(f"Mock loading RL model from {model_path}")
        return "mock_model" 

    def update_state(self, market_data: MarketData):
        """Update agent internal state with new market data"""
        # Update technical indicators if available
        if hasattr(market_data, 'technical_indicators') and market_data.technical_indicators:
            self.technical_indicators.update(market_data.technical_indicators)
    
    def get_confidence(self) -> float:
        """Get current confidence level"""
        return min(0.9, self.current_confidence)

    def get_explanation(self) -> str:
        """Get explanation of current RL approach"""
        model_type = self.model_metadata.get('model_type', 'unknown')
        algorithm = self.rl_config.get('algorithm', 'PPO')

        explanation = f"RL Agent using {algorithm} algorithm with {model_type} model"

        if self.model_warmup_done:
            explanation += " (warmed up)"

        if self.prediction_count > 0:
            avg_prediction_time = np.mean(self.prediction_times) if self.prediction_times else 0
            explanation += f", avg prediction time: {avg_prediction_time:.3f}s"

        return explanation

    def get_features(self) -> Dict[str, Any]:
        """Get current agent features and state"""
        return {
            'model_loaded': self.model is not None,
            'initialized': self.is_initialized,
            'current_confidence': self.current_confidence,
            'technical_indicators': list(self.technical_indicators.keys()),
            'model_type': self.model_metadata.get('model_type', 'unknown'),
            'prediction_count': self.prediction_count,
            'model_warmup_done': self.model_warmup_done
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get detailed performance statistics"""
        stats = {
            'prediction_count': self.prediction_count,
            'model_type': self.model_metadata.get('model_type', 'unknown'),
            'model_warmup_done': self.model_warmup_done,
            'current_confidence': self.current_confidence,
            'algorithm': self.rl_config.get('algorithm', 'PPO')
        }

        # Prediction timing stats
        if self.prediction_times:
            stats.update({
                'avg_prediction_time': np.mean(self.prediction_times),
                'min_prediction_time': np.min(self.prediction_times),
                'max_prediction_time': np.max(self.prediction_times),
                'prediction_time_std': np.std(self.prediction_times)
            })

        # Model manager stats
        if self.model_manager:
            stats['cache_stats'] = self.model_manager.get_cache_stats()

        return stats