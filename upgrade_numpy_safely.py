#!/usr/bin/env python3
"""
安全升级NumPy到1.21+版本的脚本
解决stable-baselines3和gymnasium的兼容性问题
"""

import subprocess
import sys
import os

def run_command(command, ignore_errors=False):
    """执行命令并处理错误"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout.strip():
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        if not ignore_errors:
            print(f"错误: {e}")
            if e.stderr:
                print(f"错误详情: {e.stderr}")
        return False

def check_current_versions():
    """检查当前版本"""
    print("=== 当前环境检查 ===")
    
    packages_to_check = [
        'numpy', 'pandas', 'stable_baselines3', 'gymnasium', 'gym', 
        'opencv-python', 'torch', 'scipy', 'scikit-learn'
    ]
    
    for package in packages_to_check:
        try:
            module = __import__(package.replace('-', '_'))
            if hasattr(module, '__version__'):
                print(f"{package}: {module.__version__}")
            else:
                print(f"{package}: 已安装")
        except ImportError:
            print(f"{package}: 未安装")

def backup_environment():
    """备份当前环境"""
    print("\n=== 备份当前环境 ===")
    
    # 导出当前包列表
    run_command(f"{sys.executable} -m pip freeze > requirements_backup.txt")
    print("已备份当前环境到 requirements_backup.txt")
    
    # 显示关键包版本
    print("\n关键包当前版本:")
    key_packages = ['numpy', 'pandas', 'stable-baselines3', 'gymnasium']
    for package in key_packages:
        run_command(f"{sys.executable} -m pip show {package}", ignore_errors=True)

def upgrade_numpy_strategy():
    """升级NumPy的策略"""
    print("\n=== NumPy升级策略 ===")
    
    # 策略1: 直接升级到兼容版本
    print("\n策略1: 升级到NumPy 1.24.x (推荐)")
    numpy_version = "1.24.4"  # 稳定且兼容的版本
    
    commands_strategy1 = [
        f"{sys.executable} -m pip install --upgrade numpy=={numpy_version}",
        f"{sys.executable} -m pip install --force-reinstall --no-deps numpy=={numpy_version}",
    ]
    
    for cmd in commands_strategy1:
        success = run_command(cmd, ignore_errors=True)
        if success:
            break
    
    # 重新安装可能受影响的包
    print("\n重新安装相关包...")
    affected_packages = [
        "pandas",
        "scipy", 
        "scikit-learn",
        "opencv-python",
        "stable-baselines3",
        "gymnasium"
    ]
    
    for package in affected_packages:
        print(f"\n重新安装 {package}...")
        run_command(f"{sys.executable} -m pip install --force-reinstall {package}", ignore_errors=True)

def verify_upgrade():
    """验证升级结果"""
    print("\n=== 验证升级结果 ===")
    
    # 检查NumPy版本
    try:
        import numpy
        print(f"✓ NumPy版本: {numpy.__version__}")
        
        # 检查是否有NDArray
        try:
            from numpy.typing import NDArray
            print("✓ numpy.typing.NDArray 可用")
        except ImportError:
            print("✗ numpy.typing.NDArray 不可用")
            
    except ImportError:
        print("✗ NumPy导入失败")
        return False
    
    # 测试关键包
    test_imports = [
        ("pandas", "import pandas; print(f'Pandas: {pandas.__version__}')"),
        ("stable_baselines3", "import stable_baselines3; print(f'SB3: {stable_baselines3.__version__}')"),
        ("gymnasium", "import gymnasium; print(f'Gymnasium: {gymnasium.__version__}')"),
        ("opencv", "import cv2; print(f'OpenCV: {cv2.__version__}')"),
        ("torch", "import torch; print(f'PyTorch: {torch.__version__}')")
    ]
    
    success_count = 0
    for name, import_code in test_imports:
        try:
            exec(import_code)
            print(f"✓ {name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {name} 导入失败: {e}")
    
    print(f"\n成功导入: {success_count}/{len(test_imports)}")
    return success_count == len(test_imports)

def test_stable_baselines3():
    """测试stable-baselines3是否正常工作"""
    print("\n=== 测试Stable-Baselines3 ===")
    
    test_code = """
import numpy as np
import gymnasium as gym
from stable_baselines3 import SAC
from stable_baselines3.common.env_checker import check_env

# 创建简单环境
env = gym.make('CartPole-v1')
print("✓ 环境创建成功")

# 检查环境
check_env(env)
print("✓ 环境检查通过")

# 创建模型
model = SAC('MlpPolicy', env, verbose=0)
print("✓ SAC模型创建成功")

print("🎉 Stable-Baselines3测试通过!")
"""
    
    try:
        exec(test_code)
        return True
    except Exception as e:
        print(f"✗ Stable-Baselines3测试失败: {e}")
        return False

def rollback_if_needed():
    """如果升级失败，回滚到原始状态"""
    print("\n=== 回滚选项 ===")
    print("如果升级后出现问题，可以使用以下命令回滚:")
    print(f"{sys.executable} -m pip install -r requirements_backup.txt --force-reinstall")
    print("\n或者手动安装原始版本:")
    print(f"{sys.executable} -m pip install numpy==1.20.3 --force-reinstall")

def main():
    """主函数"""
    print("NumPy安全升级工具")
    print("=" * 50)
    
    # 检查当前状态
    check_current_versions()
    
    # 备份环境
    backup_environment()
    
    # 询问用户是否继续
    print("\n" + "=" * 50)
    response = input("是否继续升级NumPy? (y/N): ").lower().strip()
    
    if response != 'y':
        print("升级已取消")
        return
    
    # 执行升级
    upgrade_numpy_strategy()
    
    # 验证升级
    success = verify_upgrade()
    
    if success:
        # 测试SB3
        sb3_success = test_stable_baselines3()
        
        if sb3_success:
            print("\n" + "=" * 50)
            print("🎉 升级成功！现在可以运行:")
            print("python enhanced_rl_stock_base_sb3.py")
        else:
            print("\n⚠️ NumPy升级成功，但SB3测试失败")
            rollback_if_needed()
    else:
        print("\n❌ 升级失败")
        rollback_if_needed()

if __name__ == "__main__":
    main()