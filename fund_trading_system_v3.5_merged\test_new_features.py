"""
第三批任务新功能测试脚本

测试新增的在线学习、A/B测试、高级评估和风险监控功能
"""

import sys
import os
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_online_learning():
    """测试在线学习功能"""
    print("=== 测试在线学习功能 ===")
    
    try:
        from rl_infrastructure.online_learning_manager import OnlineLearningManager, OnlineLearningConfig
        
        # 创建配置
        config = OnlineLearningConfig(
            batch_size=16,
            buffer_size=1000,
            update_frequency=50
        )
        
        # 创建管理器
        manager = OnlineLearningManager(config)
        print("✅ 在线学习管理器创建成功")
        
        # 测试添加数据
        state = np.random.rand(10)
        action = np.random.rand(2)
        reward = 0.5
        next_state = np.random.rand(10)
        done = False
        
        manager.add_training_data(state, action, reward, next_state, done)
        print("✅ 训练数据添加成功")
        
        # 获取状态
        status = manager.get_learning_status()
        print(f"✅ 学习状态: 样本数={status['samples_processed']}, 缓冲区大小={status['buffer_size']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 在线学习测试失败: {str(e)}")
        return False


def test_ab_testing():
    """测试A/B测试功能"""
    print("\n=== 测试A/B测试功能 ===")
    
    try:
        from rl_infrastructure.ab_testing_framework import ABTestingFramework, ABTestConfig, ModelVariant
        
        # 创建变体
        variants = [
            ModelVariant(
                variant_id="model_a",
                model_name="Model A",
                model_path="/path/to/model_a",
                description="基础模型"
            ),
            ModelVariant(
                variant_id="model_b",
                model_name="Model B", 
                model_path="/path/to/model_b",
                description="改进模型"
            )
        ]
        
        # 创建配置
        config = ABTestConfig(
            test_name="RL_Model_Test",
            variants=variants,
            min_sample_size=10
        )
        
        # 创建框架
        framework = ABTestingFramework(config)
        print("✅ A/B测试框架创建成功")
        
        # 开始测试
        framework.start_test()
        print("✅ A/B测试开始成功")
        
        # 分配变体并记录结果
        for i in range(20):
            variant = framework.allocate_variant(f"request_{i}")
            metrics = {
                'accuracy': np.random.uniform(0.7, 0.9),
                'precision': np.random.uniform(0.6, 0.8)
            }
            framework.record_result(variant, metrics)
        
        print("✅ 变体分配和结果记录成功")
        
        # 获取结果
        results = framework.get_current_results()
        print(f"✅ 测试结果: {len(results)}个变体有数据")
        
        return True
        
    except Exception as e:
        print(f"❌ A/B测试失败: {str(e)}")
        return False


def test_advanced_evaluator():
    """测试高级评估器"""
    print("\n=== 测试高级评估器 ===")
    
    try:
        from rl_infrastructure.advanced_evaluator import AdvancedEvaluator
        
        # 创建评估器
        evaluator = AdvancedEvaluator(risk_free_rate=0.02)
        print("✅ 高级评估器创建成功")
        
        # 生成测试数据
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 252)  # 一年的日收益率
        prices = np.cumprod(1 + returns) * 100
        
        # 生成测试交易
        trades = []
        for i in range(10):
            trades.append({
                'return': np.random.normal(0.02, 0.05),
                'pnl': np.random.normal(200, 500)
            })
        
        # 执行评估
        result = evaluator.evaluate_performance(
            returns=returns.tolist(),
            prices=prices.tolist(),
            trades=trades
        )
        
        print("✅ 性能评估完成")
        print(f"  - 总收益率: {result.performance_metrics.get('total_return', 0):.2%}")
        print(f"  - 夏普比率: {result.performance_metrics.get('sharpe_ratio', 0):.3f}")
        print(f"  - 最大回撤: {result.risk_metrics.get('max_drawdown', 0):.2%}")
        
        # 测试滚动指标
        rolling_metrics = evaluator.calculate_rolling_metrics(returns.tolist(), window=60)
        print(f"✅ 滚动指标计算完成，数据点数: {len(rolling_metrics['rolling_return'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级评估器测试失败: {str(e)}")
        return False


def test_report_generator():
    """测试报告生成器"""
    print("\n=== 测试报告生成器 ===")
    
    try:
        from rl_infrastructure.evaluation_report_generator import EvaluationReportGenerator, ReportConfig
        from rl_infrastructure.advanced_evaluator import EvaluationResult
        
        # 创建配置
        config = ReportConfig(
            title="测试报告",
            include_charts=False,  # 避免matplotlib依赖
            output_formats=['json', 'markdown']
        )
        
        # 创建生成器
        generator = EvaluationReportGenerator(config)
        print("✅ 报告生成器创建成功")
        
        # 创建测试评估结果
        evaluation_result = EvaluationResult(
            timestamp=datetime.now().isoformat(),
            total_samples=1000,
            performance_metrics={
                'total_return': 0.15,
                'annualized_return': 0.12,
                'sharpe_ratio': 1.2,
                'win_rate': 0.65
            },
            risk_metrics={
                'volatility': 0.18,
                'max_drawdown': -0.08,
                'var_95': -0.025
            },
            trade_statistics={
                'total_trades': 50,
                'winning_trades': 32,
                'average_trade_return': 0.003
            },
            period_analysis={}
        )
        
        # 生成报告
        import tempfile
        temp_dir = tempfile.mkdtemp()
        
        generated_files = generator.generate_report(
            evaluation_result,
            temp_dir,
            "test_report"
        )
        
        print(f"✅ 报告生成成功，生成文件: {list(generated_files.keys())}")
        
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成器测试失败: {str(e)}")
        return False


def test_risk_monitor():
    """测试风险监控系统"""
    print("\n=== 测试风险监控系统 ===")
    
    try:
        from risk_management.portfolio_risk_analyzer import AdvancedRiskMonitor, RiskAlert
        
        # 创建监控器
        monitor = AdvancedRiskMonitor()
        print("✅ 风险监控器创建成功")
        
        # 创建测试告警
        alert = RiskAlert(
            alert_type='TEST_ALERT',
            severity='medium',
            message='这是一个测试告警'
        )
        
        print("✅ 风险告警创建成功")
        
        # 添加到监控器
        monitor.active_alerts.append(alert)
        
        # 获取告警摘要
        summary = monitor.get_alert_summary()
        print(f"✅ 告警摘要: 活跃告警数={summary['total_active_alerts']}")
        
        # 确认告警
        success = monitor.acknowledge_alert(0)
        print(f"✅ 告警确认: {'成功' if success else '失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险监控测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始第三批任务功能测试")
    print("=" * 50)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("在线学习", test_online_learning()))
    test_results.append(("A/B测试", test_ab_testing()))
    test_results.append(("高级评估", test_advanced_evaluator()))
    test_results.append(("报告生成", test_report_generator()))
    test_results.append(("风险监控", test_risk_monitor()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！第三批任务功能实现成功！")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
