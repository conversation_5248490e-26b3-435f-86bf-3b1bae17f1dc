#!/usr/bin/env python3
"""
独立的强化学习股票交易环境
不依赖任何外部金融库，使用模拟数据
"""

import argparse
import os
import random
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# 尝试导入gym
try:
    import gymnasium as gym
    from gymnasium import spaces
    GYM_VERSION = "gymnasium"
    print("使用 gymnasium")
except ImportError:
    try:
        import gym
        from gym import spaces
        GYM_VERSION = "gym"
        print("使用 gym")
    except ImportError:
        print("警告: 未安装 gym 或 gymnasium，将使用基础实现")
        GYM_VERSION = "none"

# =================== 配置参数 ===================
MAX_ACCOUNT_BALANCE = 2147480
MAX_NUM_SHARES = 2147480
MAX_SHARE_PRICE = 5000
MAX_VOLUME = 1e9
MAX_AMOUNT = 1e10
INITIAL_ACCOUNT_BALANCE = 100000
SEED = 42

# =================== 基础空间类 ===================
if GYM_VERSION == "none":
    class Box:
        def __init__(self, low, high, shape=None, dtype=np.float32):
            self.low = np.array(low) if not isinstance(low, np.ndarray) else low
            self.high = np.array(high) if not isinstance(high, np.ndarray) else high
            self.shape = shape if shape is not None else self.low.shape
            self.dtype = dtype
            
        def sample(self):
            return np.random.uniform(self.low, self.high).astype(self.dtype)
            
        def contains(self, x):
            return np.all(x >= self.low) and np.all(x <= self.high)
    
    spaces = type('spaces', (), {'Box': Box})()

# =================== 状态空间管理器 ===================
class StateSpaceManager:
    """简化的状态空间管理器 - 20维基础指标"""
    
    def __init__(self):
        self.state_dim = 20
        self.feature_names = self._define_feature_names()
        
    def _define_feature_names(self) -> List[str]:
        """定义20维特征名称"""
        return [
            # 基础价格特征 (6维)
            'open_norm', 'high_norm', 'low_norm', 'close_norm', 'volume_norm', 'amount_norm',
            # 技术指标 (8维)
            'sma_5', 'sma_20', 'rsi', 'macd', 'bb_upper', 'bb_lower', 'atr', 'momentum',
            # 账户状态 (6维)
            'balance_norm', 'net_worth_norm', 'shares_held_norm', 'cost_basis_norm', 
            'unrealized_pnl', 'profit_rate'
        ]
    
    def extract_features(self, df: pd.DataFrame, current_step: int, account_state: Dict) -> np.ndarray:
        """提取20维特征向量"""
        features = np.zeros(self.state_dim)
        
        try:
            # 基础价格特征 (6维)
            features[0] = df.loc[current_step, 'open'] / MAX_SHARE_PRICE
            features[1] = df.loc[current_step, 'high'] / MAX_SHARE_PRICE  
            features[2] = df.loc[current_step, 'low'] / MAX_SHARE_PRICE
            features[3] = df.loc[current_step, 'close'] / MAX_SHARE_PRICE
            features[4] = df.loc[current_step, 'volume'] / MAX_VOLUME
            features[5] = df.loc[current_step, 'amount'] / MAX_AMOUNT
            
            # 技术指标 (8维)
            features[6:14] = self._calculate_technical_indicators(df, current_step)
            
            # 账户状态 (6维)
            features[14] = account_state['balance'] / MAX_ACCOUNT_BALANCE
            features[15] = account_state['net_worth'] / MAX_ACCOUNT_BALANCE
            features[16] = account_state['shares_held'] / MAX_NUM_SHARES
            features[17] = account_state['cost_basis'] / MAX_SHARE_PRICE
            
            # 未实现盈亏
            if account_state['shares_held'] > 0:
                current_price = df.loc[current_step, 'close']
                unrealized_pnl = (current_price - account_state['cost_basis']) * account_state['shares_held']
                features[18] = (unrealized_pnl / INITIAL_ACCOUNT_BALANCE) + 0.5
            else:
                features[18] = 0.5
                
            # 收益率
            profit = account_state['net_worth'] - INITIAL_ACCOUNT_BALANCE
            features[19] = (profit / INITIAL_ACCOUNT_BALANCE) + 0.5
            
        except (KeyError, IndexError) as e:
            print(f"Warning: Feature extraction error: {e}")
            features = np.random.random(self.state_dim) * 0.1
            
        return np.clip(features, 0, 1).astype(np.float32)
 
    def _calculate_technical_indicators(self, df: pd.DataFrame, current_step: int) -> np.ndarray:
        """计算技术指标 (8维)"""
        indicators = np.zeros(8)
        
        if current_step < 20:  # 不足计算周期时使用默认值
            return indicators + 0.5
            
        try:
            close_prices = df['close'].iloc[max(0, current_step-20):current_step+1].values
            high_prices = df['high'].iloc[max(0, current_step-20):current_step+1].values
            low_prices = df['low'].iloc[max(0, current_step-20):current_step+1].values
            
            # SMA指标
            if len(close_prices) >= 5:
                sma_5 = np.mean(close_prices[-5:])
                indicators[0] = sma_5 / close_prices[-1]
                
            if len(close_prices) >= 20:
                sma_20 = np.mean(close_prices[-20:])
                indicators[1] = sma_20 / close_prices[-1]
                
            # RSI
            if len(close_prices) >= 14:
                rsi = self._calculate_rsi(close_prices, 14)
                indicators[2] = rsi / 100
                
            # MACD (简化)
            if len(close_prices) >= 12:
                ema_12 = self._calculate_ema(close_prices, 12)
                ema_26 = self._calculate_ema(close_prices, 26) if len(close_prices) >= 26 else ema_12
                macd = (ema_12 - ema_26) / close_prices[-1]
                indicators[3] = np.clip(macd + 0.5, 0, 1)
                
            # 布林带
            if len(close_prices) >= 20:
                bb_std = np.std(close_prices[-20:])
                bb_middle = np.mean(close_prices[-20:])
                indicators[4] = min((bb_middle + 2*bb_std) / close_prices[-1], 2.0) - 1.0
                indicators[5] = max((bb_middle - 2*bb_std) / close_prices[-1], 0.0)
                
            # ATR
            if len(high_prices) >= 14:
                atr = self._calculate_atr(high_prices, low_prices, close_prices, 14)
                indicators[6] = min(atr / close_prices[-1], 1.0)
                
            # 动量
            if len(close_prices) >= 10:
                momentum = (close_prices[-1] / close_prices[-10] - 1) + 0.5
                indicators[7] = np.clip(momentum, 0, 1)
                
        except Exception as e:
            print(f"Technical indicator calculation error: {e}")
            indicators = np.ones(8) * 0.5
            
        return np.clip(indicators, 0, 1)
    
    def _calculate_ema(self, prices: np.ndarray, period: int) -> float:
        """计算EMA"""
        if len(prices) < period:
            return prices[-1]
        alpha = 2.0 / (period + 1)
        ema = prices[0]
        for price in prices[1:]:
            ema = alpha * price + (1 - alpha) * ema
        return ema
        
    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0
            
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
        
    def _calculate_atr(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> float:
        """计算ATR"""
        if len(high) < period + 1:
            return (high[-1] - low[-1])
            
        tr_list = []
        for i in range(1, len(high)):
            tr = max(high[i] - low[i], 
                    abs(high[i] - close[i-1]),
                    abs(low[i] - close[i-1]))
            tr_list.append(tr)
            
        return np.mean(tr_list[-period:])

# =================== 动作空间 ===================
class ActionSpace:
    """简化的动作空间"""
    
    def __init__(self):
        # 动作：[交易信号, 仓位比例]
        self.action_space = spaces.Box(
            low=np.array([-1.0, 0.0]), 
            high=np.array([1.0, 1.0]), 
            dtype=np.float32
        )
        self.action_dim = 2
        
    def parse_action(self, raw_action: np.ndarray) -> Dict[str, float]:
        """解析动作"""
        action = np.clip(raw_action, self.action_space.low, self.action_space.high)
        
        # 交易信号
        signal_raw = action[0]
        if signal_raw < -0.33:
            trade_signal = 'sell'
        elif signal_raw > 0.33:
            trade_signal = 'buy'
        else:
            trade_signal = 'hold'
            
        return {
            'trade_signal': trade_signal,
            'position_ratio': float(action[1]),
            'signal_strength': abs(float(action[0]))
        }

# =================== 交易环境 ===================
class StockTradingEnv:
    """简化的股票交易环境"""
    
    def __init__(self, df: pd.DataFrame):
        self.df = df
        self.current_step = 0
        
        # 初始化组件
        self.state_manager = StateSpaceManager()
        self.action_parser = ActionSpace()
        
        # 环境空间定义
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(self.state_manager.state_dim,), dtype=np.float32
        )
        self.action_space = self.action_parser.action_space
        
        # 重置环境
        self.reset()
        
    def reset(self, seed=None):
        """重置环境"""
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)
        
        # 重置账户状态
        self.balance = INITIAL_ACCOUNT_BALANCE
        self.net_worth = INITIAL_ACCOUNT_BALANCE
        self.max_net_worth = INITIAL_ACCOUNT_BALANCE
        self.shares_held = 0
        self.cost_basis = 0
        self.total_shares_sold = 0
        self.total_sales_value = 0
        self.current_step = 0
        
        observation = self._get_observation()
        info = self._get_info()
        
        # 兼容不同版本
        if GYM_VERSION == "gymnasium":
            return observation, info
        else:
            return observation
        
    def step(self, action: np.ndarray):
        """执行动作"""
        # 解析动作
        parsed_action = self.action_parser.parse_action(action)
        
        # 执行交易
        reward = self._execute_trade(parsed_action)
        
        # 更新状态
        self.current_step += 1
        done = self._check_done()
        
        # 构建info
        info = {
            'profit': self.net_worth - INITIAL_ACCOUNT_BALANCE,
            'current_step': self.current_step,
            'action': parsed_action,
            'account_state': self._get_current_state()
        }
        
        next_obs = self._get_observation()
        
        # 兼容不同版本
        if GYM_VERSION == "gymnasium":
            truncated = False
            return next_obs, reward, done, truncated, info
        else:
            return next_obs, reward, done, info
       
    def _get_observation(self) -> np.ndarray:
        """获取当前观测"""
        if self.current_step >= len(self.df):
            self.current_step = len(self.df) - 1
            
        account_state = {
            'balance': self.balance,
            'net_worth': self.net_worth,
            'shares_held': self.shares_held,
            'cost_basis': self.cost_basis,
            'total_shares_sold': self.total_shares_sold,
            'total_sales_value': self.total_sales_value
        }
        
        return self.state_manager.extract_features(self.df, self.current_step, account_state)
        
    def _get_info(self) -> Dict:
        """获取环境信息"""
        return {
            'profit': self.net_worth - INITIAL_ACCOUNT_BALANCE,
            'current_step': self.current_step,
            'account_state': self._get_current_state()
        }
        
    def _get_current_state(self) -> Dict:
        """获取当前完整状态"""
        current_price = self.df.loc[self.current_step, 'close'] if self.current_step < len(self.df) else 0
        
        return {
            'balance': self.balance,
            'net_worth': self.net_worth,
            'shares_held': self.shares_held,
            'cost_basis': self.cost_basis,
            'total_shares_sold': self.total_shares_sold,
            'total_sales_value': self.total_sales_value,
            'current_price': current_price,
            'current_step': self.current_step
        }
        
    def _execute_trade(self, action: Dict) -> float:
        """执行交易并计算奖励"""
        if self.current_step >= len(self.df):
            return 0
            
        current_price = random.uniform(
            self.df.loc[self.current_step, "low"], 
            self.df.loc[self.current_step, "high"]
        )
        
        initial_net_worth = self.net_worth
        
        if action['trade_signal'] == 'buy' and self.balance >= current_price:
            # 买入逻辑
            max_shares = int(self.balance / current_price)
            shares_to_buy = int(max_shares * action['position_ratio'])
            
            if shares_to_buy > 0:
                cost = shares_to_buy * current_price
                self.balance -= cost
                
                # 更新成本基础
                if self.shares_held > 0:
                    total_cost = self.cost_basis * self.shares_held + cost
                    self.shares_held += shares_to_buy
                    self.cost_basis = total_cost / self.shares_held
                else:
                    self.shares_held = shares_to_buy
                    self.cost_basis = current_price
                
        elif action['trade_signal'] == 'sell' and self.shares_held > 0:
            # 卖出逻辑
            shares_to_sell = int(self.shares_held * action['position_ratio'])
            
            if shares_to_sell > 0:
                revenue = shares_to_sell * current_price
                self.balance += revenue
                self.shares_held -= shares_to_sell
                self.total_shares_sold += shares_to_sell
                self.total_sales_value += revenue
                
                if self.shares_held == 0:
                    self.cost_basis = 0
        
        # 更新净资产
        self.net_worth = self.balance + self.shares_held * current_price
        if self.net_worth > self.max_net_worth:
            self.max_net_worth = self.net_worth
            
        # 计算奖励
        return self._calculate_reward(initial_net_worth, action)
        
    def _calculate_reward(self, initial_net_worth: float, action: Dict) -> float:
        """计算奖励函数"""
        # 基础收益奖励
        profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
        profit_rate = profit / INITIAL_ACCOUNT_BALANCE
        
        # 收益奖励
        if profit_rate > 0:
            reward = profit_rate * 10
        else:
            reward = profit_rate * 5
            
        # 风险调整
        if self.shares_held > 0:
            current_price = self.df.loc[self.current_step, 'close']
            unrealized_loss_rate = (self.cost_basis - current_price) / self.cost_basis
            if unrealized_loss_rate > 0.1:
                reward -= unrealized_loss_rate * 2
                
        # 交易成本
        if action['trade_signal'] != 'hold':
            reward -= 0.001
            
        return reward
        
    def _check_done(self) -> bool:
        """检查是否结束"""
        if self.current_step >= len(self.df) - 1:
            return True
        if self.net_worth <= INITIAL_ACCOUNT_BALANCE * 0.1:
            return True
        if self.net_worth >= INITIAL_ACCOUNT_BALANCE * 3:
            return True
        return False
        
    def render(self, mode: str = 'human') -> None:
        """渲染环境信息"""
        profit = self.net_worth - INITIAL_ACCOUNT_BALANCE
        profit_rate = profit / INITIAL_ACCOUNT_BALANCE
        
        print('-' * 50)
        print(f'Step: {self.current_step}')
        print(f'Balance: ${self.balance:,.2f}')
        print(f'Shares held: {self.shares_held}')
        print(f'Cost basis: ${self.cost_basis:.2f}')
        print(f'Net worth: ${self.net_worth:,.2f}')
        print(f'Profit: ${profit:,.2f} ({profit_rate:.2%})')
        print(f'Max net worth: ${self.max_net_worth:,.2f}')
        print('-' * 50)

# =================== 数据生成函数 ===================
def generate_sample_data(days: int = 1000) -> pd.DataFrame:
    """生成示例股票数据"""
    np.random.seed(SEED)
    
    dates = pd.date_range(start='2020-01-01', periods=days, freq='D')
    
    # 生成价格数据
    initial_price = 100.0
    prices = [initial_price]
    
    for i in range(1, days):
        # 随机游走 + 趋势
        change = np.random.normal(0, 0.02) + 0.0001  # 小幅上涨趋势
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1.0))  # 价格不能为负
    
    # 生成OHLC数据
    data = []
    for i, price in enumerate(prices):
        volatility = np.random.uniform(0.005, 0.03)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        open_price = np.random.uniform(low, high)
        close_price = price
        
        volume = np.random.randint(1000000, 10000000)
        amount = volume * close_price
        
        data.append({
            'date': dates[i],
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume,
            'amount': amount
        })
    
    df = pd.DataFrame(data)
    df.reset_index(drop=True, inplace=True)
    return df

# =================== 简单RL模型 ===================
class SimpleActor(nn.Module):
    """简单的Actor网络"""
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 128):
        super(SimpleActor, self).__init__()
        self.network = nn.Sequential(
            nn.Linear(state_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim),
            nn.Tanh()
        )
        
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        return self.network(state)

# =================== 测试函数 ===================
def test_environment():
    """测试环境基本功能"""
    print("生成测试数据...")
    df = generate_sample_data(100)
    print(f"数据形状: {df.shape}")
    print(f"价格范围: {df['close'].min():.2f} - {df['close'].max():.2f}")
    
    print("\n创建环境...")
    env = StockTradingEnv(df)
    
    print("测试环境重置...")
    obs = env.reset()
    if isinstance(obs, tuple):
        obs = obs[0]  # gymnasium版本返回(obs, info)
    
    print(f"观测空间维度: {obs.shape}")
    print(f"动作空间: {env.action_space}")
    print(f"观测样本: {obs[:5]}")
    
    print("\n测试随机动作...")
    total_reward = 0
    for step in range(20):
        action = env.action_space.sample()
        result = env.step(action)
        
        if len(result) == 5:  # gymnasium版本
            obs, reward, done, truncated, info = result
        else:  # gym版本
            obs, reward, done, info = result
            
        total_reward += reward
        print(f"Step {step:2d}: Action={action}, Reward={reward:6.4f}, Profit=${info['profit']:8.2f}")
        
        if done:
            print(f"环境结束于第 {step} 步")
            break
    
    print(f"\n总奖励: {total_reward:.4f}")
    print("环境测试完成！")

def test_model():
    """测试简单模型"""
    print("\n测试简单模型...")
    
    # 创建模型
    state_dim = 20
    action_dim = 2
    model = SimpleActor(state_dim, action_dim)
    
    # 测试前向传播
    test_state = torch.randn(1, state_dim)
    action = model(test_state)
    
    print(f"模型输入维度: {state_dim}")
    print(f"模型输出维度: {action_dim}")
    print(f"测试输出: {action.detach().numpy()}")
    print("模型测试完成！")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='独立的强化学习股票交易环境')
    parser.add_argument('--test', action='store_true', help='运行环境测试')
    parser.add_argument('--test-model', action='store_true', help='测试模型')
    parser.add_argument('--days', type=int, default=1000, help='生成数据的天数')
    
    args = parser.parse_args()
    
    if args.test:
        test_environment()
    elif args.test_model:
        test_model()
    else:
        print("独立的强化学习股票交易环境")
        print("使用 --test 运行环境测试")
        print("使用 --test-model 测试模型")
        print("使用 --days N 指定生成数据的天数")