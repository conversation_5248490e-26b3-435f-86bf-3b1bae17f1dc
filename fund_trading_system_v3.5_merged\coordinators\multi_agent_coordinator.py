"""
Multi-Agent Coordinator Implementation

This coordinator manages the collaboration between RL, LLM, and CZSC agents,
implementing decision fusion and conflict resolution strategies.

Key Features:
- Multiple fusion strategies: weighted, consensus, confidence_override, conservative
- Real-time signal coordination and conflict resolution
- Performance tracking and statistics
- Extensible architecture for adding new fusion methods

Classes:
    AgentWeight: Configuration for agent weights in coordination
    CoordinatedDecision: Result of multi-agent decision fusion
    MultiAgentCoordinator: Main coordination engine

Example:
    ```python
    from agents.rl.rl_agent import RLAgent
    from agents.llm.llm_agent import LLMAgent
    from agents.czsc.czsc_agent import CZSCAgent

    # Create agents
    agents = {
        'rl': RLAgent("RL"),
        'llm': LLMAgent("LLM"),
        'czsc': CZSCAgent("CZSC")
    }

    # Create coordinator
    coordinator = MultiAgentCoordinator(agents)

    # Generate coordinated decision
    decision = coordinator.generate_coordinated_decision(market_data, method="weighted")
    ```
"""

import logging
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict

from agents import BaseAgent, TradingSignal, MarketData, RLAgent, LLMAgent, CZSCAgent


@dataclass
class AgentWeight:
    """Agent weight configuration for decision fusion"""
    rl_weight: float = 0.4      # RL agent weight (quantitative analysis)
    llm_weight: float = 0.3     # LLM agent weight (semantic analysis)
    czsc_weight: float = 0.3    # CZSC agent weight (technical analysis)
    
    def normalize(self):
        """Normalize weights to sum to 1.0"""
        total = self.rl_weight + self.llm_weight + self.czsc_weight
        if total > 0:
            self.rl_weight /= total
            self.llm_weight /= total
            self.czsc_weight /= total


@dataclass
class CoordinatedDecision:
    """Result of coordinated decision making"""
    final_action: str
    final_confidence: float
    individual_signals: Dict[str, TradingSignal]
    fusion_method: str
    conflict_resolution: str
    reasoning: str
    timestamp: datetime


class MultiAgentCoordinator:
    """
    Multi-Agent Coordinator for Trading System

    Coordinates decisions from multiple trading agents (RL, LLM, CZSC) using
    various fusion strategies to generate unified trading signals.

    Attributes:
        agents (Dict[str, Any]): Dictionary of agent instances
        fusion_methods (List[str]): Available fusion strategies
        performance_stats (Dict): Performance tracking for each method
        last_experiment_results (Dict): Results from last batch experiment

    Fusion Methods:
        - weighted: Weighted average based on confidence scores
        - consensus: Majority voting among agents
        - confidence_override: Highest confidence agent decides
        - conservative: Hold if any agent suggests hold, otherwise weighted

    Example:
        ```python
        agents = {'rl': rl_agent, 'llm': llm_agent, 'czsc': czsc_agent}
        coordinator = MultiAgentCoordinator(agents)
        decision = coordinator.generate_coordinated_decision(market_data, "weighted")
        ```
    """
    def __init__(self, agents: Dict[str, Any], fusion_methods: Optional[List[str]] = None):
        """
        Initialize the Multi-Agent Coordinator.

        Args:
            agents: Dictionary mapping agent names to agent instances
                   Expected format: {'rl': RLAgent, 'llm': LLMAgent, 'czsc': CZSCAgent}
            fusion_methods: List of fusion methods to support
                          Default: ["weighted", "consensus", "confidence_override", "conservative"]
        """
        self.agents = agents  # {'rl': RLAgent, 'llm': LLMAgent, 'czsc': CZSCAgent}
        self.fusion_methods = fusion_methods or ["weighted", "consensus", "confidence_override", "conservative"]
        self.logger = logging.getLogger("MultiAgentCoordinator")
        self.performance_stats = defaultdict(lambda: defaultdict(list))  # {method: {agent: [correct, total]}}
        self.last_experiment_results = None

    def generate_coordinated_decision(self, market_data: Any, method: str = "weighted") -> Dict[str, Any]:
        """
        Generate coordinated trading decision from multiple agents.

        This method collects signals from all agents and fuses them using the specified
        fusion strategy to produce a unified trading decision.

        Args:
            market_data: Market data object containing price, volume, and technical indicators
            method: Fusion method to use. Options:
                   - "weighted": Weighted average based on confidence scores
                   - "consensus": Majority voting among agents
                   - "confidence_override": Highest confidence agent decides
                   - "conservative": Hold if any agent suggests hold

        Returns:
            Dict containing:
                - final_decision: Dict with 'action' and 'confidence'
                - agent_signals: Dict of individual agent signals

        Example:
            ```python
            decision = coordinator.generate_coordinated_decision(market_data, "weighted")
            action = decision['final_decision']['action']  # 'buy', 'sell', or 'hold'
            confidence = decision['final_decision']['confidence']  # 0.0 to 1.0
            ```
        """
        agent_signals = {}
        for name, agent in self.agents.items():
            # 假设每个agent有generate_signal方法，返回{'action': str, 'confidence': float}
            signal = agent.generate_signal(market_data)
            agent_signals[name] = signal
        # 融合决策
        final_decision = self._fuse_signals(agent_signals, method)
        # 记录性能统计（需传入真实标签时可用）
        self._update_stats(agent_signals, final_decision, method)
        return {'final_decision': final_decision, 'agent_signals': agent_signals}

    def _fuse_signals(self, agent_signals: Dict[str, Any], method: str) -> Dict[str, Any]:
        """多策略融合实现（简化版）"""
        if method == "weighted":
            # 简单加权平均（假设每个signal有confidence）
            actions = defaultdict(float)
            total_weight = 0
            for name, sig in agent_signals.items():
                # Handle both dict and TradingSignal object
                if hasattr(sig, 'action'):  # TradingSignal object
                    action = sig.action
                    confidence = sig.confidence
                else:  # dict format
                    action = sig['action']
                    confidence = sig.get('confidence', 1.0)

                actions[action] += confidence
                total_weight += confidence
            best_action = max(actions, key=actions.get)
            return {'action': best_action, 'confidence': actions[best_action] / (total_weight or 1)}
        elif method == "consensus":
            # 多数投票
            votes = defaultdict(int)
            for sig in agent_signals.values():
                action = sig.action if hasattr(sig, 'action') else sig['action']
                votes[action] += 1
            best_action = max(votes, key=votes.get)
            return {'action': best_action, 'confidence': votes[best_action] / len(agent_signals)}
        elif method == "confidence_override":
            # 置信度最高者决定
            best = max(agent_signals.values(), key=lambda s: s.confidence if hasattr(s, 'confidence') else s.get('confidence', 0))
            action = best.action if hasattr(best, 'action') else best['action']
            confidence = best.confidence if hasattr(best, 'confidence') else best['confidence']
            return {'action': action, 'confidence': confidence}
        elif method == "conservative":
            # 只要有hold则hold，否则加权
            has_hold = any((sig.action if hasattr(sig, 'action') else sig['action']) == 'hold' for sig in agent_signals.values())
            if has_hold:
                return {'action': 'hold', 'confidence': 1.0}
            return self._fuse_signals(agent_signals, 'weighted')
        else:
            return self._fuse_signals(agent_signals, 'weighted')

    def _update_stats(self, agent_signals, final_decision, method):
        # 仅结构预留，需传入真实标签时可用
        pass

    def run_fusion_experiment(self, market_data_list: List[Any], true_labels: Optional[List[str]] = None) -> Dict[str, Any]:
        """对比不同融合策略的实验接口，输出每种策略的决策分布和性能统计"""
        results = {m: {'decisions': [], 'correct': 0, 'total': 0} for m in self.fusion_methods}
        for i, data in enumerate(market_data_list):
            label = true_labels[i] if true_labels else None
            for method in self.fusion_methods:
                out = self.generate_coordinated_decision(data, method)
                action = out['final_decision']['action']
                results[method]['decisions'].append(action)
                if label is not None:
                    if action == label:
                        results[method]['correct'] += 1
                    results[method]['total'] += 1
        # 统计准确率
        for method in self.fusion_methods:
            if results[method]['total'] > 0:
                results[method]['accuracy'] = results[method]['correct'] / results[method]['total']
            else:
                results[method]['accuracy'] = None
        self.last_experiment_results = results
        return results

    def get_coordination_stats(self) -> Dict[str, Any]:
        """Get coordination statistics and performance metrics"""
        stats = {
            'total_agents': len(self.agents),
            'fusion_methods': self.fusion_methods,
            'performance_stats': dict(self.performance_stats),
            'last_experiment_results': self.last_experiment_results
        }

        # Add agent status
        agent_status = {}
        for name, agent in self.agents.items():
            if hasattr(agent, 'get_status'):
                agent_status[name] = agent.get_status()
            else:
                agent_status[name] = {'name': name, 'type': getattr(agent, 'agent_type', 'unknown')}

        stats['agent_status'] = agent_status
        return stats

    def get_agent_features(self) -> Dict[str, Any]:
        """Get agent feature information"""
        features = {}

        for name, agent in self.agents.items():
            agent_features = {
                'name': name,
                'type': getattr(agent, 'agent_type', 'unknown'),
                'initialized': getattr(agent, 'is_initialized', False),
                'confidence': agent.get_confidence() if hasattr(agent, 'get_confidence') else 0.0
            }

            # Add performance metrics if available
            if hasattr(agent, 'performance_metrics'):
                agent_features['performance'] = agent.performance_metrics

            features[name] = agent_features

        return features