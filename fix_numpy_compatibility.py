#!/usr/bin/env python3
"""
NumPy 2.0 兼容性修复脚本
解决 "numpy.dtype size changed" 和 "_ARRAY_API not found" 错误
"""

import subprocess
import sys
import os

def run_command(command, ignore_errors=False):
    """执行命令并处理错误"""
    try:
        print(f"执行: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout.strip():
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        if not ignore_errors:
            print(f"错误: {e}")
            if e.stderr:
                print(f"错误详情: {e.stderr}")
        return False

def check_current_versions():
    """检查当前版本"""
    print("=== 当前环境检查 ===")
    
    try:
        import numpy
        print(f"NumPy 版本: {numpy.__version__}")
    except ImportError:
        print("NumPy 未安装")
    
    try:
        import pandas
        print(f"Pandas 版本: {pandas.__version__}")
    except ImportError:
        print("Pandas 未安装")
    
    try:
        import cv2
        print(f"OpenCV 版本: {cv2.__version__}")
    except ImportError:
        print("OpenCV 未安装")
    
    try:
        import gym
        print(f"Gym 版本: {gym.__version__}")
    except ImportError:
        print("Gym 未安装")

def fix_numpy_compatibility():
    """修复NumPy兼容性问题"""
    print("\n=== 开始修复NumPy兼容性问题 ===")
    
    # 方案1: 降级NumPy到1.x版本
    print("\n方案1: 降级NumPy到兼容版本...")
    commands_plan1 = [
        f"{sys.executable} -m pip uninstall numpy -y",
        f"{sys.executable} -m pip install 'numpy<2.0'",
        f"{sys.executable} -m pip install --force-reinstall --no-deps 'numpy<2.0'",
    ]
    
    for cmd in commands_plan1:
        if not run_command(cmd, ignore_errors=True):
            print(f"命令失败，继续下一步...")
    
    # 重新安装可能有问题的包
    print("\n重新安装相关包...")
    packages_to_reinstall = [
        "pandas",
        "opencv-python", 
        "gym",
        "stable-baselines3"
    ]
    
    for package in packages_to_reinstall:
        print(f"\n重新安装 {package}...")
        run_command(f"{sys.executable} -m pip uninstall {package} -y", ignore_errors=True)
        run_command(f"{sys.executable} -m pip install {package}", ignore_errors=True)

def alternative_fix():
    """替代修复方案"""
    print("\n=== 替代修复方案 ===")
    print("如果上述方案不工作，尝试以下步骤:")
    
    # 使用gymnasium替代gym
    print("\n使用 gymnasium 替代 gym...")
    run_command(f"{sys.executable} -m pip uninstall gym -y", ignore_errors=True)
    run_command(f"{sys.executable} -m pip install gymnasium", ignore_errors=True)
    
    # 安装特定版本的包
    print("\n安装特定兼容版本...")
    compatible_packages = [
        "numpy==1.24.3",
        "pandas==2.0.3", 
        "opencv-python==********",
        "stable-baselines3==2.0.0"
    ]
    
    for package in compatible_packages:
        run_command(f"{sys.executable} -m pip install {package}", ignore_errors=True)

def create_fixed_script():
    """创建修复后的脚本版本"""
    print("\n=== 创建兼容版本的脚本 ===")
    
    fixed_script_content = '''import argparse
import os
import gymnasium as gym  # 使用gymnasium替代gym
import random
from gymnasium import spaces  # 使用gymnasium.spaces
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from stable_baselines3 import SAC, PPO, A2C
from stable_baselines3.common.env_checker import check_env
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import EvalCallback, StopTrainingOnRewardThreshold
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.noise import NormalActionNoise
from stable_baselines3.common.policies import ActorCriticPolicy
from stable_baselines3.sac.policies import SACPolicy
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# 其余代码保持不变...
print("已创建兼容版本，请查看 enhanced_rl_stock_base_sb3_fixed.py")
'''
    
    with open('enhanced_rl_stock_base_sb3_fixed.py', 'w', encoding='utf-8') as f:
        f.write("# 修复NumPy兼容性问题的版本\n")
        f.write("# 主要变更: gym -> gymnasium\n\n")
        f.write(fixed_script_content)

def verify_fix():
    """验证修复结果"""
    print("\n=== 验证修复结果 ===")
    
    test_imports = [
        ("numpy", "import numpy; print(f'NumPy: {numpy.__version__}')"),
        ("pandas", "import pandas; print(f'Pandas: {pandas.__version__}')"),
        ("opencv", "import cv2; print(f'OpenCV: {cv2.__version__}')"),
        ("gymnasium", "import gymnasium; print(f'Gymnasium: {gymnasium.__version__}')"),
        ("stable_baselines3", "import stable_baselines3; print(f'SB3: {stable_baselines3.__version__}')")
    ]
    
    success_count = 0
    for name, import_code in test_imports:
        try:
            exec(import_code)
            print(f"✓ {name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {name} 导入失败: {e}")
    
    print(f"\n成功导入: {success_count}/{len(test_imports)}")
    
    if success_count == len(test_imports):
        print("🎉 所有包导入成功！")
        return True
    else:
        print("❌ 仍有包导入失败，可能需要手动处理")
        return False

def main():
    """主函数"""
    print("NumPy 2.0 兼容性修复工具")
    print("=" * 50)
    
    # 检查当前状态
    check_current_versions()
    
    # 尝试修复
    fix_numpy_compatibility()
    
    # 替代方案
    alternative_fix()
    
    # 创建修复版本的脚本
    create_fixed_script()
    
    # 验证修复
    success = verify_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 修复完成！现在可以尝试运行:")
        print("python enhanced_rl_stock_base_sb3.py")
    else:
        print("⚠️  如果问题仍然存在，请尝试:")
        print("1. 重启Python环境/IDE")
        print("2. 使用虚拟环境:")
        print("   conda create -n trading python=3.9")
        print("   conda activate trading")
        print("   pip install 'numpy<2.0' pandas stable-baselines3 gymnasium")
        print("3. 或者使用修复版本的脚本")

if __name__ == "__main__":
    main()