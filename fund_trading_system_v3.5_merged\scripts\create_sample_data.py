#!/usr/bin/env python3
"""
Sample Data Generation Script

Creates sample historical data files for testing the backtesting system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backtest.engines.data_manager import DataManager


def main():
    """Generate sample data files for multiple symbols"""
    data_manager = DataManager("data")
    
    # Generate sample data for common Chinese stocks
    symbols = [
        "000001.SZ",  # Ping An Bank
        "000002.SZ",  # Vanke
        "600036.SH",  # China Merchants Bank
        "600519.SH",  # Kweichow Moutai
        "000858.SZ"   # Wuliangye
    ]
    
    print("🔄 Generating sample data files...")
    
    for symbol in symbols:
        print(f"Creating data for {symbol}...")
        data_manager.save_sample_data(
            symbol=symbol,
            start_date="2020-01-01",
            end_date="2023-12-31"
        )
    
    print("✅ Sample data generation completed!")
    print(f"📁 Data files saved in: {data_manager.data_dir}")
    
    # List available symbols
    available_symbols = data_manager.get_available_symbols()
    print(f"\n📊 Available symbols: {available_symbols}")


if __name__ == "__main__":
    main() 