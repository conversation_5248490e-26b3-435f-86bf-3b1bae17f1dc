"""
结构维度评估器
负责评估市场结构的稳定性和变化趋势
"""

import logging
import sys
import os
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.data_structures import DimensionEvaluationResult
from core.enums import StructuralState


class StructuralEvaluator:
    """
    @class StructuralEvaluator
    @brief 结构维度评估器
    @details 负责评估市场结构的稳定性和变化趋势
    """
    
    def __init__(self):
        self.name = "StructuralEvaluator"
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    def evaluate(self, data: Dict[str, Any]) -> DimensionEvaluationResult:
        """
        @brief 评估结构维度
        @param data: 市场数据
        @return: 结构评估结果
        """
        try:
            fund_code = data.get('fund_code', 'UNKNOWN')
            technical_data = data.get('technical_data', {})
            price_data = data.get('price_data', {})
            
            # 获取结构相关数据
            ma5 = technical_data.get('ma5', 0)
            ma20 = technical_data.get('ma20', 0)
            current_price = price_data.get('price', 0)
            change_rate = price_data.get('change_rate', 0)
            
            # 结构评分计算
            structural_signals = []
            structural_score = 0.0
            
            # 1. 移动平均线结构
            if ma5 > 0 and ma20 > 0:
                ma_ratio = ma5 / ma20
                if ma_ratio >= 1.05:
                    ma_structure = "强势上升"
                    ma_strength = min(1.0, (ma_ratio - 1) / 0.1)
                    structural_score += ma_strength * 0.4
                    structural_signals.append(f"MA结构强势上升({ma_ratio:.3f})")
                elif ma_ratio >= 1.02:
                    ma_structure = "温和上升"
                    ma_strength = (ma_ratio - 1) / 0.05
                    structural_score += ma_strength * 0.3
                    structural_signals.append(f"MA结构温和上升({ma_ratio:.3f})")
                elif ma_ratio >= 0.98:
                    ma_structure = "横盘整理"
                    structural_score += 0.1
                    structural_signals.append(f"MA结构横盘整理({ma_ratio:.3f})")
                elif ma_ratio >= 0.95:
                    ma_structure = "温和下降"
                    ma_strength = (1 - ma_ratio) / 0.05
                    structural_score -= ma_strength * 0.3
                    structural_signals.append(f"MA结构温和下降({ma_ratio:.3f})")
                else:
                    ma_structure = "弱势下降"
                    ma_strength = min(1.0, (1 - ma_ratio) / 0.1)
                    structural_score -= ma_strength * 0.4
                    structural_signals.append(f"MA结构弱势下降({ma_ratio:.3f})")
            else:
                ma_structure = "数据不足"
                structural_signals.append("MA数据不足")
            
            # 2. 价格位置结构
            if current_price > 0 and ma20 > 0:
                price_position = current_price / ma20
                if price_position >= 1.1:
                    position_structure = "高位运行"
                    pos_strength = min(1.0, (price_position - 1) / 0.2)
                    structural_score += pos_strength * 0.3
                    structural_signals.append(f"价格高位运行({price_position:.3f})")
                elif price_position >= 1.05:
                    position_structure = "中高位"
                    pos_strength = (price_position - 1) / 0.1
                    structural_score += pos_strength * 0.2
                    structural_signals.append(f"价格中高位({price_position:.3f})")
                elif price_position >= 0.95:
                    position_structure = "中位运行"
                    structural_score += 0.05
                    structural_signals.append(f"价格中位运行({price_position:.3f})")
                elif price_position >= 0.9:
                    position_structure = "中低位"
                    pos_strength = (1 - price_position) / 0.1
                    structural_score -= pos_strength * 0.2
                    structural_signals.append(f"价格中低位({price_position:.3f})")
                else:
                    position_structure = "低位运行"
                    pos_strength = min(1.0, (1 - price_position) / 0.2)
                    structural_score -= pos_strength * 0.3
                    structural_signals.append(f"价格低位运行({price_position:.3f})")
            else:
                position_structure = "数据不足"
                structural_signals.append("价格位置数据不足")
            
            # 3. 变化稳定性
            if abs(change_rate) <= 1:
                stability = "高稳定性"
                stab_strength = 1.0 - abs(change_rate)
                structural_score += stab_strength * 0.3
                structural_signals.append(f"高稳定性({change_rate:+.2f}%)")
            elif abs(change_rate) <= 3:
                stability = "中等稳定性"
                stab_strength = 1.0 - abs(change_rate) / 3
                structural_score += stab_strength * 0.2
                structural_signals.append(f"中等稳定性({change_rate:+.2f}%)")
            else:
                stability = "低稳定性"
                instab_strength = min(1.0, abs(change_rate) / 5)
                structural_score -= instab_strength * 0.3
                structural_signals.append(f"低稳定性({change_rate:+.2f}%)")
            
            # 结构状态判断
            if structural_score >= 0.7:
                structural_state = StructuralState.BREAKOUT
            elif structural_score >= 0.3:
                structural_state = StructuralState.STRENGTHENING
            elif structural_score >= -0.3:
                structural_state = StructuralState.STABLE
            elif structural_score >= -0.7:
                structural_state = StructuralState.WEAKENING
            else:
                structural_state = StructuralState.BREAKDOWN
            
            # 置信度计算
            data_completeness = len([x for x in [ma5, ma20, current_price] if x > 0]) / 3
            confidence = min(0.95, max(0.2, data_completeness * 0.8 + abs(structural_score) * 0.2))
            
            # 数据质量评估
            data_quality = "good" if data_completeness >= 0.8 else "poor"
            
            return DimensionEvaluationResult(
                dimension_name="结构",
                state=structural_state,
                score=structural_score,
                confidence=confidence,
                signals=structural_signals,
                data_quality=data_quality,
                details={
                    'ma_structure': ma_structure,
                    'position_structure': position_structure,
                    'stability': stability
                },
                indicators={
                    'ma5': ma5,
                    'ma20': ma20,
                    'current_price': current_price,
                    'change_rate': change_rate,
                    'structural_score': structural_score
                }
            )
            
        except Exception as e:
            self.logger.error(f"Structural evaluation failed: {str(e)}")
            return DimensionEvaluationResult(
                dimension_name="结构",
                state=StructuralState.STABLE,
                score=0.0,
                confidence=0.0,
                signals=[f"评估失败: {str(e)}"],
                data_quality="error"
            )
