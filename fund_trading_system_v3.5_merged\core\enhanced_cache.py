"""
Enhanced Cache System for Trading System

Provides advanced caching capabilities including TTL, LRU eviction,
persistence, and performance monitoring.
"""

import time
import pickle
import hashlib
import logging
import threading
from pathlib import Path
from typing import Any, Dict, Optional, Union, List
from collections import OrderedDict
from datetime import datetime, timedelta


class EnhancedCache:
    """
    Enhanced cache system with TTL, LRU eviction, and persistence support
    
    Features:
    - Time-to-Live (TTL) support
    - LRU (Least Recently Used) eviction policy
    - Persistent storage with compression
    - Thread-safe operations
    - Performance monitoring and statistics
    - Automatic cleanup of expired entries
    """
    
    def __init__(self, 
                 max_size: int = 1000,
                 default_ttl: int = 3600,
                 persist_path: Optional[str] = None,
                 enable_compression: bool = True,
                 cleanup_interval: int = 300):
        """
        Initialize enhanced cache
        
        Args:
            max_size: Maximum number of cache entries
            default_ttl: Default time-to-live in seconds
            persist_path: Path for persistent storage
            enable_compression: Enable data compression
            cleanup_interval: Cleanup interval in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.enable_compression = enable_compression
        self.cleanup_interval = cleanup_interval
        
        # Cache storage
        self._cache = OrderedDict()
        self._timestamps = {}
        self._ttls = {}
        self._access_counts = {}
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Persistence
        self.persist_path = Path(persist_path) if persist_path else None
        if self.persist_path:
            self.persist_path.mkdir(parents=True, exist_ok=True)
        
        # Statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expirations': 0,
            'total_requests': 0,
            'cache_size': 0,
            'last_cleanup': time.time()
        }
        
        # Logger
        self.logger = logging.getLogger("EnhancedCache")
        
        # Start cleanup thread
        self._start_cleanup_thread()
        
        # Load persisted data
        if self.persist_path:
            self._load_from_disk()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        with self._lock:
            self.stats['total_requests'] += 1
            
            # Check if key exists and is not expired
            if key in self._cache and not self._is_expired(key):
                # Move to end (LRU)
                value = self._cache.pop(key)
                self._cache[key] = value
                self._access_counts[key] = self._access_counts.get(key, 0) + 1
                
                self.stats['hits'] += 1
                return value
            
            # Key not found or expired
            if key in self._cache:
                self._remove_key(key)
                self.stats['expirations'] += 1
            
            self.stats['misses'] += 1
            return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set value in cache
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (None for default)
        """
        with self._lock:
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.default_ttl
            
            # Remove existing key if present
            if key in self._cache:
                self._remove_key(key)
            
            # Check if we need to evict entries
            while len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # Add new entry
            self._cache[key] = value
            self._timestamps[key] = time.time()
            self._ttls[key] = ttl
            self._access_counts[key] = 1
            
            self.stats['cache_size'] = len(self._cache)
            
            # Persist if enabled
            if self.persist_path:
                self._persist_key(key, value, ttl)
    
    def delete(self, key: str) -> bool:
        """
        Delete key from cache
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted, False if not found
        """
        with self._lock:
            if key in self._cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
            self._ttls.clear()
            self._access_counts.clear()
            self.stats['cache_size'] = 0
            
            # Clear persistent storage
            if self.persist_path:
                for file_path in self.persist_path.glob("*.cache"):
                    file_path.unlink()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            hit_rate = self.stats['hits'] / max(self.stats['total_requests'], 1)
            
            return {
                **self.stats.copy(),
                'hit_rate': hit_rate,
                'miss_rate': 1 - hit_rate,
                'current_size': len(self._cache),
                'max_size': self.max_size,
                'memory_usage_mb': self._estimate_memory_usage() / (1024 * 1024)
            }
    
    def cleanup_expired(self) -> int:
        """
        Cleanup expired entries
        
        Returns:
            Number of expired entries removed
        """
        with self._lock:
            expired_keys = []
            current_time = time.time()
            
            for key in list(self._cache.keys()):
                if self._is_expired(key):
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_key(key)
            
            self.stats['expirations'] += len(expired_keys)
            self.stats['last_cleanup'] = current_time
            
            return len(expired_keys)
    
    def _is_expired(self, key: str) -> bool:
        """Check if key is expired"""
        if key not in self._timestamps or key not in self._ttls:
            return True
        
        if self._ttls[key] <= 0:  # No expiration
            return False
        
        return time.time() - self._timestamps[key] > self._ttls[key]
    
    def _remove_key(self, key: str) -> None:
        """Remove key from all internal structures"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
        self._ttls.pop(key, None)
        self._access_counts.pop(key, None)
        self.stats['cache_size'] = len(self._cache)
        
        # Remove from persistent storage
        if self.persist_path:
            cache_file = self.persist_path / f"{self._hash_key(key)}.cache"
            if cache_file.exists():
                cache_file.unlink()
    
    def _evict_lru(self) -> None:
        """Evict least recently used entry"""
        if self._cache:
            # Get least recently used key (first in OrderedDict)
            lru_key = next(iter(self._cache))
            self._remove_key(lru_key)
            self.stats['evictions'] += 1
    
    def _hash_key(self, key: str) -> str:
        """Generate hash for key"""
        return hashlib.md5(key.encode()).hexdigest()
    
    def _persist_key(self, key: str, value: Any, ttl: int) -> None:
        """Persist key to disk"""
        try:
            cache_file = self.persist_path / f"{self._hash_key(key)}.cache"
            
            cache_data = {
                'key': key,
                'value': value,
                'timestamp': time.time(),
                'ttl': ttl
            }
            
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
                
        except Exception as e:
            self.logger.warning(f"Failed to persist cache key {key}: {e}")
    
    def _load_from_disk(self) -> None:
        """Load cache from persistent storage"""
        try:
            for cache_file in self.persist_path.glob("*.cache"):
                try:
                    with open(cache_file, 'rb') as f:
                        cache_data = pickle.load(f)
                    
                    key = cache_data['key']
                    value = cache_data['value']
                    timestamp = cache_data['timestamp']
                    ttl = cache_data['ttl']
                    
                    # Check if not expired
                    if ttl <= 0 or time.time() - timestamp <= ttl:
                        self._cache[key] = value
                        self._timestamps[key] = timestamp
                        self._ttls[key] = ttl
                        self._access_counts[key] = 0
                    else:
                        # Remove expired file
                        cache_file.unlink()
                        
                except Exception as e:
                    self.logger.warning(f"Failed to load cache file {cache_file}: {e}")
                    cache_file.unlink()
            
            self.stats['cache_size'] = len(self._cache)
            self.logger.info(f"Loaded {len(self._cache)} entries from persistent cache")
            
        except Exception as e:
            self.logger.error(f"Failed to load cache from disk: {e}")
    
    def _estimate_memory_usage(self) -> int:
        """Estimate memory usage in bytes"""
        try:
            total_size = 0
            for value in self._cache.values():
                total_size += len(pickle.dumps(value))
            return total_size
        except:
            return 0
    
    def _start_cleanup_thread(self) -> None:
        """Start background cleanup thread"""
        def cleanup_loop():
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    expired_count = self.cleanup_expired()
                    if expired_count > 0:
                        self.logger.debug(f"Cleaned up {expired_count} expired cache entries")
                except Exception as e:
                    self.logger.error(f"Cache cleanup thread error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()
        self.logger.info("Cache cleanup thread started")


class DataCacheManager:
    """
    Specialized cache manager for trading data
    """
    
    def __init__(self, cache_dir: str = "cache"):
        """Initialize data cache manager"""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Different caches for different data types
        self.market_data_cache = EnhancedCache(
            max_size=500,
            default_ttl=300,  # 5 minutes for market data
            persist_path=str(self.cache_dir / "market_data")
        )
        
        self.analysis_cache = EnhancedCache(
            max_size=200,
            default_ttl=1800,  # 30 minutes for analysis results
            persist_path=str(self.cache_dir / "analysis")
        )
        
        self.model_cache = EnhancedCache(
            max_size=50,
            default_ttl=3600,  # 1 hour for model results
            persist_path=str(self.cache_dir / "models")
        )
        
        self.logger = logging.getLogger("DataCacheManager")
    
    def get_market_data(self, symbol: str, date_range: str) -> Any:
        """Get cached market data"""
        key = f"market_{symbol}_{date_range}"
        return self.market_data_cache.get(key)
    
    def set_market_data(self, symbol: str, date_range: str, data: Any, ttl: int = None) -> None:
        """Cache market data"""
        key = f"market_{symbol}_{date_range}"
        self.market_data_cache.set(key, data, ttl)
    
    def get_analysis_result(self, symbol: str, analysis_type: str) -> Any:
        """Get cached analysis result"""
        key = f"analysis_{symbol}_{analysis_type}"
        return self.analysis_cache.get(key)
    
    def set_analysis_result(self, symbol: str, analysis_type: str, result: Any, ttl: int = None) -> None:
        """Cache analysis result"""
        key = f"analysis_{symbol}_{analysis_type}"
        self.analysis_cache.set(key, result, ttl)
    
    def get_model_result(self, model_name: str, input_hash: str) -> Any:
        """Get cached model result"""
        key = f"model_{model_name}_{input_hash}"
        return self.model_cache.get(key)
    
    def set_model_result(self, model_name: str, input_hash: str, result: Any, ttl: int = None) -> None:
        """Cache model result"""
        key = f"model_{model_name}_{input_hash}"
        self.model_cache.set(key, result, ttl)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all caches"""
        return {
            'market_data': self.market_data_cache.get_stats(),
            'analysis': self.analysis_cache.get_stats(),
            'model': self.model_cache.get_stats()
        }
    
    def clear_all(self) -> None:
        """Clear all caches"""
        self.market_data_cache.clear()
        self.analysis_cache.clear()
        self.model_cache.clear()
        self.logger.info("All caches cleared")
