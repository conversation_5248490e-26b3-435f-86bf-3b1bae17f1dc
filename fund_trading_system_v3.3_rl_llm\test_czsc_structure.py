#!/usr/bin/env python3
"""
测试CZSC结构数据提取和LLM分析
"""

import sys
import os
import logging

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.config import setup_logging
from coordinators.multi_agent_coordinator import MultiAgentCoordinatorV3

def test_czsc_structure():
    """测试CZSC结构数据"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🔍 测试CZSC结构数据提取和LLM分析")
    print("=" * 50)
    
    try:
        # 初始化协调器
        coordinator = MultiAgentCoordinatorV3()
        
        # 测试基金代码
        test_fund_code = '513500'
        
        print(f"📊 测试基金: {test_fund_code}")
        print("-" * 30)
        
        # 模拟整合数据
        integrated_data = {'fund_code': test_fund_code}
        
        # 测试CZSC结构数据提取
        print("🔧 提取CZSC结构数据...")
        czsc_structure = coordinator._extract_czsc_structure_data(test_fund_code, integrated_data)
        
        if czsc_structure:
            print("✅ CZSC结构数据提取成功!")
            
            # 显示结构数据摘要
            fx_count = len(czsc_structure.get('fx_list', []))
            bi_count = len(czsc_structure.get('bi_list', []))
            xd_count = len(czsc_structure.get('xd_list', []))
            
            print(f"📈 分型(FX)数量: {fx_count}")
            print(f"📊 笔(BI)数量: {bi_count}")
            print(f"📉 线段(XD)数量: {xd_count}")
            
            # 显示最近的分型数据
            fx_list = czsc_structure.get('fx_list', [])
            if fx_list:
                print("\n🔍 最近的分型数据:")
                for i, fx in enumerate(fx_list[-3:], 1):
                    fx_type = "顶分型" if fx['fx_mark'] == 'g' else "底分型"
                    print(f"  分型{i}: {fx_type}, 价格={fx['fx_price']:.4f}, 时间={fx['dt']}")
            
            # 显示最近的笔数据
            bi_list = czsc_structure.get('bi_list', [])
            if bi_list:
                print("\n📊 最近的笔数据:")
                for i, bi in enumerate(bi_list[-3:], 1):
                    print(f"  笔{i}: {bi['direction']}, 价格={bi['bi_price']:.4f}, 时间={bi['dt']}")
            
            # 显示结构分析
            structure_analysis = czsc_structure.get('structure_analysis', {})
            if structure_analysis:
                print(f"\n🎯 结构分析:")
                print(f"  当前趋势: {structure_analysis.get('current_trend', '未知')}")
                print(f"  结构强度: {structure_analysis.get('structure_strength', 0.5):.2f}")
                
                signals = structure_analysis.get('structure_signals', [])
                if signals:
                    print(f"  结构信号: {', '.join(signals)}")
            
            # 测试LLM分析器构建的提示词
            print("\n🤖 测试LLM分析器...")
            from analyzers.llm_market_analyzer import LLMMarketAnalyzer
            
            llm_analyzer = LLMMarketAnalyzer()
            
            # 准备市场数据
            market_data = {
                'fund_code': test_fund_code,
                'czsc_structure': czsc_structure,
                'technical_analysis': {'indicators': {'ma5': 2.12, 'rsi': 68.5}},
                'gua_data': {'gua_score': -0.006, 'is_sell_gua': True}
            }
            
            # 构建提示词
            prompt = llm_analyzer._build_analysis_prompt(market_data, test_fund_code)
            
            print("✅ LLM提示词构建成功!")
            print("\n📝 提示词片段 (CZSC结构部分):")
            
            # 提取CZSC相关的提示词部分
            lines = prompt.split('\n')
            czsc_section = False
            for line in lines:
                if 'CZSC缠论结构分析' in line:
                    czsc_section = True
                elif czsc_section and line.strip() == '':
                    if any(keyword in ''.join(lines[lines.index(line):lines.index(line)+5]) 
                          for keyword in ['请基于', '技术指标', '卦象']):
                        break
                
                if czsc_section:
                    print(f"  {line}")
            
        else:
            print("❌ CZSC结构数据提取失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n🏁 CZSC结构测试完成")

if __name__ == '__main__':
    test_czsc_structure()