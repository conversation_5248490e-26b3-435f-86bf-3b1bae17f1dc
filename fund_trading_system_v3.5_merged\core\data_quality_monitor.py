"""
Data Quality Monitoring System

Comprehensive data quality monitoring, alerting, and reporting system
for the trading system.
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import pandas as pd
import numpy as np
from enum import Enum


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class QualityAlert:
    """Data quality alert"""
    timestamp: datetime
    level: AlertLevel
    source: str
    symbol: str
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class QualityMetrics:
    """Data quality metrics for a symbol"""
    symbol: str
    total_records: int = 0
    valid_records: int = 0
    missing_values: int = 0
    outliers: int = 0
    duplicates: int = 0
    late_arrivals: int = 0
    quality_score: float = 1.0
    last_update: Optional[datetime] = None
    
    @property
    def validity_rate(self) -> float:
        """Calculate validity rate"""
        return self.valid_records / max(self.total_records, 1)
    
    @property
    def completeness_rate(self) -> float:
        """Calculate completeness rate"""
        return (self.total_records - self.missing_values) / max(self.total_records, 1)


class DataQualityRule:
    """Base class for data quality rules"""
    
    def __init__(self, rule_name: str, severity: AlertLevel = AlertLevel.WARNING):
        self.rule_name = rule_name
        self.severity = severity
        self.enabled = True
    
    def check(self, data: Dict[str, Any], symbol: str) -> Tuple[bool, Optional[str]]:
        """
        Check data quality rule
        
        Args:
            data: Data to check
            symbol: Symbol being checked
            
        Returns:
            Tuple of (passed, error_message)
        """
        raise NotImplementedError


class PriceRangeRule(DataQualityRule):
    """Check if prices are within reasonable ranges"""
    
    def __init__(self, min_price: float = 0.01, max_price: float = 10000.0):
        super().__init__("PriceRange", AlertLevel.ERROR)
        self.min_price = min_price
        self.max_price = max_price
    
    def check(self, data: Dict[str, Any], symbol: str) -> Tuple[bool, Optional[str]]:
        price_fields = ['open', 'high', 'low', 'close']
        
        for field in price_fields:
            if field in data:
                price = data[field]
                if not isinstance(price, (int, float)):
                    return False, f"Invalid price type for {field}: {type(price)}"
                
                if price < self.min_price or price > self.max_price:
                    return False, f"Price {field}={price} outside range [{self.min_price}, {self.max_price}]"
        
        return True, None


class VolumeValidityRule(DataQualityRule):
    """Check volume validity"""
    
    def __init__(self):
        super().__init__("VolumeValidity", AlertLevel.WARNING)
    
    def check(self, data: Dict[str, Any], symbol: str) -> Tuple[bool, Optional[str]]:
        if 'volume' in data:
            volume = data['volume']
            if not isinstance(volume, (int, float)):
                return False, f"Invalid volume type: {type(volume)}"
            
            if volume < 0:
                return False, f"Negative volume: {volume}"
        
        return True, None


class OHLCConsistencyRule(DataQualityRule):
    """Check OHLC price consistency"""
    
    def __init__(self):
        super().__init__("OHLCConsistency", AlertLevel.ERROR)
    
    def check(self, data: Dict[str, Any], symbol: str) -> Tuple[bool, Optional[str]]:
        required_fields = ['open', 'high', 'low', 'close']
        
        # Check if all fields are present
        missing_fields = [f for f in required_fields if f not in data]
        if missing_fields:
            return False, f"Missing OHLC fields: {missing_fields}"
        
        try:
            open_price = float(data['open'])
            high_price = float(data['high'])
            low_price = float(data['low'])
            close_price = float(data['close'])
            
            # Check consistency
            if high_price < low_price:
                return False, f"High ({high_price}) < Low ({low_price})"
            
            if open_price > high_price or open_price < low_price:
                return False, f"Open ({open_price}) outside High-Low range"
            
            if close_price > high_price or close_price < low_price:
                return False, f"Close ({close_price}) outside High-Low range"
            
        except (ValueError, TypeError) as e:
            return False, f"OHLC conversion error: {e}"
        
        return True, None


class TimestampFreshnessRule(DataQualityRule):
    """Check timestamp freshness"""
    
    def __init__(self, max_delay_minutes: int = 15):
        super().__init__("TimestampFreshness", AlertLevel.WARNING)
        self.max_delay_minutes = max_delay_minutes
    
    def check(self, data: Dict[str, Any], symbol: str) -> Tuple[bool, Optional[str]]:
        if 'timestamp' in data:
            try:
                if isinstance(data['timestamp'], str):
                    timestamp = pd.to_datetime(data['timestamp'])
                else:
                    timestamp = data['timestamp']
                
                delay = datetime.now() - timestamp
                if delay.total_seconds() > self.max_delay_minutes * 60:
                    return False, f"Data too old: {delay.total_seconds():.0f} seconds"
                    
            except Exception as e:
                return False, f"Timestamp parsing error: {e}"
        
        return True, None


class DataQualityMonitor:
    """
    Comprehensive data quality monitoring system
    
    Features:
    - Real-time quality checking with configurable rules
    - Quality metrics tracking and reporting
    - Alert generation and management
    - Historical quality trend analysis
    - Automated quality scoring
    """
    
    def __init__(self, 
                 alert_retention_days: int = 30,
                 metrics_retention_days: int = 90):
        """
        Initialize data quality monitor
        
        Args:
            alert_retention_days: How long to keep alerts
            metrics_retention_days: How long to keep metrics
        """
        self.alert_retention_days = alert_retention_days
        self.metrics_retention_days = metrics_retention_days
        
        # Quality rules
        self.rules: List[DataQualityRule] = []
        self._setup_default_rules()
        
        # Metrics storage
        self.metrics: Dict[str, QualityMetrics] = {}
        self.historical_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Alerts
        self.alerts: List[QualityAlert] = []
        self.alert_callbacks: List[Callable[[QualityAlert], None]] = []
        
        # Statistics
        self.stats = {
            'total_checks': 0,
            'passed_checks': 0,
            'failed_checks': 0,
            'alerts_generated': 0,
            'last_check': None
        }
        
        # Threading
        self._lock = threading.RLock()
        self._cleanup_thread = None
        self._start_cleanup_thread()
        
        # Logger
        self.logger = logging.getLogger("DataQualityMonitor")
        self.logger.info("Data quality monitor initialized")
    
    def add_rule(self, rule: DataQualityRule) -> None:
        """Add a quality rule"""
        with self._lock:
            self.rules.append(rule)
            self.logger.info(f"Added quality rule: {rule.rule_name}")
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove a quality rule"""
        with self._lock:
            for i, rule in enumerate(self.rules):
                if rule.rule_name == rule_name:
                    del self.rules[i]
                    self.logger.info(f"Removed quality rule: {rule_name}")
                    return True
            return False
    
    def add_alert_callback(self, callback: Callable[[QualityAlert], None]) -> None:
        """Add alert callback function"""
        self.alert_callbacks.append(callback)
    
    def check_data_quality(self, data: Dict[str, Any], symbol: str, source: str = "unknown") -> float:
        """
        Check data quality and return quality score
        
        Args:
            data: Data to check
            symbol: Symbol being checked
            source: Data source
            
        Returns:
            Quality score (0.0 to 1.0)
        """
        with self._lock:
            self.stats['total_checks'] += 1
            self.stats['last_check'] = datetime.now()
            
            # Initialize metrics if not exists
            if symbol not in self.metrics:
                self.metrics[symbol] = QualityMetrics(symbol=symbol)
            
            metrics = self.metrics[symbol]
            metrics.total_records += 1
            metrics.last_update = datetime.now()
            
            # Run quality rules
            passed_rules = 0
            total_rules = len([r for r in self.rules if r.enabled])
            
            for rule in self.rules:
                if not rule.enabled:
                    continue
                
                try:
                    passed, error_message = rule.check(data, symbol)
                    
                    if passed:
                        passed_rules += 1
                    else:
                        # Generate alert
                        alert = QualityAlert(
                            timestamp=datetime.now(),
                            level=rule.severity,
                            source=source,
                            symbol=symbol,
                            message=f"Rule '{rule.rule_name}' failed: {error_message}",
                            details={'rule': rule.rule_name, 'data': data}
                        )
                        self._add_alert(alert)
                        
                        # Update metrics
                        if rule.severity in [AlertLevel.ERROR, AlertLevel.CRITICAL]:
                            metrics.outliers += 1
                        
                except Exception as e:
                    self.logger.error(f"Error checking rule {rule.rule_name}: {e}")
            
            # Calculate quality score
            if total_rules > 0:
                rule_score = passed_rules / total_rules
            else:
                rule_score = 1.0
            
            # Additional quality factors
            completeness_score = self._calculate_completeness_score(data)
            freshness_score = self._calculate_freshness_score(data)
            
            # Combined quality score
            quality_score = (rule_score * 0.6 + completeness_score * 0.3 + freshness_score * 0.1)
            
            # Update metrics
            if quality_score >= 0.8:
                metrics.valid_records += 1
                self.stats['passed_checks'] += 1
            else:
                self.stats['failed_checks'] += 1
            
            metrics.quality_score = quality_score
            
            # Store historical metrics
            self.historical_metrics[symbol].append({
                'timestamp': datetime.now(),
                'quality_score': quality_score,
                'total_records': metrics.total_records,
                'valid_records': metrics.valid_records
            })
            
            return quality_score
    
    def get_quality_metrics(self, symbol: str) -> Optional[QualityMetrics]:
        """Get quality metrics for symbol"""
        return self.metrics.get(symbol)
    
    def get_all_metrics(self) -> Dict[str, QualityMetrics]:
        """Get all quality metrics"""
        return self.metrics.copy()
    
    def get_quality_trend(self, symbol: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get quality trend for symbol"""
        if symbol not in self.historical_metrics:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [
            metric for metric in self.historical_metrics[symbol]
            if metric['timestamp'] >= cutoff_time
        ]
    
    def get_active_alerts(self, level: Optional[AlertLevel] = None) -> List[QualityAlert]:
        """Get active alerts"""
        alerts = [alert for alert in self.alerts if not alert.resolved]
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def resolve_alert(self, alert_index: int) -> bool:
        """Resolve an alert"""
        try:
            if 0 <= alert_index < len(self.alerts):
                self.alerts[alert_index].resolved = True
                self.alerts[alert_index].resolution_time = datetime.now()
                return True
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
        
        return False
    
    def get_quality_report(self) -> Dict[str, Any]:
        """Generate comprehensive quality report"""
        with self._lock:
            # Overall statistics
            total_checks = self.stats['total_checks']
            pass_rate = self.stats['passed_checks'] / max(total_checks, 1)
            
            # Symbol-level metrics
            symbol_metrics = {}
            for symbol, metrics in self.metrics.items():
                symbol_metrics[symbol] = {
                    'total_records': metrics.total_records,
                    'validity_rate': metrics.validity_rate,
                    'completeness_rate': metrics.completeness_rate,
                    'quality_score': metrics.quality_score,
                    'last_update': metrics.last_update.isoformat() if metrics.last_update else None
                }
            
            # Alert summary
            alert_summary = {}
            for level in AlertLevel:
                alert_summary[level.value] = len([
                    a for a in self.alerts 
                    if a.level == level and not a.resolved
                ])
            
            return {
                'overall_stats': {
                    'total_checks': total_checks,
                    'pass_rate': pass_rate,
                    'total_symbols': len(self.metrics),
                    'active_rules': len([r for r in self.rules if r.enabled])
                },
                'symbol_metrics': symbol_metrics,
                'alert_summary': alert_summary,
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp.isoformat(),
                        'level': alert.level.value,
                        'symbol': alert.symbol,
                        'message': alert.message
                    }
                    for alert in self.get_active_alerts()[:10]
                ],
                'generated_at': datetime.now().isoformat()
            }
    
    def _setup_default_rules(self) -> None:
        """Setup default quality rules"""
        self.rules = [
            PriceRangeRule(),
            VolumeValidityRule(),
            OHLCConsistencyRule(),
            TimestampFreshnessRule()
        ]
    
    def _add_alert(self, alert: QualityAlert) -> None:
        """Add alert and notify callbacks"""
        self.alerts.append(alert)
        self.stats['alerts_generated'] += 1
        
        # Notify callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
        
        self.logger.warning(f"Quality alert: {alert.message}")
    
    def _calculate_completeness_score(self, data: Dict[str, Any]) -> float:
        """Calculate data completeness score"""
        required_fields = ['open', 'high', 'low', 'close', 'volume']
        present_fields = sum(1 for field in required_fields if field in data and data[field] is not None)
        return present_fields / len(required_fields)
    
    def _calculate_freshness_score(self, data: Dict[str, Any]) -> float:
        """Calculate data freshness score"""
        if 'timestamp' not in data:
            return 0.5
        
        try:
            if isinstance(data['timestamp'], str):
                timestamp = pd.to_datetime(data['timestamp'])
            else:
                timestamp = data['timestamp']
            
            age_minutes = (datetime.now() - timestamp).total_seconds() / 60
            
            if age_minutes <= 5:
                return 1.0
            elif age_minutes <= 15:
                return 0.8
            elif age_minutes <= 60:
                return 0.6
            else:
                return 0.3
                
        except Exception:
            return 0.5
    
    def _start_cleanup_thread(self) -> None:
        """Start background cleanup thread"""
        def cleanup_loop():
            while True:
                try:
                    time.sleep(3600)  # Run every hour
                    self._cleanup_old_data()
                except Exception as e:
                    self.logger.error(f"Cleanup thread error: {e}")
        
        self._cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        self._cleanup_thread.start()
    
    def _cleanup_old_data(self) -> None:
        """Cleanup old alerts and metrics"""
        with self._lock:
            # Cleanup old alerts
            cutoff_time = datetime.now() - timedelta(days=self.alert_retention_days)
            self.alerts = [alert for alert in self.alerts if alert.timestamp >= cutoff_time]
            
            # Cleanup old historical metrics
            metrics_cutoff = datetime.now() - timedelta(days=self.metrics_retention_days)
            for symbol in self.historical_metrics:
                metrics_deque = self.historical_metrics[symbol]
                # Keep only recent metrics
                while metrics_deque and metrics_deque[0]['timestamp'] < metrics_cutoff:
                    metrics_deque.popleft()
            
            self.logger.debug("Completed data quality cleanup")
