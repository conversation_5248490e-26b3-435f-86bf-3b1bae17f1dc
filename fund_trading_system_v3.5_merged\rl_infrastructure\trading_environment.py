"""
Trading Environment for Reinforcement Learning

Implements a gym-compatible trading environment for training RL agents
with 50-dimensional state space and comprehensive reward mechanisms.
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
import json

try:
    import gym
    from gym import spaces
    GYM_AVAILABLE = True
except ImportError:
    GYM_AVAILABLE = False
    # Create minimal gym-like interface
    class spaces:
        class Box:
            def __init__(self, low, high, shape, dtype=np.float32):
                self.low = np.array(low)
                self.high = np.array(high)
                self.shape = shape
                self.dtype = dtype
                
        class Discrete:
            def __init__(self, n):
                self.n = n

from core.data_structures import MarketData, TradingSignal
from core.config import TradingConfig


@dataclass
class TradingState:
    """Enhanced trading state representation"""
    # Market data
    market_data: MarketData
    
    # Portfolio state
    cash: float
    position: float  # Current position size (-1 to 1, normalized)
    portfolio_value: float
    
    # Technical features (20-dimensional)
    technical_features: np.ndarray
    
    # CZSC features (10-dimensional)  
    czsc_features: np.ndarray
    
    # LLM context features (20-dimensional)
    llm_features: np.ndarray
    
    # Historical context
    price_history: List[float]
    return_history: List[float]
    
    # Risk metrics
    drawdown: float
    volatility: float
    
    def to_vector(self) -> np.ndarray:
        """Convert state to 50-dimensional feature vector"""
        # Combine all features into single vector
        state_vector = np.concatenate([
            self.technical_features,  # 20 dims
            self.czsc_features,       # 10 dims  
            self.llm_features,        # 20 dims
        ])
        

        # Ensure exactly 50 dimensions
        assert len(state_vector) == 50, f"State vector must be 50-dimensional, got {len(state_vector)}"
        
        return state_vector.astype(np.float32)


@dataclass 
class TradingReward:
    """Comprehensive reward structure"""
    # Primary rewards
    profit_reward: float = 0.0      # Direct profit/loss
    risk_adjusted_reward: float = 0.0  # Risk-adjusted return
    
    # Secondary rewards
    transaction_cost: float = 0.0   # Transaction costs penalty
    holding_reward: float = 0.0     # Reward for maintaining positions
    diversification_reward: float = 0.0  # Portfolio diversification
    
    # Risk penalties
    drawdown_penalty: float = 0.0   # Maximum drawdown penalty
    volatility_penalty: float = 0.0  # Excess volatility penalty
    concentration_penalty: float = 0.0  # Position concentration penalty
    
    def total_reward(self) -> float:
        """Calculate total reward"""
        return (self.profit_reward + 
                self.risk_adjusted_reward + 
                self.holding_reward + 
                self.diversification_reward - 
                self.transaction_cost - 
                self.drawdown_penalty - 
                self.volatility_penalty - 
                self.concentration_penalty)


class TradingEnvironment:
    """
    RL Trading Environment for Training Agents
    
    Provides a gym-compatible interface for training reinforcement learning
    agents on historical market data with realistic trading mechanics.
    """
    
    def __init__(self, 
                 data: pd.DataFrame,
                 config: Optional[TradingConfig] = None,
                 initial_capital: float = 100000.0,
                 transaction_cost: float = 0.0003,
                 max_position: float = 1.0,
                 lookback_window: int = 20):
        """
        Initialize trading environment.
        
        Args:
            data: Historical market data
            config: Trading system configuration
            initial_capital: Starting capital amount
            transaction_cost: Transaction cost rate
            max_position: Maximum position size (as fraction of capital)
            lookback_window: Number of historical periods to include in state
        """
        self.logger = logging.getLogger("TradingEnvironment")
        
        # Configuration
        self.config = config or TradingConfig()
        self.initial_capital = initial_capital
        self.transaction_cost = transaction_cost
        self.max_position = max_position
        self.lookback_window = lookback_window
        
        # Market data
        self.data = self._prepare_data(data)
        # 动态调整lookback_window，保证至少有1步
        min_lookback = 1
        max_lookback = max(min_lookback, len(self.data) - 1)
        if lookback_window > max_lookback:
            self.logger.warning(f"lookback_window({lookback_window}) > max possible({max_lookback})，自动调整为{max_lookback}")
            self.lookback_window = max_lookback
        else:
            self.lookback_window = lookback_window
        self.total_steps = len(self.data) - self.lookback_window
        if self.total_steps <= 0:
            raise ValueError(f"Insufficient data: need at least {self.lookback_window + 1} records, "
                           f"got {len(self.data)} records. "
                           f"Calculated steps: {self.total_steps}")
        if self.total_steps < 10:
            self.logger.warning(f"可用步数较少，仅{self.total_steps}步，建议增加数据量或减少lookback_window")
        
        # Define action and observation spaces
        self.action_space = spaces.Discrete(3)  # 0: Hold, 1: Buy, 2: Sell
        
        # 50-dimensional observation space
        self.observation_space = spaces.Box(
            low=-np.inf, 
            high=np.inf, 
            shape=(50,), 
            dtype=np.float32
        )
        
        # Environment state
        self.current_step = 0
        self.cash = initial_capital
        self.shares = 0.0
        self.portfolio_value = initial_capital
        
        # Trading history
        self.trade_history = []
        self.portfolio_history = []
        self.reward_history = []
        
        # Performance tracking
        self.total_rewards = 0.0
        self.max_portfolio_value = initial_capital
        self.max_drawdown = 0.0
        
        self.logger.info(f"Trading environment initialized with {self.total_steps} steps")
    
    def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare and validate market data"""
        df = data.copy()
        
        # Ensure required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Sort by date
        if 'date' in df.columns:
            df = df.sort_values('date').reset_index(drop=True)
        
        # Calculate technical indicators
        df = self._add_technical_indicators(df)
        
        return df
    
    def _add_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators for feature engineering"""
        df = data.copy()
        
        # Price-based indicators
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            df[f'ma_{window}'] = df['close'].rolling(window).mean()
            df[f'ma_ratio_{window}'] = df['close'] / df[f'ma_{window}']
        
        # Volatility measures
        df['volatility_10'] = df['returns'].rolling(10).std()
        df['volatility_20'] = df['returns'].rolling(20).std()
        
        # Momentum indicators
        df['rsi'] = self._calculate_rsi(df['close'], 14)
        df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
        
        # Volume indicators
        df['volume_ma'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_ma']
        
        # Price position indicators
        df['high_low_ratio'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        df['price_position'] = (df['close'] - df['close'].rolling(20).min()) / (
            df['close'].rolling(20).max() - df['close'].rolling(20).min()
        )
        
        return df
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, 
                       fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """Calculate MACD and signal line"""
        exp1 = prices.ewm(span=fast).mean()
        exp2 = prices.ewm(span=slow).mean()
        macd = exp1 - exp2
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def reset(self) -> np.ndarray:
        """Reset environment to initial state"""
        self.current_step = 0
        self.cash = self.initial_capital
        self.shares = 0.0
        self.portfolio_value = self.initial_capital
        
        # Clear history
        self.trade_history.clear()
        self.portfolio_history.clear()
        self.reward_history.clear()
        
        # Reset performance tracking
        self.total_rewards = 0.0
        self.max_portfolio_value = self.initial_capital
        self.max_drawdown = 0.0
        
        # Get initial state
        state = self._get_state()
        
        self.logger.debug("Environment reset")
        return state.to_vector()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """
        Execute one trading step.
        
        Args:
            action: Trading action (0: Hold, 1: Buy, 2: Sell)
            
        Returns:
            observation: Next state observation
            reward: Reward for this step
            done: Whether episode is finished
            info: Additional information
        """
        if self.current_step >= self.total_steps:
            raise ValueError("Environment has finished, call reset()")
        
        # Execute action
        reward_info = self._execute_action(action)
        
        # Update portfolio value
        current_price = self.data.iloc[self.current_step + self.lookback_window]['close']
        self.portfolio_value = self.cash + self.shares * current_price
        
        # Calculate reward
        reward = self._calculate_reward(action, reward_info)
        
        # Update tracking
        self.total_rewards += reward.total_reward()
        self.max_portfolio_value = max(self.max_portfolio_value, self.portfolio_value)
        
        # Update drawdown
        drawdown = (self.max_portfolio_value - self.portfolio_value) / self.max_portfolio_value
        self.max_drawdown = max(self.max_drawdown, drawdown)
        
        # Store history
        self.portfolio_history.append(self.portfolio_value)
        self.reward_history.append(reward.total_reward())
        
        # Move to next step
        self.current_step += 1
        
        # Check if episode is done
        done = (self.current_step >= self.total_steps or 
                self.portfolio_value <= 0.1 * self.initial_capital)  # Stop loss at 90% loss
        
        # Get next state
        if not done:
            next_state = self._get_state()
            observation = next_state.to_vector()
        else:
            observation = np.zeros(50, dtype=np.float32)
        
        # Prepare info
        info = {
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'shares': self.shares,
            'total_rewards': self.total_rewards,
            'max_drawdown': self.max_drawdown,
            'current_price': current_price,
            'step': self.current_step,
            'reward_breakdown': {
                'profit': reward.profit_reward,
                'risk_adjusted': reward.risk_adjusted_reward,
                'transaction_cost': reward.transaction_cost,
                'drawdown_penalty': reward.drawdown_penalty
            }
        }
        
        return observation, reward.total_reward(), done, info
    
    def _execute_action(self, action: int) -> Dict[str, float]:
        """Execute trading action and return execution info"""
        current_price = self.data.iloc[self.current_step + self.lookback_window]['close']
        old_shares = self.shares
        transaction_cost = 0.0
        
        if action == 1:  # Buy
            # Calculate maximum possible shares to buy
            available_cash = self.cash * 0.95  # Keep 5% cash buffer
            max_shares = available_cash / current_price
            
            # Apply position size limit
            max_position_value = self.portfolio_value * self.max_position
            max_shares_by_position = max_position_value / current_price
            
            shares_to_buy = min(max_shares, max_shares_by_position - self.shares)
            shares_to_buy = max(0, shares_to_buy)  # Ensure non-negative
            
            if shares_to_buy > 0:
                cost = shares_to_buy * current_price
                transaction_cost = cost * self.transaction_cost
                
                self.shares += shares_to_buy
                self.cash -= (cost + transaction_cost)
                
                self.trade_history.append({
                    'step': self.current_step,
                    'action': 'buy',
                    'shares': shares_to_buy,
                    'price': current_price,
                    'cost': cost,
                    'transaction_cost': transaction_cost
                })
        
        elif action == 2:  # Sell
            if self.shares > 0:
                shares_to_sell = self.shares
                revenue = shares_to_sell * current_price
                transaction_cost = revenue * self.transaction_cost
                
                self.shares = 0.0
                self.cash += (revenue - transaction_cost)
                
                self.trade_history.append({
                    'step': self.current_step,
                    'action': 'sell',
                    'shares': shares_to_sell,
                    'price': current_price,
                    'revenue': revenue,
                    'transaction_cost': transaction_cost
                })
        
        # Action 0 (Hold) requires no execution
        
        return {
            'old_shares': old_shares,
            'new_shares': self.shares,
            'transaction_cost': transaction_cost,
            'current_price': current_price
        }
    
    def _calculate_reward(self, action: int, execution_info: Dict[str, float]) -> TradingReward:
        """Calculate comprehensive reward for the action"""
        reward = TradingReward()
        
        # Calculate profit reward
        if len(self.portfolio_history) > 0:
            portfolio_change = self.portfolio_value - self.portfolio_history[-1]
            reward.profit_reward = portfolio_change / self.initial_capital * 100  # Scaled
        
        # Transaction cost penalty
        reward.transaction_cost = execution_info['transaction_cost'] / self.initial_capital * 100
        
        # Risk-adjusted reward (Sharpe-like)
        if len(self.portfolio_history) >= 10:
            returns = np.diff(self.portfolio_history[-10:]) / self.portfolio_history[-10:-1]
            if np.std(returns) > 0:
                reward.risk_adjusted_reward = np.mean(returns) / np.std(returns) * 0.1
        
        # Drawdown penalty
        current_drawdown = (self.max_portfolio_value - self.portfolio_value) / self.max_portfolio_value
        if current_drawdown > 0.1:  # Penalty for drawdown > 10%
            reward.drawdown_penalty = current_drawdown * 10
        
        # Holding reward (encourage staying in market when profitable)
        if action == 0 and self.shares > 0:  # Holding with position
            recent_return = self.portfolio_value / self.initial_capital - 1.0
            if recent_return > 0:
                reward.holding_reward = 0.01  # Small positive reward
        
        # Volatility penalty (discourage excessive trading)
        if len(self.trade_history) >= 5:
            recent_trades = self.trade_history[-5:]
            trade_frequency = len(recent_trades) / 5
            if trade_frequency > 0.8:  # More than 4 trades in 5 steps
                reward.volatility_penalty = 0.1
        
        return reward
    
    def _get_state(self) -> TradingState:
        """Get current environment state"""
        current_idx = self.current_step + self.lookback_window
        current_row = self.data.iloc[current_idx]
        
        # Create market data
        market_data = MarketData(
            symbol='ENV_TRAINING',
            timestamp=datetime.now(),
            open=current_row['open'],
            high=current_row['high'],
            low=current_row['low'],
            close=current_row['close'],
            volume=current_row['volume']
        )
        
        # Technical features (20-dimensional)
        technical_features = np.array([
            current_row.get('ma_ratio_5', 1.0),
            current_row.get('ma_ratio_10', 1.0),
            current_row.get('ma_ratio_20', 1.0),
            current_row.get('ma_ratio_50', 1.0),
            current_row.get('rsi', 50.0) / 100.0,  # Normalize RSI
            current_row.get('macd', 0.0),
            current_row.get('macd_signal', 0.0),
            current_row.get('volatility_10', 0.02),
            current_row.get('volatility_20', 0.02),
            current_row.get('volume_ratio', 1.0),
            current_row.get('high_low_ratio', 0.5),
            current_row.get('price_position', 0.5),
            current_row.get('returns', 0.0),
            self.cash / self.initial_capital,  # Cash ratio
            self.shares * current_row['close'] / self.initial_capital,  # Position ratio
            self.portfolio_value / self.initial_capital,  # Portfolio ratio
            self.max_drawdown,  # Current max drawdown
            len(self.trade_history) / max(1, self.current_step),  # Trade frequency
            min(1.0, self.current_step / self.total_steps),  # Progress ratio
            1.0 if self.shares > 0 else 0.0  # Has position indicator
        ])
        
        # CZSC features (10-dimensional) - Simplified for training
        czsc_features = np.array([
            1.0 if current_row['close'] > current_row['open'] else -1.0,  # Bar direction
            current_row.get('high_low_ratio', 0.5),  # Price position in bar
            current_row.get('price_position', 0.5),  # Price position in recent range
            np.tanh((current_row['close'] - current_row.get('ma_20', current_row['close'])) / current_row['close']),  # Trend
            np.tanh(current_row.get('rsi', 50.0) / 100.0 - 0.5),  # RSI normalized
            0.0,  # Bi direction (placeholder)
            0.5,  # Bi strength (placeholder)
            1.0,  # Duan level (placeholder)
            0.0,  # Zhongshu status (placeholder)
            np.tanh(current_row.get('returns', 0.0) * 10)  # Return momentum
        ])
        
        # LLM features (20-dimensional) - Market context simulation
        llm_features = np.array([
            np.sin(2 * np.pi * self.current_step / 252),  # Seasonal pattern
            np.cos(2 * np.pi * self.current_step / 252),  # Seasonal pattern
            np.tanh(current_row.get('volatility_20', 0.02) * 50),  # Market volatility
            np.tanh(current_row.get('volume_ratio', 1.0) - 1.0),  # Volume anomaly
            0.0,  # Sentiment score (placeholder)
            0.0,  # News impact (placeholder)
            0.0,  # Market regime (placeholder)
            np.tanh(current_row.get('returns', 0.0) * 20),  # Recent momentum
            0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0  # Additional context (placeholders)
        ])
        
        # Price and return history
        start_idx = max(0, current_idx - self.lookback_window)
        price_history = self.data.iloc[start_idx:current_idx]['close'].tolist()
        return_history = self.data.iloc[start_idx:current_idx]['returns'].fillna(0).tolist()
        
        return TradingState(
            market_data=market_data,
            cash=self.cash,
            position=self.shares * current_row['close'] / self.initial_capital,
            portfolio_value=self.portfolio_value,
            technical_features=technical_features,
            czsc_features=czsc_features,
            llm_features=llm_features,
            price_history=price_history,
            return_history=return_history,
            drawdown=self.max_drawdown,
            volatility=current_row.get('volatility_20', 0.02)
        )
    
    def render(self, mode: str = 'human'):
        """Render environment state (for debugging)"""
        if mode == 'human':
            current_price = self.data.iloc[self.current_step + self.lookback_window]['close']
            print(f"Step: {self.current_step}/{self.total_steps}")
            print(f"Price: ${current_price:.2f}")
            print(f"Portfolio: ${self.portfolio_value:.2f}")
            print(f"Cash: ${self.cash:.2f}")
            print(f"Shares: {self.shares:.2f}")
            print(f"Drawdown: {self.max_drawdown:.2%}")
            print(f"Total Reward: {self.total_rewards:.4f}")
            print("-" * 40)
    
    def get_portfolio_stats(self) -> Dict[str, Any]:
        """Get comprehensive portfolio statistics"""
        if len(self.portfolio_history) == 0:
            return {}
        
        portfolio_array = np.array(self.portfolio_history)
        returns = np.diff(portfolio_array) / portfolio_array[:-1]
        
        total_return = (self.portfolio_value - self.initial_capital) / self.initial_capital
        
        if len(returns) > 1:
            volatility = np.std(returns) * np.sqrt(252)  # Annualized
            sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        else:
            volatility = 0
            sharpe_ratio = 0
        
        return {
            'total_return': total_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': self.max_drawdown,
            'total_trades': len(self.trade_history),
            'portfolio_value': self.portfolio_value,
            'total_rewards': self.total_rewards
        }
    
    def save_episode_data(self, filename: str):
        """Save episode data for analysis"""
        episode_data = {
            'config': {
                'initial_capital': self.initial_capital,
                'transaction_cost': self.transaction_cost,
                'max_position': self.max_position,
                'total_steps': self.total_steps
            },
            'portfolio_history': self.portfolio_history,
            'reward_history': self.reward_history,
            'trade_history': self.trade_history,
            'final_stats': self.get_portfolio_stats()
        }
        
        with open(filename, 'w') as f:
            json.dump(episode_data, f, indent=2)
        
        self.logger.info(f"Episode data saved to {filename}") 