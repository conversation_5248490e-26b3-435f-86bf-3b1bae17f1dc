"""
安装脚本 - 解决PyArrow兼容性问题
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并显示输出"""
    print(f"执行: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"成功: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e.stderr}")
        return False

def main():
    """主安装流程"""
    print("=== 安装深度强化学习环境 ===")
    
    # 方案1: 降级PyArrow (推荐)
    print("\n1. 尝试降级PyArrow到兼容版本...")
    if run_command("pip install pyarrow==10.0.1"):
        print("PyArrow降级成功，可以继续使用PARL")
        
        # 安装其他依赖
        commands = [
            "pip install paddlepaddle-gpu==2.4.1",
            "pip install parl==2.0.4",
            "pip install gymnasium",
            "pip install pandas numpy matplotlib"
        ]
        
        for cmd in commands:
            run_command(cmd)
            
        print("\n=== PARL环境安装完成 ===")
        print("现在可以运行 enhanced_rl_stock_base.py")
        
    else:
        print("PyArrow降级失败，使用Stable-Baselines3方案")
        
        # 方案2: 安装Stable-Baselines3
        print("\n2. 安装Stable-Baselines3环境...")
        commands = [
            "pip install stable-baselines3[extra]",
            "pip install gymnasium[atari,accept-rom-license]",
            "pip install pandas numpy matplotlib",
            "pip install tensorboard"
        ]
        
        for cmd in commands:
            run_command(cmd)
            
        print("\n=== Stable-Baselines3环境安装完成 ===")
        print("现在可以运行 sb3_stock_trading.py")
    
    # 创建目录
    os.makedirs("models", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    os.makedirs("sb3_tensorboard", exist_ok=True)
    
    print("\n=== 安装完成 ===")
    print("推荐使用方案:")
    print("1. 如果PyArrow降级成功: python enhanced_rl_stock_base.py")
    print("2. 如果使用SB3方案: python sb3_stock_trading.py")

if __name__ == "__main__":
    main()