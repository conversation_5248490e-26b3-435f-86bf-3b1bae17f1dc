"""
Performance Analyzer for Backtesting

Calculates comprehensive performance metrics and generates detailed
backtest reports with visualizations.
"""

import logging
import numpy as np
import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    # Return metrics
    total_return: float
    annualized_return: float
    cumulative_return: float
    
    # Risk metrics
    volatility: float
    max_drawdown: float
    max_drawdown_duration: int
    var_95: float  # Value at Risk (95%)
    
    # Risk-adjusted returns
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    
    # Trading metrics
    total_trades: int
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    
    # Additional metrics
    beta: float = 0.0
    alpha: float = 0.0
    information_ratio: float = 0.0
    tracking_error: float = 0.0


class PerformanceAnalyzer:
    """
    Performance Analyzer for Backtesting Engine
    
    Calculates comprehensive performance metrics, generates reports,
    and creates visualizations for backtest results.
    """
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize the performance analyzer.
        
        Args:
            risk_free_rate: Annual risk-free rate for Sharpe ratio calculation
        """
        self.logger = logging.getLogger("PerformanceAnalyzer")
        self.risk_free_rate = risk_free_rate
        
    def analyze_performance(self, 
                          equity_curve: List[float],
                          daily_returns: List[float],
                          trades: List[Any],
                          benchmark_returns: Optional[List[float]] = None) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics.
        
        Args:
            equity_curve: Daily portfolio values
            daily_returns: Daily return percentages
            trades: List of executed trades
            benchmark_returns: Optional benchmark returns for comparison
            
        Returns:
            PerformanceMetrics object with all calculated metrics
        """
        try:
            if not equity_curve or not daily_returns:
                self.logger.warning("Empty data provided, returning zero metrics")
                return self._zero_metrics()
            
            returns_array = np.array(daily_returns)
            equity_array = np.array(equity_curve)
            
            # Calculate return metrics
            total_return = (equity_array[-1] - equity_array[0]) / equity_array[0]
            cumulative_return = total_return
            annualized_return = self._annualized_return(returns_array)
            
            # Calculate risk metrics
            volatility = self._volatility(returns_array)
            max_drawdown, max_dd_duration = self._max_drawdown(equity_array)
            var_95 = self._value_at_risk(returns_array, 0.95)
            
            # Calculate risk-adjusted returns
            sharpe_ratio = self._sharpe_ratio(returns_array)
            sortino_ratio = self._sortino_ratio(returns_array)
            calmar_ratio = self._calmar_ratio(annualized_return, max_drawdown)
            
            # Calculate trading metrics
            total_trades = len(trades)
            win_rate, profit_factor, avg_win, avg_loss = self._trading_metrics(trades)
            
            # Calculate benchmark-relative metrics
            beta, alpha, information_ratio, tracking_error = self._benchmark_metrics(
                returns_array, benchmark_returns
            )
            
            metrics = PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                cumulative_return=cumulative_return,
                volatility=volatility,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_dd_duration,
                var_95=var_95,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                total_trades=total_trades,
                win_rate=win_rate,
                profit_factor=profit_factor,
                avg_win=avg_win,
                avg_loss=avg_loss,
                beta=beta,
                alpha=alpha,
                information_ratio=information_ratio,
                tracking_error=tracking_error
            )
            
            self.logger.info(f"Performance analysis completed. Total return: {total_return:.2%}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"Performance analysis failed: {e}")
            return self._zero_metrics()
    
    def generate_report(self, 
                       metrics: PerformanceMetrics,
                       equity_curve: List[float],
                       daily_returns: List[float],
                       trades: List[Any],
                       start_date: str,
                       end_date: str,
                       initial_capital: float) -> Dict[str, Any]:
        """
        Generate comprehensive backtest report.
        
        Args:
            metrics: Performance metrics
            equity_curve: Daily portfolio values
            daily_returns: Daily returns
            trades: Trade history
            start_date: Backtest start date
            end_date: Backtest end date
            initial_capital: Initial capital
            
        Returns:
            Dictionary containing the complete report
        """
        report = {
            'summary': {
                'start_date': start_date,
                'end_date': end_date,
                'initial_capital': initial_capital,
                'final_capital': equity_curve[-1] if equity_curve else initial_capital,
                'duration_days': len(equity_curve),
                'generated_at': datetime.now().isoformat()
            },
            'performance_metrics': asdict(metrics),
            'monthly_returns': self._calculate_monthly_returns(daily_returns),
            'yearly_returns': self._calculate_yearly_returns(daily_returns),
            'drawdown_periods': self._analyze_drawdown_periods(equity_curve),
            'trade_analysis': self._analyze_trades(trades),
            'risk_analysis': self._analyze_risk(daily_returns),
            'statistics': self._calculate_statistics(daily_returns, equity_curve)
        }
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str) -> str:
        """Save report to JSON file"""
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Report saved to {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Failed to save report: {e}")
            raise
    
    def create_charts(self, 
                     equity_curve: List[float],
                     daily_returns: List[float],
                     drawdowns: List[float],
                     dates: Optional[List[datetime]] = None,
                     save_path: Optional[str] = None) -> Optional[str]:
        """
        Create performance visualization charts.
        
        Args:
            equity_curve: Daily portfolio values
            daily_returns: Daily returns
            drawdowns: Drawdown series
            dates: Optional date series
            save_path: Optional path to save charts
            
        Returns:
            Path to saved charts or None if matplotlib not available
        """
        if not MATPLOTLIB_AVAILABLE:
            self.logger.warning("Matplotlib not available, skipping chart creation")
            return None
        
        try:
            # Create date series if not provided
            if dates is None:
                dates = pd.date_range(start='2020-01-01', periods=len(equity_curve), freq='D')
            
            # Create figure with subplots
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Backtest Performance Analysis', fontsize=16)
            
            # Equity curve
            axes[0, 0].plot(dates, equity_curve, linewidth=2, color='blue')
            axes[0, 0].set_title('Equity Curve')
            axes[0, 0].set_ylabel('Portfolio Value')
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            
            # Daily returns histogram
            axes[0, 1].hist(daily_returns, bins=50, alpha=0.7, color='green', edgecolor='black')
            axes[0, 1].set_title('Daily Returns Distribution')
            axes[0, 1].set_xlabel('Daily Return')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].grid(True, alpha=0.3)
            
            # Drawdown chart
            axes[1, 0].fill_between(dates, drawdowns, 0, alpha=0.7, color='red')
            axes[1, 0].set_title('Drawdown')
            axes[1, 0].set_ylabel('Drawdown %')
            axes[1, 0].grid(True, alpha=0.3)
            axes[1, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            
            # Rolling Sharpe ratio (30-day window)
            if len(daily_returns) > 30:
                rolling_sharpe = self._calculate_rolling_sharpe(daily_returns, window=30)
                axes[1, 1].plot(dates[29:], rolling_sharpe, linewidth=2, color='purple')
                axes[1, 1].set_title('Rolling 30-Day Sharpe Ratio')
                axes[1, 1].set_ylabel('Sharpe Ratio')
                axes[1, 1].grid(True, alpha=0.3)
                axes[1, 1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            else:
                axes[1, 1].text(0.5, 0.5, 'Insufficient data\nfor rolling Sharpe', 
                               ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('Rolling Sharpe Ratio')
            
            # Adjust layout and save
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"Charts saved to {save_path}")
                plt.close()
                return save_path
            else:
                plt.show()
                return None
                
        except Exception as e:
            self.logger.error(f"Chart creation failed: {e}")
            return None
    
    def _annualized_return(self, returns: np.ndarray) -> float:
        """Calculate annualized return"""
        if len(returns) == 0:
            return 0.0
        return float(np.mean(returns) * 252)  # 252 trading days
    
    def _volatility(self, returns: np.ndarray) -> float:
        """Calculate annualized volatility"""
        if len(returns) == 0:
            return 0.0
        return float(np.std(returns) * np.sqrt(252))
    
    def _max_drawdown(self, equity_curve: np.ndarray) -> Tuple[float, int]:
        """Calculate maximum drawdown and duration"""
        if len(equity_curve) == 0:
            return 0.0, 0
        
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak
        max_dd = float(np.min(drawdown))
        
        # Calculate max drawdown duration
        dd_duration = 0
        max_duration = 0
        for dd in drawdown:
            if dd < 0:
                dd_duration += 1
                max_duration = max(max_duration, dd_duration)
            else:
                dd_duration = 0
        
        return max_dd, max_duration
    
    def _value_at_risk(self, returns: np.ndarray, confidence: float) -> float:
        """Calculate Value at Risk"""
        if len(returns) == 0:
            return 0.0
        return float(np.percentile(returns, (1 - confidence) * 100))
    
    def _sharpe_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - (self.risk_free_rate / 252)  # Daily risk-free rate
        if np.std(excess_returns) == 0:
            return 0.0
        
        return float(np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252))
    
    def _sortino_ratio(self, returns: np.ndarray) -> float:
        """Calculate Sortino ratio"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - (self.risk_free_rate / 252)
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return float('inf')
        
        downside_deviation = np.std(downside_returns)
        if downside_deviation == 0:
            return 0.0
        
        return float(np.mean(excess_returns) / downside_deviation * np.sqrt(252))
    
    def _calmar_ratio(self, annual_return: float, max_drawdown: float) -> float:
        """Calculate Calmar ratio"""
        if max_drawdown == 0:
            return float('inf') if annual_return > 0 else 0.0
        return annual_return / abs(max_drawdown)
    
    def _trading_metrics(self, trades: List[Any]) -> Tuple[float, float, float, float]:
        """Calculate trading-specific metrics"""
        if not trades or len(trades) < 2:
            return 0.0, 0.0, 0.0, 0.0
        
        # Simple analysis assuming trades come in pairs (buy/sell)
        winning_trades = 0
        total_wins = 0
        total_losses = 0
        
        # Group trades into round trips
        for i in range(0, len(trades) - 1, 2):
            if i + 1 < len(trades):
                buy_trade = trades[i]
                sell_trade = trades[i + 1]
                
                if hasattr(buy_trade, 'price') and hasattr(sell_trade, 'price'):
                    pnl = (sell_trade.price - buy_trade.price) * buy_trade.quantity
                    
                    if pnl > 0:
                        winning_trades += 1
                        total_wins += pnl
                    else:
                        total_losses += abs(pnl)
        
        total_trades = len(trades) // 2
        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
        
        avg_win = total_wins / winning_trades if winning_trades > 0 else 0.0
        avg_loss = total_losses / (total_trades - winning_trades) if (total_trades - winning_trades) > 0 else 0.0
        
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        return win_rate, profit_factor, avg_win, avg_loss
    
    def _benchmark_metrics(self, returns: np.ndarray, benchmark_returns: Optional[List[float]]) -> Tuple[float, float, float, float]:
        """Calculate benchmark-relative metrics"""
        if benchmark_returns is None or len(benchmark_returns) != len(returns):
            return 0.0, 0.0, 0.0, 0.0
        
        benchmark_array = np.array(benchmark_returns)
        
        # Beta
        covariance = np.cov(returns, benchmark_array)[0][1]
        benchmark_variance = np.var(benchmark_array)
        beta = covariance / benchmark_variance if benchmark_variance != 0 else 0.0
        
        # Alpha
        alpha = np.mean(returns) - beta * np.mean(benchmark_array)
        
        # Information ratio and tracking error
        active_returns = returns - benchmark_array
        tracking_error = np.std(active_returns) * np.sqrt(252)
        information_ratio = np.mean(active_returns) / np.std(active_returns) * np.sqrt(252) if np.std(active_returns) != 0 else 0.0
        
        return float(beta), float(alpha * 252), float(information_ratio), float(tracking_error)
    
    def _calculate_monthly_returns(self, daily_returns: List[float]) -> Dict[str, float]:
        """Calculate monthly returns breakdown"""
        # Simplified monthly calculation
        if not daily_returns:
            return {}
        
        # Group returns by month (simplified)
        monthly_returns = {}
        chunk_size = 21  # Approximate trading days per month
        
        for i in range(0, len(daily_returns), chunk_size):
            month_returns = daily_returns[i:i+chunk_size]
            month_key = f"Month_{i//chunk_size + 1}"
            monthly_returns[month_key] = float(np.prod([1 + r for r in month_returns]) - 1)
        
        return monthly_returns
    
    def _calculate_yearly_returns(self, daily_returns: List[float]) -> Dict[str, float]:
        """Calculate yearly returns breakdown"""
        # Simplified yearly calculation
        if not daily_returns:
            return {}
        
        yearly_returns = {}
        chunk_size = 252  # Trading days per year
        
        for i in range(0, len(daily_returns), chunk_size):
            year_returns = daily_returns[i:i+chunk_size]
            year_key = f"Year_{i//chunk_size + 1}"
            yearly_returns[year_key] = float(np.prod([1 + r for r in year_returns]) - 1)
        
        return yearly_returns
    
    def _analyze_drawdown_periods(self, equity_curve: List[float]) -> List[Dict[str, Any]]:
        """Analyze drawdown periods"""
        if not equity_curve:
            return []
        
        equity_array = np.array(equity_curve)
        peak = np.maximum.accumulate(equity_array)
        drawdown = (equity_array - peak) / peak
        
        drawdown_periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, dd in enumerate(drawdown):
            if dd < 0 and not in_drawdown:
                in_drawdown = True
                start_idx = i
            elif dd >= 0 and in_drawdown:
                in_drawdown = False
                drawdown_periods.append({
                    'start_day': start_idx,
                    'end_day': i - 1,
                    'duration': i - start_idx,
                    'max_drawdown': float(np.min(drawdown[start_idx:i]))
                })
        
        return drawdown_periods
    
    def _analyze_trades(self, trades: List[Any]) -> Dict[str, Any]:
        """Analyze trade statistics"""
        if not trades:
            return {'total_trades': 0}
        
        return {
            'total_trades': len(trades),
            'avg_trade_size': np.mean([getattr(t, 'quantity', 0) for t in trades]),
            'avg_trade_price': np.mean([getattr(t, 'price', 0) for t in trades])
        }
    
    def _analyze_risk(self, daily_returns: List[float]) -> Dict[str, float]:
        """Analyze risk metrics"""
        if not daily_returns:
            return {}
        
        returns_array = np.array(daily_returns)
        
        return {
            'skewness': float(self._calculate_skewness(returns_array)),
            'kurtosis': float(self._calculate_kurtosis(returns_array)),
            'var_99': float(np.percentile(returns_array, 1)),
            'cvar_95': float(np.mean(returns_array[returns_array <= np.percentile(returns_array, 5)]))
        }
    
    def _calculate_statistics(self, daily_returns: List[float], equity_curve: List[float]) -> Dict[str, float]:
        """Calculate additional statistics"""
        if not daily_returns or not equity_curve:
            return {}
        
        returns_array = np.array(daily_returns)
        
        return {
            'best_day': float(np.max(returns_array)),
            'worst_day': float(np.min(returns_array)),
            'positive_days': int(np.sum(returns_array > 0)),
            'negative_days': int(np.sum(returns_array < 0)),
            'avg_positive_return': float(np.mean(returns_array[returns_array > 0])) if np.any(returns_array > 0) else 0.0,
            'avg_negative_return': float(np.mean(returns_array[returns_array < 0])) if np.any(returns_array < 0) else 0.0
        }
    
    def _calculate_rolling_sharpe(self, returns: List[float], window: int = 30) -> List[float]:
        """Calculate rolling Sharpe ratio"""
        returns_array = np.array(returns)
        rolling_sharpe = []
        
        for i in range(window, len(returns_array)):
            window_returns = returns_array[i-window:i]
            excess_returns = window_returns - (self.risk_free_rate / 252)
            
            if np.std(excess_returns) != 0:
                sharpe = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
            else:
                sharpe = 0.0
            
            rolling_sharpe.append(sharpe)
        
        return rolling_sharpe
    
    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """Calculate skewness"""
        if len(returns) < 3:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        return np.mean(((returns - mean_return) / std_return) ** 3)
    
    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """Calculate excess kurtosis"""
        if len(returns) < 4:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        return np.mean(((returns - mean_return) / std_return) ** 4) - 3
    
    def _zero_metrics(self) -> PerformanceMetrics:
        """Return zero-initialized performance metrics"""
        return PerformanceMetrics(
            total_return=0.0,
            annualized_return=0.0,
            cumulative_return=0.0,
            volatility=0.0,
            max_drawdown=0.0,
            max_drawdown_duration=0,
            var_95=0.0,
            sharpe_ratio=0.0,
            sortino_ratio=0.0,
            calmar_ratio=0.0,
            total_trades=0,
            win_rate=0.0,
            profit_factor=0.0,
            avg_win=0.0,
            avg_loss=0.0
        ) 