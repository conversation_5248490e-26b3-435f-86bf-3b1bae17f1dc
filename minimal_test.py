#!/usr/bin/env python3
"""
最小化测试 - 验证核心功能
"""

import numpy as np
import torch
from modern_rl_trading import generate_stock_data, StockTradingEnv, SAC, set_seed

def minimal_test():
    """最小化测试"""
    print("=== 最小化功能测试 ===")
    
    # 设置随机种子
    set_seed(42)
    
    # 1. 测试数据生成
    print("1. 测试数据生成...")
    df = generate_stock_data(length=100)
    print(f"   数据生成成功: {df.shape}")
    
    # 2. 测试环境
    print("2. 测试交易环境...")
    env = StockTradingEnv(df)
    obs, info = env.reset()
    print(f"   环境重置成功，观察维度: {obs.shape}")
    
    # 3. 测试环境步进
    print("3. 测试环境步进...")
    action = env.action_space.sample()
    next_obs, reward, done, truncated, info = env.step(action)
    print(f"   步进成功，奖励: {reward:.4f}, 净值: ${info['net_worth']:.2f}")
    
    # 4. 测试智能体
    print("4. 测试SAC智能体...")
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    state_dim = env.observation_space.shape[0]
    action_dim = env.action_space.shape[0]
    agent = SAC(state_dim, action_dim, device)
    
    # 5. 测试动作选择
    print("5. 测试动作选择...")
    obs, _ = env.reset()
    action = agent.select_action(obs)
    print(f"   智能体动作: {action}")
    
    # 6. 简单训练循环
    print("6. 简单训练测试 (5轮)...")
    from modern_rl_trading import ReplayBuffer
    
    replay_buffer = ReplayBuffer(1000)
    
    for episode in range(5):
        obs, _ = env.reset()
        episode_reward = 0
        done = False
        steps = 0
        
        while not done and steps < 50:  # 限制步数
            action = agent.select_action(obs)
            next_obs, reward, done, truncated, info = env.step(action)
            done = done or truncated
            
            replay_buffer.push(obs, action, reward, next_obs, done)
            
            if len(replay_buffer) > 32:
                agent.update(replay_buffer, 32)
            
            obs = next_obs
            episode_reward += reward
            steps += 1
        
        print(f"   轮次 {episode + 1}: 奖励={episode_reward:.4f}, "
              f"净值=${info['net_worth']:.2f}, 步数={steps}")
    
    # 7. 测试模型保存和加载
    print("7. 测试模型保存和加载...")
    import os
    os.makedirs('./models', exist_ok=True)
    
    agent.save('./models/test_model.pth')
    print("   模型保存成功")
    
    # 创建新智能体并加载模型
    new_agent = SAC(state_dim, action_dim, device)
    new_agent.load('./models/test_model.pth')
    print("   模型加载成功")
    
    # 验证加载的模型
    obs, _ = env.reset()
    action1 = agent.select_action(obs, evaluate=True)
    action2 = new_agent.select_action(obs, evaluate=True)
    print(f"   原模型动作: {action1}")
    print(f"   加载模型动作: {action2}")
    print(f"   动作差异: {np.abs(action1 - action2).max():.6f}")
    
    print("\n✅ 所有测试通过！系统核心功能正常。")
    return True

if __name__ == "__main__":
    try:
        minimal_test()
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()