"""
实际交易执行代理
使用puppet库进行真实交易的代理类
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from agents.base_agent import BaseAgent
from core.utils import PUPPET_AVAILABLE


class TradingAgent(BaseAgent):
    """
    @class TradingAgent
    @brief 实际交易执行代理类
    @details 使用puppet库进行真实交易
    """
    
    def __init__(self, name: str = "TradingAgent", title: str = ""):
        super().__init__(name, "trading_execution")
        
        # 初始化puppet账户（如果可用）
        if PUPPET_AVAILABLE:
            import puppet
            self.account = puppet.Account(title=title)
            self.puppet_enabled = True
        else:
            self.account = None
            self.puppet_enabled = False
            self.logger.warning("Puppet library not available, trading will be simulated")
        
        self.transaction_history = []
        self.all_money_list = []
        
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输入数据并执行交易"""
        action = data.get('action')
        fund_code = data.get('fund_code')
        price = data.get('price')
        quantity = data.get('quantity', '20000')
        
        self.logger.info(f"Processing trade: {action} {fund_code} at {price}")
        
        if action == 'buy':
            result = self.execute_buy(fund_code, price, quantity)
        elif action == 'sell':
            result = self.execute_sell(fund_code, price, quantity)
        else:
            result = {
                'success': False,
                'message': f"Unknown action: {action}",
                'timestamp': datetime.now().isoformat()
            }
            
        # 记录交易历史
        transaction_record = {
            'action': action,
            'fund_code': fund_code,
            'price': price,
            'quantity': quantity,
            'result': result,
            'timestamp': datetime.now().isoformat()
        }
        self.transaction_history.append(transaction_record)
        
        return result
    
    def execute_buy(self, fund_code: str, price: float, quantity: str) -> Dict[str, Any]:
        """执行买入操作"""
        try:
            adjusted_price = round(price/10, 3) if price > 9 else round(price, 3)
            
            if self.puppet_enabled and self.account:
                # 真实交易
                self.account.buy(fund_code, adjusted_price, quantity)
                message = f"Real buy executed: {fund_code} at {adjusted_price}"
            else:
                # 模拟交易
                message = f"Simulated buy: {fund_code} at {adjusted_price}"
                self.logger.info(f"Simulated buy: {fund_code} at {adjusted_price} quantity {quantity}")

            return {
                'success': True,
                'message': message,
                'fund_code': fund_code,
                'price': adjusted_price,
                'quantity': quantity,
                'execution_type': 'real' if self.puppet_enabled else 'simulated',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Buy error for {fund_code}: {str(e)}")
            return {
                'success': False,
                'message': str(e),
                'fund_code': fund_code,
                'execution_type': 'real' if self.puppet_enabled else 'simulated',
                'timestamp': datetime.now().isoformat()
            }
    
    def execute_sell(self, fund_code: str, price: float, quantity: str) -> Dict[str, Any]:
        """执行卖出操作"""
        try:
            adjusted_price = round(price/10, 3) if price > 9 else round(price, 3)
            
            if self.puppet_enabled and self.account:
                # 真实交易
                self.account.sell(fund_code, adjusted_price, quantity)
                message = f"Real sell executed: {fund_code} at {adjusted_price}"
            else:
                # 模拟交易
                message = f"Simulated sell: {fund_code} at {adjusted_price}"
                self.logger.info(f"Simulated sell: {fund_code} at {adjusted_price} quantity {quantity}")

            return {
                'success': True,
                'message': message,
                'fund_code': fund_code,
                'price': adjusted_price,
                'quantity': quantity,
                'execution_type': 'real' if self.puppet_enabled else 'simulated',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Sell error for {fund_code}: {str(e)}")
            return {
                'success': False,
                'message': str(e),
                'fund_code': fund_code,
                'execution_type': 'real' if self.puppet_enabled else 'simulated',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            if not self.puppet_enabled or not self.account:
                return {
                    'error': 'Puppet library not available',
                    'execution_type': 'simulated',
                    'timestamp': datetime.now().isoformat()
                }
            
            position = self.account.query('position')
            summary = self.account.query('summary')

            position_list = [x[2:-1] for x in position['symbol']]
            all_sum = sum(position['amount'])
            all_money = summary['equity']
            position_percentage = summary['position_pct']

            self.all_money_list.append(all_money)

            return {
                'position': position,
                'position_list': position_list,
                'all_sum': all_sum,
                'all_money': all_money,
                'position_percentage': position_percentage,
                'execution_type': 'real',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting account info: {str(e)}")
            return {
                'error': str(e),
                'execution_type': 'real' if self.puppet_enabled else 'simulated',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_transaction_history(self) -> List[Dict[str, Any]]:
        """获取交易历史"""
        return self.transaction_history.copy()
    
    def get_money_history(self) -> List[float]:
        """获取资金历史"""
        return self.all_money_list.copy()
    
    def is_puppet_available(self) -> bool:
        """检查puppet是否可用"""
        return self.puppet_enabled
