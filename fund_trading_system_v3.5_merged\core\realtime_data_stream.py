"""
Real-time Data Stream Processing System

Provides real-time market data streaming, processing, and distribution
capabilities for the trading system.
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Any, Optional, Set
from collections import deque
from dataclasses import dataclass
import pandas as pd
import numpy as np
from concurrent.futures import Thread<PERSON>oolExecutor


@dataclass
class StreamData:
    """Real-time stream data structure"""
    symbol: str
    timestamp: datetime
    data: Dict[str, Any]
    data_type: str  # 'price', 'volume', 'news', 'technical'
    source: str
    quality_score: float = 1.0


class DataStreamSubscriber:
    """Base class for data stream subscribers"""
    
    def __init__(self, subscriber_id: str):
        self.subscriber_id = subscriber_id
        self.is_active = True
        self.last_update = None
    
    async def on_data_received(self, data: StreamData) -> None:
        """Handle received data"""
        raise NotImplementedError
    
    def get_subscription_filters(self) -> Dict[str, Any]:
        """Get subscription filters"""
        return {}


class RealTimeDataStream:
    """
    Real-time data stream manager
    
    Features:
    - Multi-source data aggregation
    - Real-time data processing and filtering
    - Subscriber pattern for data distribution
    - Data quality monitoring
    - Buffering and replay capabilities
    - Performance monitoring
    """
    
    def __init__(self, 
                 buffer_size: int = 10000,
                 processing_interval: float = 0.1,
                 quality_threshold: float = 0.8):
        """
        Initialize real-time data stream
        
        Args:
            buffer_size: Maximum buffer size for historical data
            processing_interval: Data processing interval in seconds
            quality_threshold: Minimum quality score for data acceptance
        """
        self.buffer_size = buffer_size
        self.processing_interval = processing_interval
        self.quality_threshold = quality_threshold
        
        # Data storage
        self.data_buffer = deque(maxlen=buffer_size)
        self.latest_data = {}  # symbol -> latest StreamData
        
        # Subscribers
        self.subscribers: Dict[str, DataStreamSubscriber] = {}
        self.subscription_filters: Dict[str, Dict[str, Any]] = {}
        
        # Data sources
        self.data_sources: Dict[str, Callable] = {}
        self.source_status: Dict[str, bool] = {}
        
        # Processing
        self.is_running = False
        self.processing_thread = None
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Statistics
        self.stats = {
            'total_messages': 0,
            'messages_per_second': 0,
            'quality_rejections': 0,
            'processing_errors': 0,
            'active_subscribers': 0,
            'buffer_usage': 0,
            'last_update': None
        }
        
        # Logger
        self.logger = logging.getLogger("RealTimeDataStream")
        
        # Data quality monitor
        self.quality_monitor = DataQualityMonitor()
    
    def add_data_source(self, source_name: str, source_func: Callable) -> None:
        """
        Add data source
        
        Args:
            source_name: Name of the data source
            source_func: Function that returns data
        """
        self.data_sources[source_name] = source_func
        self.source_status[source_name] = True
        self.logger.info(f"Added data source: {source_name}")
    
    def subscribe(self, subscriber: DataStreamSubscriber, 
                  filters: Optional[Dict[str, Any]] = None) -> None:
        """
        Subscribe to data stream
        
        Args:
            subscriber: Subscriber instance
            filters: Optional filters for data
        """
        self.subscribers[subscriber.subscriber_id] = subscriber
        if filters:
            self.subscription_filters[subscriber.subscriber_id] = filters
        
        self.stats['active_subscribers'] = len(self.subscribers)
        self.logger.info(f"Added subscriber: {subscriber.subscriber_id}")
    
    def unsubscribe(self, subscriber_id: str) -> None:
        """Unsubscribe from data stream"""
        self.subscribers.pop(subscriber_id, None)
        self.subscription_filters.pop(subscriber_id, None)
        self.stats['active_subscribers'] = len(self.subscribers)
        self.logger.info(f"Removed subscriber: {subscriber_id}")
    
    def start_stream(self) -> None:
        """Start the real-time data stream"""
        if self.is_running:
            self.logger.warning("Data stream is already running")
            return
        
        self.is_running = True
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.processing_thread.start()
        
        self.logger.info("Real-time data stream started")
    
    def stop_stream(self) -> None:
        """Stop the real-time data stream"""
        self.is_running = False
        if self.processing_thread:
            self.processing_thread.join(timeout=5)
        
        self.executor.shutdown(wait=True)
        self.logger.info("Real-time data stream stopped")
    
    def push_data(self, data: StreamData) -> None:
        """
        Push data to the stream
        
        Args:
            data: Stream data to push
        """
        # Quality check
        if data.quality_score < self.quality_threshold:
            self.stats['quality_rejections'] += 1
            self.logger.debug(f"Rejected low quality data: {data.quality_score}")
            return
        
        # Add to buffer
        self.data_buffer.append(data)
        self.latest_data[data.symbol] = data
        
        # Update statistics
        self.stats['total_messages'] += 1
        self.stats['buffer_usage'] = len(self.data_buffer) / self.buffer_size
        self.stats['last_update'] = datetime.now()
        
        # Process data asynchronously
        self.executor.submit(self._process_data, data)
    
    def get_latest_data(self, symbol: str) -> Optional[StreamData]:
        """Get latest data for symbol"""
        return self.latest_data.get(symbol)
    
    def get_historical_data(self, symbol: str, 
                           lookback_minutes: int = 60) -> List[StreamData]:
        """
        Get historical data from buffer
        
        Args:
            symbol: Symbol to get data for
            lookback_minutes: How many minutes to look back
            
        Returns:
            List of historical StreamData
        """
        cutoff_time = datetime.now() - timedelta(minutes=lookback_minutes)
        
        return [
            data for data in self.data_buffer
            if data.symbol == symbol and data.timestamp >= cutoff_time
        ]
    
    def get_stream_stats(self) -> Dict[str, Any]:
        """Get stream statistics"""
        # Calculate messages per second
        if len(self.data_buffer) > 1:
            time_span = (self.data_buffer[-1].timestamp - self.data_buffer[0].timestamp).total_seconds()
            if time_span > 0:
                self.stats['messages_per_second'] = len(self.data_buffer) / time_span
        
        return {
            **self.stats,
            'buffer_size': len(self.data_buffer),
            'unique_symbols': len(self.latest_data),
            'data_sources': len(self.data_sources),
            'active_sources': sum(self.source_status.values())
        }
    
    def _processing_loop(self) -> None:
        """Main processing loop"""
        while self.is_running:
            try:
                # Collect data from all sources
                for source_name, source_func in self.data_sources.items():
                    if not self.source_status[source_name]:
                        continue
                    
                    try:
                        # Get data from source
                        source_data = source_func()
                        if source_data:
                            # Convert to StreamData if needed
                            if not isinstance(source_data, list):
                                source_data = [source_data]
                            
                            for data in source_data:
                                if isinstance(data, StreamData):
                                    self.push_data(data)
                                else:
                                    # Convert dict to StreamData
                                    stream_data = self._convert_to_stream_data(data, source_name)
                                    if stream_data:
                                        self.push_data(stream_data)
                    
                    except Exception as e:
                        self.logger.error(f"Error processing source {source_name}: {e}")
                        self.source_status[source_name] = False
                        self.stats['processing_errors'] += 1
                
                time.sleep(self.processing_interval)
                
            except Exception as e:
                self.logger.error(f"Processing loop error: {e}")
                time.sleep(1)
    
    def _process_data(self, data: StreamData) -> None:
        """Process individual data point"""
        try:
            # Quality monitoring
            self.quality_monitor.check_data_quality(data)
            
            # Distribute to subscribers
            for subscriber_id, subscriber in self.subscribers.items():
                if not subscriber.is_active:
                    continue
                
                # Check filters
                if self._passes_filters(data, subscriber_id):
                    try:
                        # Use asyncio for async subscribers
                        if asyncio.iscoroutinefunction(subscriber.on_data_received):
                            asyncio.create_task(subscriber.on_data_received(data))
                        else:
                            subscriber.on_data_received(data)
                    except Exception as e:
                        self.logger.error(f"Error notifying subscriber {subscriber_id}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error processing data: {e}")
            self.stats['processing_errors'] += 1
    
    def _passes_filters(self, data: StreamData, subscriber_id: str) -> bool:
        """Check if data passes subscriber filters"""
        filters = self.subscription_filters.get(subscriber_id, {})
        
        if not filters:
            return True
        
        # Symbol filter
        if 'symbols' in filters and data.symbol not in filters['symbols']:
            return False
        
        # Data type filter
        if 'data_types' in filters and data.data_type not in filters['data_types']:
            return False
        
        # Quality filter
        if 'min_quality' in filters and data.quality_score < filters['min_quality']:
            return False
        
        return True
    
    def _convert_to_stream_data(self, data: Dict[str, Any], source: str) -> Optional[StreamData]:
        """Convert dictionary data to StreamData"""
        try:
            return StreamData(
                symbol=data.get('symbol', 'UNKNOWN'),
                timestamp=data.get('timestamp', datetime.now()),
                data=data,
                data_type=data.get('type', 'unknown'),
                source=source,
                quality_score=data.get('quality', 1.0)
            )
        except Exception as e:
            self.logger.error(f"Error converting data to StreamData: {e}")
            return None


class DataQualityMonitor:
    """Monitor data quality in real-time"""
    
    def __init__(self):
        self.quality_stats = {}
        self.anomaly_threshold = 3.0  # Standard deviations
        self.logger = logging.getLogger("DataQualityMonitor")
    
    def check_data_quality(self, data: StreamData) -> float:
        """
        Check data quality and return quality score
        
        Args:
            data: StreamData to check
            
        Returns:
            Quality score (0.0 to 1.0)
        """
        quality_score = 1.0
        
        try:
            # Check timestamp freshness
            age_seconds = (datetime.now() - data.timestamp).total_seconds()
            if age_seconds > 300:  # 5 minutes old
                quality_score *= 0.8
            
            # Check data completeness
            if not data.data or len(data.data) == 0:
                quality_score *= 0.5
            
            # Check for required fields based on data type
            required_fields = self._get_required_fields(data.data_type)
            missing_fields = [field for field in required_fields if field not in data.data]
            if missing_fields:
                quality_score *= (1.0 - len(missing_fields) / len(required_fields) * 0.5)
            
            # Check for anomalies in numeric data
            numeric_quality = self._check_numeric_anomalies(data)
            quality_score *= numeric_quality
            
            # Update quality statistics
            self._update_quality_stats(data.symbol, quality_score)
            
        except Exception as e:
            self.logger.error(f"Error checking data quality: {e}")
            quality_score = 0.5
        
        return quality_score
    
    def _get_required_fields(self, data_type: str) -> List[str]:
        """Get required fields for data type"""
        field_map = {
            'price': ['open', 'high', 'low', 'close', 'volume'],
            'volume': ['volume'],
            'news': ['title', 'content'],
            'technical': ['indicator_name', 'value']
        }
        return field_map.get(data_type, [])
    
    def _check_numeric_anomalies(self, data: StreamData) -> float:
        """Check for numeric anomalies"""
        try:
            numeric_fields = ['open', 'high', 'low', 'close', 'volume']
            quality_score = 1.0
            
            for field in numeric_fields:
                if field in data.data:
                    value = data.data[field]
                    if isinstance(value, (int, float)):
                        # Check for reasonable ranges
                        if field in ['open', 'high', 'low', 'close']:
                            if value <= 0 or value > 10000:  # Reasonable price range
                                quality_score *= 0.7
                        elif field == 'volume':
                            if value < 0:
                                quality_score *= 0.5
            
            return quality_score
            
        except Exception:
            return 0.8
    
    def _update_quality_stats(self, symbol: str, quality_score: float) -> None:
        """Update quality statistics for symbol"""
        if symbol not in self.quality_stats:
            self.quality_stats[symbol] = {
                'scores': deque(maxlen=100),
                'avg_quality': 0.0,
                'min_quality': 1.0,
                'max_quality': 0.0
            }
        
        stats = self.quality_stats[symbol]
        stats['scores'].append(quality_score)
        stats['avg_quality'] = np.mean(stats['scores'])
        stats['min_quality'] = min(stats['min_quality'], quality_score)
        stats['max_quality'] = max(stats['max_quality'], quality_score)
    
    def get_quality_report(self) -> Dict[str, Any]:
        """Get quality monitoring report"""
        return {
            symbol: {
                'avg_quality': stats['avg_quality'],
                'min_quality': stats['min_quality'],
                'max_quality': stats['max_quality'],
                'sample_count': len(stats['scores'])
            }
            for symbol, stats in self.quality_stats.items()
        }


# Example usage and testing
if __name__ == "__main__":
    # Example subscriber
    class TestSubscriber(DataStreamSubscriber):
        def __init__(self, subscriber_id: str):
            super().__init__(subscriber_id)
            self.received_count = 0
        
        async def on_data_received(self, data: StreamData) -> None:
            self.received_count += 1
            print(f"Subscriber {self.subscriber_id} received data for {data.symbol}")
    
    # Example data source
    def sample_data_source():
        return StreamData(
            symbol="TEST001",
            timestamp=datetime.now(),
            data={'close': 100.0, 'volume': 1000},
            data_type='price',
            source='test'
        )
    
    # Test the stream
    stream = RealTimeDataStream()
    stream.add_data_source("test_source", sample_data_source)
    
    subscriber = TestSubscriber("test_sub")
    stream.subscribe(subscriber)
    
    stream.start_stream()
    time.sleep(2)
    stream.stop_stream()
    
    print(f"Stream stats: {stream.get_stream_stats()}")
    print(f"Subscriber received: {subscriber.received_count} messages")
